{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "IBCKtSr50mVr5qyjqc84IbmgDrtjKtpeN9PFStgL+jE=", "__NEXT_PREVIEW_MODE_ID": "0a3dbab6db9c535a8ccaa9596ca03bd3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "af102c868cb5c649fe2bb50c2f65e40b94ab2f86328ffd0b2ee5c229a6e6dc04", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4209060daae4e5eb0834edc1fe1a0b8695561bbea3c697d36376c7623fcde7e8"}}}, "instrumentation": null, "functions": {}}