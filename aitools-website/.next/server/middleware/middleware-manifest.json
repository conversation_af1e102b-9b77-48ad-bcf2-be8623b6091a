{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_2c4b2d10._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_1f9ae7ce.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "N7q2b64Zt1JT/i+kp0zKQtSwAeXEad1q7osWZzH2+Lo=", "__NEXT_PREVIEW_MODE_ID": "8efab6998a2688d6c38fda0dec9a11d4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fd114681cd64b7b596fc88b5aee70c6e5aaeb8ad4739edba0f26cc4dc548552d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d44d6ea5907acc50e9c4aa6b0fbe2d3bf3c5898421c1b8cd4e9e187650da1683"}}}, "instrumentation": null, "functions": {}}