module.exports = {

"[project]/.next-internal/server/app/api/tools/[id]/launch-date/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function dbConnect() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = dbConnect;
}}),
"[project]/src/i18n/config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 支持的语言列表
__turbopack_context__.s({
    "defaultLocale": (()=>defaultLocale),
    "isValidLocale": (()=>isValidLocale),
    "localeNames": (()=>localeNames),
    "locales": (()=>locales)
});
const locales = [
    'en',
    'zh'
];
const defaultLocale = 'en';
const localeNames = {
    zh: '中文',
    en: 'English'
};
function isValidLocale(locale) {
    return locales.includes(locale);
}
}}),
"[project]/src/i18n/request.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js [app-route] (ecmascript) <export default as getRequestConfig>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/config.ts [app-route] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__["getRequestConfig"])(async ({ locale })=>{
    // Validate that the incoming `locale` parameter is valid
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["locales"].includes(locale)) {
        locale = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["defaultLocale"];
    }
    return {
        locale,
        messages: (await __turbopack_context__.f({
            "./messages/en.json": {
                id: ()=>"[project]/src/i18n/messages/en.json (json, async loader)",
                module: ()=>__turbopack_context__.r("[project]/src/i18n/messages/en.json (json, async loader)")(__turbopack_context__.i)
            },
            "./messages/zh.json": {
                id: ()=>"[project]/src/i18n/messages/zh.json (json, async loader)",
                module: ()=>__turbopack_context__.r("[project]/src/i18n/messages/zh.json (json, async loader)")(__turbopack_context__.i)
            }
        }).import(`./messages/${locale}.json`)).default
    };
});
}}),
"[project]/src/constants/categories-i18n.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 国际化分类配置文件
// 支持多语言的AI工具分类配置
__turbopack_context__.s({
    "CATEGORY_BASE_CONFIGS": (()=>CATEGORY_BASE_CONFIGS),
    "CATEGORY_BASE_METADATA": (()=>CATEGORY_BASE_METADATA),
    "CATEGORY_SLUGS": (()=>CATEGORY_SLUGS),
    "getCategoryConfig": (()=>getCategoryConfig),
    "getCategoryConfigs": (()=>getCategoryConfigs),
    "getCategoryDescription": (()=>getCategoryDescription),
    "getCategoryName": (()=>getCategoryName),
    "getCategoryOptions": (()=>getCategoryOptions),
    "getCategoryOptionsWithAll": (()=>getCategoryOptionsWithAll),
    "isValidCategory": (()=>isValidCategory),
    "useCategoryConfigs": (()=>useCategoryConfigs),
    "useCategoryDescription": (()=>useCategoryDescription),
    "useCategoryName": (()=>useCategoryName),
    "useCategoryOptions": (()=>useCategoryOptions),
    "useCategoryOptionsWithAll": (()=>useCategoryOptionsWithAll)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-server/useTranslations.js [app-route] (ecmascript) <export default as useTranslations>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js [app-route] (ecmascript) <export default as getTranslations>");
;
;
const CATEGORY_BASE_CONFIGS = [
    {
        slug: 'text-generation',
        icon: '📝',
        color: '#3B82F6'
    },
    {
        slug: 'image-generation',
        icon: '🎨',
        color: '#10B981'
    },
    {
        slug: 'code-generation',
        icon: '💻',
        color: '#8B5CF6'
    },
    {
        slug: 'data-analysis',
        icon: '📊',
        color: '#F59E0B'
    },
    {
        slug: 'audio-processing',
        icon: '🎵',
        color: '#EF4444'
    },
    {
        slug: 'video-editing',
        icon: '🎬',
        color: '#06B6D4'
    },
    {
        slug: 'translation',
        icon: '🌐',
        color: '#84CC16'
    },
    {
        slug: 'search-engines',
        icon: '🔍',
        color: '#F97316'
    },
    {
        slug: 'education',
        icon: '📚',
        color: '#A855F7'
    },
    {
        slug: 'marketing',
        icon: '📈',
        color: '#EC4899'
    },
    {
        slug: 'productivity',
        icon: '⚡',
        color: '#14B8A6'
    },
    {
        slug: 'customer-service',
        icon: '🎧',
        color: '#F59E0B'
    }
];
function useCategoryConfigs() {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    return CATEGORY_BASE_CONFIGS.map((config)=>({
            slug: config.slug,
            name: t(`category_names.${config.slug}`),
            description: t(`category_descriptions.${config.slug}`),
            icon: config.icon,
            color: config.color
        }));
}
function useCategoryOptions() {
    const configs = useCategoryConfigs();
    return configs.map((config)=>({
            value: config.slug,
            label: config.name
        }));
}
function useCategoryOptionsWithAll() {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    const options = useCategoryOptions();
    return [
        {
            value: '',
            label: t('all_categories')
        },
        ...options
    ];
}
function useCategoryName(slug) {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    return t(`category_names.${slug}`) || slug;
}
function useCategoryDescription(slug) {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    return t(`category_descriptions.${slug}`) || '';
}
async function getCategoryConfigs(locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    return CATEGORY_BASE_CONFIGS.map((config)=>({
            slug: config.slug,
            name: t(`category_names.${config.slug}`),
            description: t(`category_descriptions.${config.slug}`),
            icon: config.icon,
            color: config.color
        }));
}
async function getCategoryOptions(locale) {
    const configs = await getCategoryConfigs(locale);
    return configs.map((config)=>({
            value: config.slug,
            label: config.name
        }));
}
async function getCategoryOptionsWithAll(locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    const options = await getCategoryOptions(locale);
    return [
        {
            value: '',
            label: t('all_categories')
        },
        ...options
    ];
}
async function getCategoryName(slug, locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    return t(`category_names.${slug}`) || slug;
}
async function getCategoryDescription(slug, locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    return t(`category_descriptions.${slug}`) || '';
}
async function getCategoryConfig(slug, locale) {
    const configs = await getCategoryConfigs(locale);
    return configs.find((config)=>config.slug === slug);
}
function isValidCategory(slug) {
    return CATEGORY_BASE_CONFIGS.some((config)=>config.slug === slug);
}
const CATEGORY_SLUGS = CATEGORY_BASE_CONFIGS.map((config)=>config.slug);
const CATEGORY_BASE_METADATA = CATEGORY_BASE_CONFIGS.reduce((acc, config)=>{
    acc[config.slug] = config;
    return acc;
}, {});
}}),
"[project]/src/models/Tool.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$categories$2d$i18n$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/categories-i18n.ts [app-route] (ecmascript)");
;
;
const ToolSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    name: {
        type: String,
        required: [
            true,
            'Tool name is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Tool name cannot exceed 100 characters'
        ]
    },
    tagline: {
        type: String,
        trim: true,
        maxlength: [
            200,
            'Tagline cannot exceed 200 characters'
        ]
    },
    description: {
        type: String,
        required: [
            true,
            'Tool description is required'
        ],
        trim: true,
        maxlength: [
            500,
            'Description cannot exceed 500 characters'
        ]
    },
    longDescription: {
        type: String,
        trim: true,
        maxlength: [
            2000,
            'Long description cannot exceed 2000 characters'
        ]
    },
    website: {
        type: String,
        required: [
            true,
            'Website URL is required'
        ],
        trim: true,
        validate: {
            validator: function(v) {
                return /^https?:\/\/.+/.test(v);
            },
            message: 'Please enter a valid URL'
        }
    },
    logo: {
        type: String,
        trim: true
    },
    category: {
        type: String,
        required: [
            true,
            'Category is required'
        ],
        enum: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$categories$2d$i18n$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CATEGORY_SLUGS"]
    },
    tags: [
        {
            type: String,
            trim: true,
            lowercase: true
        }
    ],
    pricing: {
        type: String,
        required: [
            true,
            'Pricing model is required'
        ],
        enum: [
            'free',
            'freemium',
            'paid'
        ]
    },
    pricingDetails: {
        type: String,
        trim: true,
        maxlength: [
            500,
            'Pricing details cannot exceed 500 characters'
        ]
    },
    screenshots: [
        {
            type: String,
            trim: true
        }
    ],
    submittedBy: {
        type: String,
        required: [
            true,
            'Submitter ID is required'
        ],
        trim: true
    },
    submittedAt: {
        type: Date,
        default: Date.now
    },
    launchDate: {
        type: Date
    },
    status: {
        type: String,
        required: true,
        enum: [
            'draft',
            'pending',
            'approved',
            'rejected'
        ],
        default: 'draft'
    },
    reviewNotes: {
        type: String,
        trim: true,
        maxlength: [
            1000,
            'Review notes cannot exceed 1000 characters'
        ]
    },
    reviewedBy: {
        type: String,
        trim: true
    },
    reviewedAt: {
        type: Date
    },
    // 发布日期选择相关
    launchDateSelected: {
        type: Boolean,
        default: false
    },
    selectedLaunchDate: {
        type: Date
    },
    launchOption: {
        type: String,
        enum: [
            'free',
            'paid'
        ]
    },
    // 付费相关
    paymentRequired: {
        type: Boolean,
        default: false
    },
    paymentAmount: {
        type: Number,
        min: 0
    },
    paymentStatus: {
        type: String,
        enum: [
            'pending',
            'completed',
            'failed',
            'refunded'
        ]
    },
    orderId: {
        type: String,
        trim: true
    },
    paymentMethod: {
        type: String,
        trim: true
    },
    paidAt: {
        type: Date
    },
    views: {
        type: Number,
        default: 0,
        min: 0
    },
    likes: {
        type: Number,
        default: 0,
        min: 0
    },
    likedBy: [
        {
            type: String,
            trim: true
        }
    ],
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes for better query performance
ToolSchema.index({
    status: 1,
    isActive: 1
});
ToolSchema.index({
    category: 1,
    status: 1
});
ToolSchema.index({
    tags: 1,
    status: 1
});
ToolSchema.index({
    submittedBy: 1
});
ToolSchema.index({
    launchDate: -1
});
ToolSchema.index({
    views: -1
});
ToolSchema.index({
    likes: -1
});
// Text search index
ToolSchema.index({
    name: 'text',
    tagline: 'text',
    description: 'text',
    longDescription: 'text',
    tags: 'text'
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Tool || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Tool', ToolSchema);
}}),
"[project]/src/models/Order.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const OrderSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    userId: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'User',
        required: [
            true,
            'User ID is required'
        ]
    },
    toolId: {
        type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
        ref: 'Tool',
        required: [
            true,
            'Tool ID is required'
        ]
    },
    type: {
        type: String,
        required: true,
        enum: [
            'launch_date_priority'
        ],
        default: 'launch_date_priority'
    },
    amount: {
        type: Number,
        required: [
            true,
            'Amount is required'
        ],
        min: [
            0,
            'Amount must be positive'
        ]
    },
    currency: {
        type: String,
        required: true,
        default: 'CNY',
        enum: [
            'CNY',
            'USD'
        ]
    },
    status: {
        type: String,
        required: true,
        enum: [
            'pending',
            'completed',
            'failed',
            'cancelled',
            'refunded'
        ],
        default: 'pending'
    },
    // 支付相关
    paymentMethod: {
        type: String,
        trim: true
    },
    paymentIntentId: {
        type: String,
        trim: true
    },
    paymentSessionId: {
        type: String,
        trim: true
    },
    stripePaymentIntentId: {
        type: String,
        trim: true
    },
    stripeCustomerId: {
        type: String,
        trim: true
    },
    stripePaymentDetails: {
        paymentIntentId: String,
        amount: Number,
        currency: String,
        status: String,
        created: Date,
        failureReason: String
    },
    // 订单详情
    description: {
        type: String,
        required: [
            true,
            'Description is required'
        ],
        trim: true,
        maxlength: [
            500,
            'Description cannot exceed 500 characters'
        ]
    },
    selectedLaunchDate: {
        type: Date,
        required: [
            true,
            'Selected launch date is required'
        ]
    },
    // 时间戳
    paidAt: {
        type: Date
    },
    cancelledAt: {
        type: Date
    },
    refundedAt: {
        type: Date
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// 索引
OrderSchema.index({
    userId: 1,
    createdAt: -1
});
OrderSchema.index({
    toolId: 1
});
OrderSchema.index({
    status: 1
});
OrderSchema.index({
    paymentIntentId: 1
});
OrderSchema.index({
    paymentSessionId: 1
});
OrderSchema.index({
    stripePaymentIntentId: 1
});
OrderSchema.index({
    stripeCustomerId: 1
});
// 虚拟字段
OrderSchema.virtual('user', {
    ref: 'User',
    localField: 'userId',
    foreignField: '_id',
    justOne: true
});
OrderSchema.virtual('tool', {
    ref: 'Tool',
    localField: 'toolId',
    foreignField: '_id',
    justOne: true
});
// 实例方法
OrderSchema.methods.markAsPaid = function() {
    this.status = 'completed';
    this.paidAt = new Date();
    return this.save();
};
OrderSchema.methods.markAsFailed = function() {
    this.status = 'failed';
    return this.save();
};
OrderSchema.methods.cancel = function() {
    this.status = 'cancelled';
    this.cancelledAt = new Date();
    return this.save();
};
OrderSchema.methods.refund = function() {
    this.status = 'refunded';
    this.refundedAt = new Date();
    return this.save();
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Order || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Order', OrderSchema);
}}),
"[project]/src/models/User.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const AccountSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    provider: {
        type: String,
        required: true,
        enum: [
            'google',
            'github',
            'email'
        ]
    },
    providerId: {
        type: String,
        required: true
    },
    providerAccountId: {
        type: String,
        required: true
    },
    accessToken: String,
    refreshToken: String,
    expiresAt: Date
}, {
    _id: false
});
const UserSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    email: {
        type: String,
        required: [
            true,
            'Email is required'
        ],
        unique: true,
        trim: true,
        lowercase: true,
        validate: {
            validator: function(v) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
            },
            message: 'Please enter a valid email address'
        }
    },
    name: {
        type: String,
        required: [
            true,
            'Name is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Name cannot exceed 100 characters'
        ]
    },
    avatar: {
        type: String,
        trim: true
    },
    role: {
        type: String,
        required: true,
        enum: [
            'user',
            'admin'
        ],
        default: 'user'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    // 认证相关
    emailVerified: {
        type: Boolean,
        default: false
    },
    emailVerificationToken: {
        type: String,
        trim: true
    },
    emailVerificationExpires: {
        type: Date
    },
    // OAuth账户关联
    accounts: [
        AccountSchema
    ],
    // 用户行为
    submittedTools: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Tool'
        }
    ],
    likedTools: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Tool'
        }
    ],
    comments: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Comment'
        }
    ],
    // 时间戳
    lastLoginAt: {
        type: Date
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes
UserSchema.index({
    email: 1
});
UserSchema.index({
    role: 1
});
UserSchema.index({
    emailVerificationToken: 1
});
UserSchema.index({
    'accounts.provider': 1,
    'accounts.providerAccountId': 1
});
// 实例方法
UserSchema.methods.addAccount = function(account) {
    // 检查是否已存在相同的账户
    const existingAccount = this.accounts.find((acc)=>acc.provider === account.provider && acc.providerAccountId === account.providerAccountId);
    if (!existingAccount) {
        this.accounts.push(account);
    } else {
        // 更新现有账户信息
        Object.assign(existingAccount, account);
    }
};
UserSchema.methods.removeAccount = function(provider, providerAccountId) {
    this.accounts = this.accounts.filter((acc)=>!(acc.provider === provider && acc.providerAccountId === providerAccountId));
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.User || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('User', UserSchema);
}}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/google.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/github.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-route] (ecmascript)");
;
;
;
;
;
const authOptions = {
    // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略
    // adapter: MongoDBAdapter(client),
    // 动态配置基础URL，支持不同环境
    // NextAuth会自动从环境变量或请求头中检测URL，但我们也可以显式设置
    // 在生产环境中，这将被环境变量覆盖
    ...("TURBOPACK compile-time value", "development") === 'development' && {
    },
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GITHUB_CLIENT_ID,
            clientSecret: process.env.GITHUB_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            id: 'email-code',
            name: 'Email Code',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                code: {
                    label: 'Code',
                    type: 'text'
                },
                token: {
                    label: 'Token',
                    type: 'text'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.code || !credentials?.token) {
                    return null;
                }
                try {
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
                    // 查找用户
                    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
                        email: credentials.email.toLowerCase(),
                        emailVerificationExpires: {
                            $gt: new Date()
                        }
                    });
                    if (!user) {
                        return null;
                    }
                    // 验证token和验证码
                    const storedData = user.emailVerificationToken;
                    if (!storedData || !storedData.includes(':')) {
                        return null;
                    }
                    const [storedToken, storedCode] = storedData.split(':');
                    if (storedToken !== credentials.token || storedCode !== credentials.code) {
                        return null;
                    }
                    // 验证成功，更新用户状态
                    user.emailVerified = true;
                    user.emailVerificationToken = undefined;
                    user.emailVerificationExpires = undefined;
                    user.lastLoginAt = new Date();
                    // 如果用户没有邮箱账户记录，添加一个
                    const hasEmailAccount = user.accounts.some((acc)=>acc.provider === 'email');
                    if (!hasEmailAccount) {
                        user.accounts.push({
                            provider: 'email',
                            providerId: 'email',
                            providerAccountId: user.email
                        });
                    }
                    await user.save();
                    return {
                        id: user._id.toString(),
                        email: user.email,
                        name: user.name,
                        image: user.avatar,
                        role: user.role
                    };
                } catch (error) {
                    console.error('Email code authorization error:', error);
                    return null;
                }
            }
        })
    ],
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        async signIn ({ user, account, profile }) {
            // 对于credentials provider，用户已经在authorize中处理过了
            if (account?.provider === 'email-code') {
                return true;
            }
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            try {
                // 查找或创建用户（仅用于OAuth providers）
                let existingUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
                    email: user.email
                });
                if (!existingUser) {
                    // 创建新用户
                    existingUser = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
                        email: user.email,
                        name: user.name || profile?.name || 'User',
                        avatar: user.image || profile?.image,
                        emailVerified: true,
                        lastLoginAt: new Date()
                    });
                    await existingUser.save();
                } else {
                    // 更新最后登录时间
                    existingUser.lastLoginAt = new Date();
                    await existingUser.save();
                }
                // 添加或更新账户信息
                if (account && account.provider !== 'email-code') {
                    existingUser.addAccount({
                        provider: account.provider,
                        providerId: account.provider,
                        providerAccountId: account.providerAccountId || account.id || '',
                        accessToken: account.access_token,
                        refreshToken: account.refresh_token,
                        expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined
                    });
                    await existingUser.save();
                }
                return true;
            } catch (error) {
                console.error('Sign in error:', error);
                return false;
            }
        },
        async jwt ({ token, user }) {
            if (user) {
                // 对于credentials provider，user对象已经包含了我们需要的信息
                token.userId = user.id;
                token.role = user.role || 'user';
            }
            return token;
        },
        async session ({ session, token }) {
            if (token && session.user) {
                session.user.id = token.userId;
                session.user.role = token.role;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        error: '/auth/error'
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[project]/src/constants/pricing.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * 统一的价格配置文件
 * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中
 */ // 基础价格配置
__turbopack_context__.s({
    "LAUNCH_OPTIONS": (()=>LAUNCH_OPTIONS),
    "PRICING_CONFIG": (()=>PRICING_CONFIG),
    "TOOL_PRICING_FORM_OPTIONS": (()=>TOOL_PRICING_FORM_OPTIONS),
    "TOOL_PRICING_OPTIONS": (()=>TOOL_PRICING_OPTIONS),
    "TOOL_PRICING_TYPES": (()=>TOOL_PRICING_TYPES),
    "formatPrice": (()=>formatPrice),
    "formatStripeAmount": (()=>formatStripeAmount),
    "getPricingConfig": (()=>getPricingConfig),
    "getToolPricingColor": (()=>getToolPricingColor),
    "getToolPricingText": (()=>getToolPricingText)
});
const PRICING_CONFIG = {
    // 优先发布服务价格
    PRIORITY_LAUNCH: {
        // 显示价格（元）
        displayPrice: 19.9,
        // Stripe价格（分为单位）
        stripeAmount: 1990,
        // 货币
        currency: 'USD',
        // Stripe货币代码（小写）
        stripeCurrency: 'usd',
        // 产品名称
        productName: 'AI工具优先发布服务',
        // 产品描述
        description: '让您的AI工具获得优先审核和推荐位置',
        // 功能特性
        features: [
            '可选择任意发布日期',
            '优先审核处理',
            '首页推荐位置',
            '专属客服支持'
        ]
    },
    // 免费发布配置
    FREE_LAUNCH: {
        displayPrice: 0,
        stripeAmount: 0,
        currency: 'USD',
        stripeCurrency: 'usd',
        productName: '免费发布服务',
        description: '选择一个月后的任意发布日期',
        features: [
            '免费提交审核',
            '发布日期：一个月后起',
            '正常审核流程',
            '标准展示位置'
        ]
    }
};
const LAUNCH_OPTIONS = [
    {
        id: 'free',
        title: '免费发布',
        description: PRICING_CONFIG.FREE_LAUNCH.description,
        price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,
        features: PRICING_CONFIG.FREE_LAUNCH.features
    },
    {
        id: 'paid',
        title: '优先发布',
        description: PRICING_CONFIG.PRIORITY_LAUNCH.description,
        price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,
        features: PRICING_CONFIG.PRIORITY_LAUNCH.features,
        recommended: true
    }
];
const TOOL_PRICING_TYPES = {
    FREE: {
        value: 'free',
        label: '免费',
        color: 'bg-green-100 text-green-800'
    },
    FREEMIUM: {
        value: 'freemium',
        label: '免费增值',
        color: 'bg-blue-100 text-blue-800'
    },
    PAID: {
        value: 'paid',
        label: '付费',
        color: 'bg-orange-100 text-orange-800'
    }
};
const TOOL_PRICING_OPTIONS = [
    {
        value: '',
        label: '所有价格'
    },
    {
        value: TOOL_PRICING_TYPES.FREE.value,
        label: TOOL_PRICING_TYPES.FREE.label
    },
    {
        value: TOOL_PRICING_TYPES.FREEMIUM.value,
        label: TOOL_PRICING_TYPES.FREEMIUM.label
    },
    {
        value: TOOL_PRICING_TYPES.PAID.value,
        label: TOOL_PRICING_TYPES.PAID.label
    }
];
const TOOL_PRICING_FORM_OPTIONS = [
    {
        value: TOOL_PRICING_TYPES.FREE.value,
        label: TOOL_PRICING_TYPES.FREE.label
    },
    {
        value: TOOL_PRICING_TYPES.FREEMIUM.value,
        label: TOOL_PRICING_TYPES.FREEMIUM.label
    },
    {
        value: TOOL_PRICING_TYPES.PAID.value,
        label: TOOL_PRICING_TYPES.PAID.label
    }
];
const getPricingConfig = (optionId)=>{
    return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;
};
const getToolPricingColor = (pricing)=>{
    switch(pricing){
        case TOOL_PRICING_TYPES.FREE.value:
            return TOOL_PRICING_TYPES.FREE.color;
        case TOOL_PRICING_TYPES.FREEMIUM.value:
            return TOOL_PRICING_TYPES.FREEMIUM.color;
        case TOOL_PRICING_TYPES.PAID.value:
            return TOOL_PRICING_TYPES.PAID.color;
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
const getToolPricingText = (pricing)=>{
    switch(pricing){
        case TOOL_PRICING_TYPES.FREE.value:
            return TOOL_PRICING_TYPES.FREE.label;
        case TOOL_PRICING_TYPES.FREEMIUM.value:
            return TOOL_PRICING_TYPES.FREEMIUM.label;
        case TOOL_PRICING_TYPES.PAID.value:
            return TOOL_PRICING_TYPES.PAID.label;
        default:
            return pricing;
    }
};
const formatPrice = (price)=>{
    return price === 0 ? '免费' : `¥${price}`;
};
const formatStripeAmount = (amount, currency = 'cny')=>{
    return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: currency.toUpperCase(),
        minimumFractionDigits: 2
    }).format(amount / 100);
};
}}),
"[project]/src/lib/api-messages.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// API 响应消息国际化
__turbopack_context__.s({
    "enMessages": (()=>enMessages),
    "getApiMessage": (()=>getApiMessage),
    "getLocaleFromRequest": (()=>getLocaleFromRequest),
    "zhMessages": (()=>zhMessages)
});
const zhMessages = {
    errors: {
        fetch_failed: '获取数据失败',
        network_error: '网络错误，请重试',
        validation_failed: '验证失败',
        unauthorized: '未授权访问',
        forbidden: '禁止访问',
        not_found: '资源未找到',
        internal_error: '服务器内部错误',
        invalid_request: '无效请求',
        missing_required_field: '缺少必需字段',
        duplicate_name: '名称已存在',
        create_failed: '创建失败',
        update_failed: '更新失败',
        delete_failed: '删除失败'
    },
    success: {
        created: '创建成功',
        updated: '更新成功',
        deleted: '删除成功',
        submitted: '提交成功',
        approved: '批准成功',
        rejected: '拒绝成功',
        published: '发布成功'
    },
    tools: {
        fetch_failed: '获取工具列表失败',
        create_failed: '创建工具失败',
        name_required: 'name 是必需的',
        description_required: 'description 是必需的',
        website_required: 'website 是必需的',
        category_required: 'category 是必需的',
        pricing_required: 'pricing 是必需的',
        submitter_name_required: 'submitterName 是必需的',
        submitter_email_required: 'submitterEmail 是必需的',
        name_exists: '该工具名称已存在',
        submit_success: '工具提交成功，等待审核',
        approve_success: '工具审核通过',
        reject_success: '工具已拒绝',
        approve_failed: '审核通过失败',
        reject_failed: '拒绝失败',
        not_found: '工具未找到',
        update_success: '工具更新成功',
        update_failed: '工具更新失败',
        launch_date_already_set: '此工具已经选择了发布日期',
        free_date_restriction: '免费选项只能选择一个月后的日期',
        paid_date_restriction: '付费选项最早只能选择明天的日期',
        launch_date_set_success: '发布日期设置成功，工具已进入审核队列',
        edit_not_allowed: '当前状态不允许修改发布日期',
        already_published: '工具已发布，无法修改发布日期',
        launch_date_updated: '发布日期修改成功',
        publish_success: '工具发布成功',
        publish_failed: '工具发布失败'
    },
    user: {
        not_found: '用户未找到',
        unauthorized: '用户未授权',
        profile_update_success: '个人资料更新成功',
        profile_update_failed: '个人资料更新失败'
    },
    auth: {
        invalid_credentials: '无效的登录凭据',
        code_sent: '验证码已发送',
        code_send_failed: '验证码发送失败',
        invalid_code: '无效的验证码',
        login_success: '登录成功',
        login_failed: '登录失败',
        logout_success: '退出成功'
    },
    payment: {
        create_intent_failed: '创建支付意图失败',
        payment_success: '支付成功',
        payment_failed: '支付失败',
        webhook_error: 'Webhook 处理错误',
        order_created: '订单创建成功，请完成支付',
        upgrade_order_created: '升级订单创建成功，请完成支付'
    },
    upload: {
        no_file: '请选择要上传的文件',
        invalid_type: '只支持 JPEG、PNG、GIF、WebP 格式的图片',
        file_too_large: '文件大小不能超过 5MB',
        upload_failed: '文件上传失败',
        upload_success: '文件上传成功'
    }
};
const enMessages = {
    errors: {
        fetch_failed: 'Failed to fetch data',
        network_error: 'Network error, please try again',
        validation_failed: 'Validation failed',
        unauthorized: 'Unauthorized access',
        forbidden: 'Access forbidden',
        not_found: 'Resource not found',
        internal_error: 'Internal server error',
        invalid_request: 'Invalid request',
        missing_required_field: 'Missing required field',
        duplicate_name: 'Name already exists',
        create_failed: 'Creation failed',
        update_failed: 'Update failed',
        delete_failed: 'Deletion failed'
    },
    success: {
        created: 'Created successfully',
        updated: 'Updated successfully',
        deleted: 'Deleted successfully',
        submitted: 'Submitted successfully',
        approved: 'Approved successfully',
        rejected: 'Rejected successfully',
        published: 'Published successfully'
    },
    tools: {
        fetch_failed: 'Failed to fetch tools list',
        create_failed: 'Failed to create tool',
        name_required: 'name is required',
        description_required: 'description is required',
        website_required: 'website is required',
        category_required: 'category is required',
        pricing_required: 'pricing is required',
        submitter_name_required: 'submitterName is required',
        submitter_email_required: 'submitterEmail is required',
        name_exists: 'Tool name already exists',
        submit_success: 'Tool submitted successfully, awaiting review',
        approve_success: 'Tool approved successfully',
        reject_success: 'Tool rejected successfully',
        approve_failed: 'Failed to approve tool',
        reject_failed: 'Failed to reject tool',
        not_found: 'Tool not found',
        update_success: 'Tool updated successfully',
        update_failed: 'Failed to update tool',
        launch_date_already_set: 'This tool has already selected a launch date',
        free_date_restriction: 'Free option can only select dates one month later',
        paid_date_restriction: 'Paid option can only select dates from tomorrow',
        launch_date_set_success: 'Launch date set successfully, tool entered review queue',
        edit_not_allowed: 'Current status does not allow modifying launch date',
        already_published: 'Tool already published, cannot modify launch date',
        launch_date_updated: 'Launch date updated successfully',
        publish_success: 'Tool published successfully',
        publish_failed: 'Failed to publish tool'
    },
    user: {
        not_found: 'User not found',
        unauthorized: 'User unauthorized',
        profile_update_success: 'Profile updated successfully',
        profile_update_failed: 'Failed to update profile'
    },
    auth: {
        invalid_credentials: 'Invalid credentials',
        code_sent: 'Verification code sent',
        code_send_failed: 'Failed to send verification code',
        invalid_code: 'Invalid verification code',
        login_success: 'Login successful',
        login_failed: 'Login failed',
        logout_success: 'Logout successful'
    },
    payment: {
        create_intent_failed: 'Failed to create payment intent',
        payment_success: 'Payment successful',
        payment_failed: 'Payment failed',
        webhook_error: 'Webhook processing error',
        order_created: 'Order created successfully, please complete payment',
        upgrade_order_created: 'Upgrade order created successfully, please complete payment'
    },
    upload: {
        no_file: 'Please select a file to upload',
        invalid_type: 'Only JPEG, PNG, GIF, WebP image formats are supported',
        file_too_large: 'File size cannot exceed 5MB',
        upload_failed: 'File upload failed',
        upload_success: 'File uploaded successfully'
    }
};
function getApiMessage(locale, key) {
    const messages = locale === 'zh' ? zhMessages : enMessages;
    // 支持嵌套键，如 'tools.fetch_failed'
    const keys = key.split('.');
    let message = messages;
    for (const k of keys){
        if (message && typeof message === 'object' && k in message) {
            message = message[k];
        } else {
            // 如果找不到对应的键，返回默认的中文消息
            return locale === 'zh' ? '操作失败' : 'Operation failed';
        }
    }
    return typeof message === 'string' ? message : locale === 'zh' ? '操作失败' : 'Operation failed';
}
function getLocaleFromRequest(request) {
    const acceptLanguage = request.headers.get('accept-language') || '';
    const pathname = new URL(request.url).pathname;
    // 首先检查URL路径中的语言前缀
    if (pathname.startsWith('/en/')) {
        return 'en';
    } else if (pathname.startsWith('/zh/')) {
        return 'zh';
    }
    // 然后检查Accept-Language头
    if (acceptLanguage.includes('en')) {
        return 'en';
    }
    // 默认返回中文
    return 'zh';
}
}}),
"[project]/src/app/api/tools/[id]/launch-date/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "PATCH": (()=>PATCH),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/next/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Tool.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Order$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Order.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$pricing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/pricing.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-messages.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
async function POST(request, { params }) {
    try {
        // 检查用户认证
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLocaleFromRequest"])(request);
        if (!session?.user?.email) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'user.unauthorized')
            }, {
                status: 401
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { id } = await params;
        const { launchOption, selectedDate } = await request.json();
        // 验证ID格式
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Types.ObjectId.isValid(id)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.invalid_request')
            }, {
                status: 400
            });
        }
        // 验证输入
        if (!launchOption || !selectedDate) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.invalid_request')
            }, {
                status: 400
            });
        }
        if (![
            'free',
            'paid'
        ].includes(launchOption)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.invalid_request')
            }, {
                status: 400
            });
        }
        // 获取用户信息
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            email: session.user.email
        });
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'user.not_found')
            }, {
                status: 404
            });
        }
        // 查找工具
        const tool = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(id);
        if (!tool) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.not_found')
            }, {
                status: 404
            });
        }
        // 检查工具所有权
        if (tool.submittedBy !== user._id.toString()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.forbidden')
            }, {
                status: 403
            });
        }
        // 检查工具状态 - 允许draft状态或者免费用户升级到付费
        if (tool.status !== 'draft' && !(tool.status === 'pending' && tool.launchOption === 'free' && launchOption === 'paid')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.launch_date_already_set')
            }, {
                status: 400
            });
        }
        const selectedLaunchDate = new Date(selectedDate);
        const now = new Date();
        // 验证日期
        if (launchOption === 'free') {
            // 免费选项：必须是一个月后或更晚的日期
            const oneMonthLater = new Date();
            oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
            oneMonthLater.setHours(0, 0, 0, 0);
            const selectedDateOnly = new Date(selectedLaunchDate);
            selectedDateOnly.setHours(0, 0, 0, 0);
            if (selectedDateOnly.getTime() < oneMonthLater.getTime()) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.free_date_restriction')
                }, {
                    status: 400
                });
            }
        } else {
            // 付费选项：必须是明天或以后的日期
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);
            if (selectedLaunchDate < tomorrow) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.paid_date_restriction')
                }, {
                    status: 400
                });
            }
        }
        if (launchOption === 'free') {
            // 免费选项：直接更新工具状态并进入审核
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findByIdAndUpdate(id, {
                $set: {
                    launchDateSelected: true,
                    selectedLaunchDate,
                    launchDate: selectedLaunchDate,
                    launchOption: 'free',
                    paymentRequired: false,
                    status: 'pending' // 进入审核队列
                }
            });
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: {
                    message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.launch_date_set_success')
                }
            });
        } else {
            // 付费选项：创建订单
            const paymentAmount = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$pricing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRICING_CONFIG"].PRIORITY_LAUNCH.stripeAmount;
            const order = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Order$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
                userId: user._id,
                toolId: id,
                type: 'launch_date_priority',
                amount: paymentAmount,
                currency: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$pricing$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PRICING_CONFIG"].PRIORITY_LAUNCH.currency,
                status: 'pending',
                description: `工具 "${tool.name}" 优先发布服务`,
                selectedLaunchDate
            });
            await order.save();
            // 更新工具状态
            const updateData = {
                launchDateSelected: true,
                selectedLaunchDate,
                launchDate: selectedLaunchDate,
                launchOption: 'paid',
                paymentRequired: true,
                paymentAmount,
                paymentStatus: 'pending',
                orderId: order._id.toString()
            };
            // 付费工具在付费成功前始终保持draft状态
            updateData.status = 'draft';
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findByIdAndUpdate(id, {
                $set: updateData
            });
            // 这里应该集成真实的支付系统（如Stripe、支付宝等）
            // 现在返回一个模拟的支付URL
            const paymentUrl = `/payment/checkout?orderId=${order._id}`;
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: {
                    orderId: order._id,
                    paymentUrl,
                    amount: paymentAmount,
                    message: tool.status === 'pending' ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'payment.upgrade_order_created') : (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'payment.order_created')
                }
            });
        }
    } catch (error) {
        console.error('Launch date selection error:', error);
        const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLocaleFromRequest"])(request);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.internal_error')
        }, {
            status: 500
        });
    }
}
async function GET(request, { params }) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLocaleFromRequest"])(request);
        if (!session?.user?.email) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'user.unauthorized')
            }, {
                status: 401
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { id } = await params;
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Types.ObjectId.isValid(id)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.invalid_request')
            }, {
                status: 400
            });
        }
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            email: session.user.email
        });
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'user.not_found')
            }, {
                status: 404
            });
        }
        const tool = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(id);
        if (!tool) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.not_found')
            }, {
                status: 404
            });
        }
        // 检查工具所有权
        if (tool.submittedBy !== user._id.toString()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.forbidden')
            }, {
                status: 403
            });
        }
        // 如果有订单，获取订单信息
        let order = null;
        if (tool.orderId) {
            order = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Order$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(tool.orderId);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                tool: {
                    id: tool._id,
                    name: tool.name,
                    status: tool.status,
                    launchDateSelected: tool.launchDateSelected,
                    selectedLaunchDate: tool.selectedLaunchDate,
                    launchOption: tool.launchOption,
                    paymentRequired: tool.paymentRequired,
                    paymentStatus: tool.paymentStatus
                },
                order
            }
        });
    } catch (error) {
        console.error('Get launch date info error:', error);
        const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLocaleFromRequest"])(request);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.internal_error')
        }, {
            status: 500
        });
    }
}
async function PATCH(request, { params }) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLocaleFromRequest"])(request);
        if (!session?.user?.email) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'user.unauthorized')
            }, {
                status: 401
            });
        }
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { id } = await params;
        const { selectedDate } = await request.json();
        if (!__TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].Types.ObjectId.isValid(id)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.invalid_request')
            }, {
                status: 400
            });
        }
        if (!selectedDate) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.invalid_request')
            }, {
                status: 400
            });
        }
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            email: session.user.email
        });
        if (!user) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'user.not_found')
            }, {
                status: 404
            });
        }
        const tool = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(id);
        if (!tool) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.not_found')
            }, {
                status: 404
            });
        }
        // 检查工具所有权
        if (tool.submittedBy !== user._id.toString()) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.forbidden')
            }, {
                status: 403
            });
        }
        // 检查工具状态（只有还未发布的工具可以修改）
        if (![
            'pending',
            'approved'
        ].includes(tool.status)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.edit_not_allowed')
            }, {
                status: 400
            });
        }
        // 检查是否已经发布
        const now = new Date();
        const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= now;
        if (isPublished) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: false,
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.already_published')
            }, {
                status: 400
            });
        }
        const selectedLaunchDate = new Date(selectedDate);
        // 根据付费状态验证日期
        if (tool.launchOption === 'paid') {
            // 付费用户：可以选择明天及以后的任意日期
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(0, 0, 0, 0);
            if (selectedLaunchDate < tomorrow) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.paid_date_restriction')
                }, {
                    status: 400
                });
            }
        } else {
            // 免费用户：只能选择一个月后的日期
            const oneMonthLater = new Date();
            oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
            oneMonthLater.setHours(0, 0, 0, 0);
            const selectedDateOnly = new Date(selectedLaunchDate);
            selectedDateOnly.setHours(0, 0, 0, 0);
            if (selectedDateOnly.getTime() < oneMonthLater.getTime()) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    success: false,
                    message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.free_date_restriction')
                }, {
                    status: 400
                });
            }
        }
        // 更新工具的发布日期
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findByIdAndUpdate(id, {
            $set: {
                selectedLaunchDate,
                launchDate: selectedLaunchDate // 同时更新launchDate
            }
        });
        // 如果有关联的订单，也更新订单的发布日期
        if (tool.orderId) {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Order$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findByIdAndUpdate(tool.orderId, {
                $set: {
                    selectedLaunchDate
                }
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'tools.launch_date_updated'),
                selectedLaunchDate
            }
        });
    } catch (error) {
        console.error('Update launch date error:', error);
        const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLocaleFromRequest"])(request);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            message: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.internal_error')
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__cf133a8d._.js.map