module.exports = {

"[project]/.next-internal/server/app/api/categories/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function dbConnect() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = dbConnect;
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/i18n/config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "defaultLocale": (()=>defaultLocale),
    "isValidLocale": (()=>isValidLocale),
    "localeNames": (()=>localeNames),
    "locales": (()=>locales)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js [app-route] (ecmascript) <export default as getRequestConfig>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-route] (ecmascript)");
;
;
const locales = [
    'en',
    'zh'
];
const defaultLocale = 'en';
const localeNames = {
    zh: '中文',
    en: 'English'
};
function isValidLocale(locale) {
    return locales.includes(locale);
}
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__["getRequestConfig"])(async ({ locale })=>{
    // 验证传入的语言是否有效
    if (!isValidLocale(locale)) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["notFound"])();
    }
    return {
        messages: (await __turbopack_context__.f({
            "./messages/en.json": {
                id: ()=>"[project]/src/i18n/messages/en.json (json, async loader)",
                module: ()=>__turbopack_context__.r("[project]/src/i18n/messages/en.json (json, async loader)")(__turbopack_context__.i)
            },
            "./messages/zh.json": {
                id: ()=>"[project]/src/i18n/messages/zh.json (json, async loader)",
                module: ()=>__turbopack_context__.r("[project]/src/i18n/messages/zh.json (json, async loader)")(__turbopack_context__.i)
            }
        }).import(`./messages/${locale}.json`)).default
    };
});
}}),
"[project]/src/i18n/request.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js [app-route] (ecmascript) <export default as getRequestConfig>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/config.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-route] (ecmascript)");
;
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__["getRequestConfig"])(async ({ locale })=>{
    // Validate that the incoming `locale` parameter is valid
    if (!locale || !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["locales"].includes(locale)) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["notFound"])();
    }
    return {
        locale,
        messages: (await __turbopack_context__.f({
            "./messages/en.json": {
                id: ()=>"[project]/src/i18n/messages/en.json (json, async loader)",
                module: ()=>__turbopack_context__.r("[project]/src/i18n/messages/en.json (json, async loader)")(__turbopack_context__.i)
            },
            "./messages/zh.json": {
                id: ()=>"[project]/src/i18n/messages/zh.json (json, async loader)",
                module: ()=>__turbopack_context__.r("[project]/src/i18n/messages/zh.json (json, async loader)")(__turbopack_context__.i)
            }
        }).import(`./messages/${locale}.json`)).default
    };
});
}}),
"[project]/src/constants/categories-i18n.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 国际化分类配置文件
// 支持多语言的AI工具分类配置
__turbopack_context__.s({
    "CATEGORY_BASE_CONFIGS": (()=>CATEGORY_BASE_CONFIGS),
    "CATEGORY_BASE_METADATA": (()=>CATEGORY_BASE_METADATA),
    "CATEGORY_SLUGS": (()=>CATEGORY_SLUGS),
    "getCategoryConfig": (()=>getCategoryConfig),
    "getCategoryConfigs": (()=>getCategoryConfigs),
    "getCategoryDescription": (()=>getCategoryDescription),
    "getCategoryName": (()=>getCategoryName),
    "getCategoryOptions": (()=>getCategoryOptions),
    "getCategoryOptionsWithAll": (()=>getCategoryOptionsWithAll),
    "isValidCategory": (()=>isValidCategory),
    "useCategoryConfigs": (()=>useCategoryConfigs),
    "useCategoryDescription": (()=>useCategoryDescription),
    "useCategoryName": (()=>useCategoryName),
    "useCategoryOptions": (()=>useCategoryOptions),
    "useCategoryOptionsWithAll": (()=>useCategoryOptionsWithAll)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-server/useTranslations.js [app-route] (ecmascript) <export default as useTranslations>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js [app-route] (ecmascript) <export default as getTranslations>");
;
;
const CATEGORY_BASE_CONFIGS = [
    {
        slug: 'text-generation',
        icon: '📝',
        color: '#3B82F6'
    },
    {
        slug: 'image-generation',
        icon: '🎨',
        color: '#10B981'
    },
    {
        slug: 'code-generation',
        icon: '💻',
        color: '#8B5CF6'
    },
    {
        slug: 'data-analysis',
        icon: '📊',
        color: '#F59E0B'
    },
    {
        slug: 'audio-processing',
        icon: '🎵',
        color: '#EF4444'
    },
    {
        slug: 'video-editing',
        icon: '🎬',
        color: '#06B6D4'
    },
    {
        slug: 'translation',
        icon: '🌐',
        color: '#84CC16'
    },
    {
        slug: 'search-engines',
        icon: '🔍',
        color: '#F97316'
    },
    {
        slug: 'education',
        icon: '📚',
        color: '#A855F7'
    },
    {
        slug: 'marketing',
        icon: '📈',
        color: '#EC4899'
    },
    {
        slug: 'productivity',
        icon: '⚡',
        color: '#14B8A6'
    },
    {
        slug: 'customer-service',
        icon: '🎧',
        color: '#F59E0B'
    }
];
function useCategoryConfigs() {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    return CATEGORY_BASE_CONFIGS.map((config)=>({
            slug: config.slug,
            name: t(`category_names.${config.slug}`),
            description: t(`category_descriptions.${config.slug}`),
            icon: config.icon,
            color: config.color
        }));
}
function useCategoryOptions() {
    const configs = useCategoryConfigs();
    return configs.map((config)=>({
            value: config.slug,
            label: config.name
        }));
}
function useCategoryOptionsWithAll() {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    const options = useCategoryOptions();
    return [
        {
            value: '',
            label: t('all_categories')
        },
        ...options
    ];
}
function useCategoryName(slug) {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    return t(`category_names.${slug}`) || slug;
}
function useCategoryDescription(slug) {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    return t(`category_descriptions.${slug}`) || '';
}
async function getCategoryConfigs(locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    return CATEGORY_BASE_CONFIGS.map((config)=>({
            slug: config.slug,
            name: t(`category_names.${config.slug}`),
            description: t(`category_descriptions.${config.slug}`),
            icon: config.icon,
            color: config.color
        }));
}
async function getCategoryOptions(locale) {
    const configs = await getCategoryConfigs(locale);
    return configs.map((config)=>({
            value: config.slug,
            label: config.name
        }));
}
async function getCategoryOptionsWithAll(locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    const options = await getCategoryOptions(locale);
    return [
        {
            value: '',
            label: t('all_categories')
        },
        ...options
    ];
}
async function getCategoryName(slug, locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    return t(`category_names.${slug}`) || slug;
}
async function getCategoryDescription(slug, locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    return t(`category_descriptions.${slug}`) || '';
}
async function getCategoryConfig(slug, locale) {
    const configs = await getCategoryConfigs(locale);
    return configs.find((config)=>config.slug === slug);
}
function isValidCategory(slug) {
    return CATEGORY_BASE_CONFIGS.some((config)=>config.slug === slug);
}
const CATEGORY_SLUGS = CATEGORY_BASE_CONFIGS.map((config)=>config.slug);
const CATEGORY_BASE_METADATA = CATEGORY_BASE_CONFIGS.reduce((acc, config)=>{
    acc[config.slug] = config;
    return acc;
}, {});
}}),
"[project]/src/models/Tool.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$categories$2d$i18n$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/categories-i18n.ts [app-route] (ecmascript)");
;
;
const ToolSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    name: {
        type: String,
        required: [
            true,
            'Tool name is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Tool name cannot exceed 100 characters'
        ]
    },
    tagline: {
        type: String,
        trim: true,
        maxlength: [
            200,
            'Tagline cannot exceed 200 characters'
        ]
    },
    description: {
        type: String,
        required: [
            true,
            'Tool description is required'
        ],
        trim: true,
        maxlength: [
            500,
            'Description cannot exceed 500 characters'
        ]
    },
    longDescription: {
        type: String,
        trim: true,
        maxlength: [
            2000,
            'Long description cannot exceed 2000 characters'
        ]
    },
    website: {
        type: String,
        required: [
            true,
            'Website URL is required'
        ],
        trim: true,
        validate: {
            validator: function(v) {
                return /^https?:\/\/.+/.test(v);
            },
            message: 'Please enter a valid URL'
        }
    },
    logo: {
        type: String,
        trim: true
    },
    category: {
        type: String,
        required: [
            true,
            'Category is required'
        ],
        enum: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$categories$2d$i18n$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CATEGORY_SLUGS"]
    },
    tags: [
        {
            type: String,
            trim: true,
            lowercase: true
        }
    ],
    pricing: {
        type: String,
        required: [
            true,
            'Pricing model is required'
        ],
        enum: [
            'free',
            'freemium',
            'paid'
        ]
    },
    pricingDetails: {
        type: String,
        trim: true,
        maxlength: [
            500,
            'Pricing details cannot exceed 500 characters'
        ]
    },
    screenshots: [
        {
            type: String,
            trim: true
        }
    ],
    submittedBy: {
        type: String,
        required: [
            true,
            'Submitter ID is required'
        ],
        trim: true
    },
    submittedAt: {
        type: Date,
        default: Date.now
    },
    launchDate: {
        type: Date
    },
    status: {
        type: String,
        required: true,
        enum: [
            'draft',
            'pending',
            'approved',
            'rejected'
        ],
        default: 'draft'
    },
    reviewNotes: {
        type: String,
        trim: true,
        maxlength: [
            1000,
            'Review notes cannot exceed 1000 characters'
        ]
    },
    reviewedBy: {
        type: String,
        trim: true
    },
    reviewedAt: {
        type: Date
    },
    // 发布日期选择相关
    launchDateSelected: {
        type: Boolean,
        default: false
    },
    selectedLaunchDate: {
        type: Date
    },
    launchOption: {
        type: String,
        enum: [
            'free',
            'paid'
        ]
    },
    // 付费相关
    paymentRequired: {
        type: Boolean,
        default: false
    },
    paymentAmount: {
        type: Number,
        min: 0
    },
    paymentStatus: {
        type: String,
        enum: [
            'pending',
            'completed',
            'failed',
            'refunded'
        ]
    },
    orderId: {
        type: String,
        trim: true
    },
    paymentMethod: {
        type: String,
        trim: true
    },
    paidAt: {
        type: Date
    },
    views: {
        type: Number,
        default: 0,
        min: 0
    },
    likes: {
        type: Number,
        default: 0,
        min: 0
    },
    likedBy: [
        {
            type: String,
            trim: true
        }
    ],
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes for better query performance
ToolSchema.index({
    status: 1,
    isActive: 1
});
ToolSchema.index({
    category: 1,
    status: 1
});
ToolSchema.index({
    tags: 1,
    status: 1
});
ToolSchema.index({
    submittedBy: 1
});
ToolSchema.index({
    launchDate: -1
});
ToolSchema.index({
    views: -1
});
ToolSchema.index({
    likes: -1
});
// Text search index
ToolSchema.index({
    name: 'text',
    tagline: 'text',
    description: 'text',
    longDescription: 'text',
    tags: 'text'
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Tool || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Tool', ToolSchema);
}}),
"[project]/src/lib/api-messages.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// API 响应消息国际化
__turbopack_context__.s({
    "enMessages": (()=>enMessages),
    "getApiMessage": (()=>getApiMessage),
    "getLocaleFromRequest": (()=>getLocaleFromRequest),
    "zhMessages": (()=>zhMessages)
});
const zhMessages = {
    errors: {
        fetch_failed: '获取数据失败',
        network_error: '网络错误，请重试',
        validation_failed: '验证失败',
        unauthorized: '未授权访问',
        forbidden: '禁止访问',
        not_found: '资源未找到',
        internal_error: '服务器内部错误',
        invalid_request: '无效请求',
        missing_required_field: '缺少必需字段',
        duplicate_name: '名称已存在',
        create_failed: '创建失败',
        update_failed: '更新失败',
        delete_failed: '删除失败'
    },
    success: {
        created: '创建成功',
        updated: '更新成功',
        deleted: '删除成功',
        submitted: '提交成功',
        approved: '批准成功',
        rejected: '拒绝成功',
        published: '发布成功'
    },
    tools: {
        fetch_failed: '获取工具列表失败',
        create_failed: '创建工具失败',
        name_required: 'name 是必需的',
        description_required: 'description 是必需的',
        website_required: 'website 是必需的',
        category_required: 'category 是必需的',
        pricing_required: 'pricing 是必需的',
        submitter_name_required: 'submitterName 是必需的',
        submitter_email_required: 'submitterEmail 是必需的',
        name_exists: '该工具名称已存在',
        submit_success: '工具提交成功，等待审核',
        approve_success: '工具审核通过',
        reject_success: '工具已拒绝',
        approve_failed: '审核通过失败',
        reject_failed: '拒绝失败',
        not_found: '工具未找到',
        update_success: '工具更新成功',
        update_failed: '工具更新失败',
        launch_date_already_set: '此工具已经选择了发布日期',
        free_date_restriction: '免费选项只能选择一个月后的日期',
        paid_date_restriction: '付费选项最早只能选择明天的日期',
        launch_date_set_success: '发布日期设置成功，工具已进入审核队列',
        edit_not_allowed: '当前状态不允许修改发布日期',
        already_published: '工具已发布，无法修改发布日期',
        launch_date_updated: '发布日期修改成功',
        publish_success: '工具发布成功',
        publish_failed: '工具发布失败'
    },
    user: {
        not_found: '用户未找到',
        unauthorized: '用户未授权',
        profile_update_success: '个人资料更新成功',
        profile_update_failed: '个人资料更新失败'
    },
    auth: {
        invalid_credentials: '无效的登录凭据',
        code_sent: '验证码已发送',
        code_send_failed: '验证码发送失败',
        invalid_code: '无效的验证码',
        login_success: '登录成功',
        login_failed: '登录失败',
        logout_success: '退出成功'
    },
    payment: {
        create_intent_failed: '创建支付意图失败',
        payment_success: '支付成功',
        payment_failed: '支付失败',
        webhook_error: 'Webhook 处理错误',
        order_created: '订单创建成功，请完成支付',
        upgrade_order_created: '升级订单创建成功，请完成支付'
    },
    upload: {
        no_file: '请选择要上传的文件',
        invalid_type: '只支持 JPEG、PNG、GIF、WebP 格式的图片',
        file_too_large: '文件大小不能超过 5MB',
        upload_failed: '文件上传失败',
        upload_success: '文件上传成功'
    }
};
const enMessages = {
    errors: {
        fetch_failed: 'Failed to fetch data',
        network_error: 'Network error, please try again',
        validation_failed: 'Validation failed',
        unauthorized: 'Unauthorized access',
        forbidden: 'Access forbidden',
        not_found: 'Resource not found',
        internal_error: 'Internal server error',
        invalid_request: 'Invalid request',
        missing_required_field: 'Missing required field',
        duplicate_name: 'Name already exists',
        create_failed: 'Creation failed',
        update_failed: 'Update failed',
        delete_failed: 'Deletion failed'
    },
    success: {
        created: 'Created successfully',
        updated: 'Updated successfully',
        deleted: 'Deleted successfully',
        submitted: 'Submitted successfully',
        approved: 'Approved successfully',
        rejected: 'Rejected successfully',
        published: 'Published successfully'
    },
    tools: {
        fetch_failed: 'Failed to fetch tools list',
        create_failed: 'Failed to create tool',
        name_required: 'name is required',
        description_required: 'description is required',
        website_required: 'website is required',
        category_required: 'category is required',
        pricing_required: 'pricing is required',
        submitter_name_required: 'submitterName is required',
        submitter_email_required: 'submitterEmail is required',
        name_exists: 'Tool name already exists',
        submit_success: 'Tool submitted successfully, awaiting review',
        approve_success: 'Tool approved successfully',
        reject_success: 'Tool rejected successfully',
        approve_failed: 'Failed to approve tool',
        reject_failed: 'Failed to reject tool',
        not_found: 'Tool not found',
        update_success: 'Tool updated successfully',
        update_failed: 'Failed to update tool',
        launch_date_already_set: 'This tool has already selected a launch date',
        free_date_restriction: 'Free option can only select dates one month later',
        paid_date_restriction: 'Paid option can only select dates from tomorrow',
        launch_date_set_success: 'Launch date set successfully, tool entered review queue',
        edit_not_allowed: 'Current status does not allow modifying launch date',
        already_published: 'Tool already published, cannot modify launch date',
        launch_date_updated: 'Launch date updated successfully',
        publish_success: 'Tool published successfully',
        publish_failed: 'Failed to publish tool'
    },
    user: {
        not_found: 'User not found',
        unauthorized: 'User unauthorized',
        profile_update_success: 'Profile updated successfully',
        profile_update_failed: 'Failed to update profile'
    },
    auth: {
        invalid_credentials: 'Invalid credentials',
        code_sent: 'Verification code sent',
        code_send_failed: 'Failed to send verification code',
        invalid_code: 'Invalid verification code',
        login_success: 'Login successful',
        login_failed: 'Login failed',
        logout_success: 'Logout successful'
    },
    payment: {
        create_intent_failed: 'Failed to create payment intent',
        payment_success: 'Payment successful',
        payment_failed: 'Payment failed',
        webhook_error: 'Webhook processing error',
        order_created: 'Order created successfully, please complete payment',
        upgrade_order_created: 'Upgrade order created successfully, please complete payment'
    },
    upload: {
        no_file: 'Please select a file to upload',
        invalid_type: 'Only JPEG, PNG, GIF, WebP image formats are supported',
        file_too_large: 'File size cannot exceed 5MB',
        upload_failed: 'File upload failed',
        upload_success: 'File uploaded successfully'
    }
};
function getApiMessage(locale, key) {
    const messages = locale === 'zh' ? zhMessages : enMessages;
    // 支持嵌套键，如 'tools.fetch_failed'
    const keys = key.split('.');
    let message = messages;
    for (const k of keys){
        if (message && typeof message === 'object' && k in message) {
            message = message[k];
        } else {
            // 如果找不到对应的键，返回默认的中文消息
            return locale === 'zh' ? '操作失败' : 'Operation failed';
        }
    }
    return typeof message === 'string' ? message : locale === 'zh' ? '操作失败' : 'Operation failed';
}
function getLocaleFromRequest(request) {
    const acceptLanguage = request.headers.get('accept-language') || '';
    const pathname = new URL(request.url).pathname;
    // 首先检查URL路径中的语言前缀
    if (pathname.startsWith('/en/')) {
        return 'en';
    } else if (pathname.startsWith('/zh/')) {
        return 'zh';
    }
    // 然后检查Accept-Language头
    if (acceptLanguage.includes('en')) {
        return 'en';
    }
    // 默认返回中文
    return 'zh';
}
}}),
"[project]/src/app/api/categories/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Tool.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$categories$2d$i18n$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/categories-i18n.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-messages.ts [app-route] (ecmascript)");
;
;
;
;
;
async function GET(request) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        // 获取所有分类的工具数量统计（已发布的工具）
        const categoryStats = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    status: 'approved',
                    launchDate: {
                        $lte: new Date()
                    }
                }
            },
            {
                $group: {
                    _id: '$category',
                    count: {
                        $sum: 1
                    },
                    totalViews: {
                        $sum: '$views'
                    },
                    totalLikes: {
                        $sum: '$likes'
                    }
                }
            },
            {
                $sort: {
                    count: -1
                }
            }
        ]);
        // 构建完整的分类列表（使用分类slug）
        const categories = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$categories$2d$i18n$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CATEGORY_SLUGS"].map((slug)=>{
            const stats = categoryStats.find((stat)=>stat._id === slug);
            return {
                id: slug,
                name: slug,
                count: stats?.count || 0,
                totalViews: stats?.totalViews || 0,
                totalLikes: stats?.totalLikes || 0
            };
        });
        // 按工具数量排序
        categories.sort((a, b)=>b.count - a.count);
        // 获取总统计（已发布的工具）
        const totalStats = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    status: 'approved',
                    launchDate: {
                        $lte: new Date()
                    }
                }
            },
            {
                $group: {
                    _id: null,
                    totalTools: {
                        $sum: 1
                    },
                    totalViews: {
                        $sum: '$views'
                    },
                    totalLikes: {
                        $sum: '$likes'
                    }
                }
            }
        ]);
        const overview = totalStats[0] || {
            totalTools: 0,
            totalViews: 0,
            totalLikes: 0
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: {
                categories,
                overview
            }
        });
    } catch (error) {
        console.error('Error fetching categories:', error);
        const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getLocaleFromRequest"])(request);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: false,
            error: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$messages$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getApiMessage"])(locale, 'errors.internal_error')
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__01c9467e._.js.map