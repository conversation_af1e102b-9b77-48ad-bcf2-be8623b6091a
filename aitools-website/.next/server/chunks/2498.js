"use strict";exports.id=2498,exports.ids=[2498],exports.modules={5336:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},32498:(e,r,t)=>{t.d(r,{A:()=>u});var a=t(60687),l=t(43210),s=t(77618),i=t(40228),d=t(85778),c=t(5336),n=t(48730),o=t(94865);let m=o.vS;function u({currentOption:e="free",currentDate:r,isEditing:t=!1,onSubmit:u,isSubmitting:x,error:p}){let[h,g]=(0,l.useState)(e),[b,y]=(0,l.useState)(""),v=(0,s.c3)("launch"),f=()=>{let e=new Date;return e.setMonth(e.getMonth()+1),e.toISOString().split("T")[0]},N=()=>{let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]},E=e=>{g(e),"free"===e?y(f()):y(N())},A=async()=>{b&&await u(h,b)};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:t?v("select_plan"):v("select_option")}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:m.map(e=>(0,a.jsxs)("div",{className:`relative border-2 rounded-lg p-6 cursor-pointer transition-all ${h===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"} ${"recommended"in e&&e.recommended?"ring-2 ring-blue-200":""}`,onClick:()=>E(e.id),children:["recommended"in e&&e.recommended&&(0,a.jsx)("div",{className:"absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:v("recommended")}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center",children:["free"===e.id?(0,a.jsx)(i.A,{className:"h-6 w-6 text-gray-600 mr-3"}):(0,a.jsx)(d.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.description})]})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,o.$g)(e.price)})})]}),(0,a.jsx)("ul",{className:"space-y-2",children:e.features.map((e,r)=>(0,a.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-green-500 mr-2 flex-shrink-0"}),e]},r))}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("input",{type:"radio",name:"launchOption",value:e.id,checked:h===e.id,onChange:()=>E(e.id),className:"sr-only"}),(0,a.jsx)("div",{className:`w-4 h-4 rounded-full border-2 ${h===e.id?"border-blue-500 bg-blue-500":"border-gray-300"}`,children:h===e.id&&(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full mx-auto mt-0.5"})})]})]},e.id))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(n.A,{className:"h-5 w-5 mr-2"}),v("select_date")]}),(0,a.jsxs)("div",{className:"max-w-md",children:[(0,a.jsx)("input",{type:"date",value:b,onChange:e=>y(e.target.value),min:"free"===h?f():N(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"free"===h?v("free_date_info"):v("paid_date_info")})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("button",{onClick:A,disabled:x||!b,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"paid"===h?v("processing"):v("saving")]}):(0,a.jsx)(a.Fragment,{children:"paid"===h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-2"}),t?v("upgrade_and_pay",{price:(0,o.$g)(o.kX.PRIORITY_LAUNCH.displayPrice)}):v("pay_amount",{price:(0,o.$g)(o.kX.PRIORITY_LAUNCH.displayPrice)})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),t?v("save_changes"):v("confirm_date")]})})}),p&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-4",children:p}),(0,a.jsx)("p",{className:"text-gray-500 text-sm mt-4",children:"paid"===h?v("payment_redirect"):t?v("changes_effective"):v("review_queue")})]})]})}},40228:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48730:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},62688:(e,r,t)=>{t.d(r,{A:()=>m});var a=t(43210);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),i=e=>{let r=s(e);return r.charAt(0).toUpperCase()+r.slice(1)},d=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),c=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:l,className:s="",children:i,iconNode:o,...m},u)=>(0,a.createElement)("svg",{ref:u,...n,width:r,height:r,stroke:e,strokeWidth:l?24*Number(t)/Number(r):t,className:d("lucide",s),...!i&&!c(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(i)?i:[i]])),m=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...s},c)=>(0,a.createElement)(o,{ref:c,iconNode:r,className:d(`lucide-${l(i(e))}`,`lucide-${e}`,t),...s}));return t.displayName=i(e),t}},85778:(e,r,t)=>{t.d(r,{A:()=>a});let a=(0,t(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},94865:(e,r,t)=>{t.d(r,{$g:()=>o,Ef:()=>c,Y$:()=>d,kX:()=>a,mV:()=>n,tF:()=>m,v4:()=>i,vS:()=>l});let a={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},l=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],s={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"所有价格"},{value:s.FREE.value,label:s.FREE.label},{value:s.FREEMIUM.value,label:s.FREEMIUM.label},{value:s.PAID.value,label:s.PAID.label}],d=[{value:s.FREE.value,label:s.FREE.label},{value:s.FREEMIUM.value,label:s.FREEMIUM.label},{value:s.PAID.value,label:s.PAID.label}],c=e=>{switch(e){case s.FREE.value:return s.FREE.color;case s.FREEMIUM.value:return s.FREEMIUM.color;case s.PAID.value:return s.PAID.color;default:return"bg-gray-100 text-gray-800"}},n=e=>{switch(e){case s.FREE.value:return s.FREE.label;case s.FREEMIUM.value:return s.FREEMIUM.label;case s.PAID.value:return s.PAID.label;default:return e}},o=e=>0===e?"免费":`\xa5${e}`,m=(e,r="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:r.toUpperCase(),minimumFractionDigits:2}).format(e/100)}};