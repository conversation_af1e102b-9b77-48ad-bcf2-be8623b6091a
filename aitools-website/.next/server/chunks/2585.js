exports.id=2585,exports.ids=[2585],exports.modules={2328:(e,t,s)=>{"use strict";s.d(t,{LikeProvider:()=>n,X:()=>c});var r=s(60687),o=s(43210),i=s(82136);let l={liked:!1,likes:0,loading:!1},a=(0,o.createContext)(null);function n({children:e}){let{data:t}=(0,i.useSession)(),[s,n]=(0,o.useState)({}),c=(0,o.useCallback)(e=>s[e]||l,[s]),d=(0,o.useCallback)((e,t,s=!1)=>{n(r=>r[e]?r:{...r,[e]:{liked:s,likes:t,loading:!1}})},[]),m=(0,o.useCallback)(async e=>{if(t)try{let t=await fetch(`/api/tools/${e}/like`);if(t.ok){let s=await t.json();s.success&&n(t=>({...t,[e]:{liked:s.data.liked,likes:s.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[t]),x=(0,o.useCallback)(async(e,s=!1)=>{if(!t)return!1;n(t=>({...t,[e]:{...t[e]||l,loading:!0}}));try{let t=await fetch(`/api/tools/${e}/like`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s?{forceUnlike:!0}:{})});if(t.ok){let s=await t.json();if(s.success)return n(t=>({...t,[e]:{liked:s.data.liked,likes:s.data.likes,loading:!1}})),!0}return n(t=>({...t,[e]:{...t[e]||l,loading:!1}})),!1}catch(t){return console.error("Like request failed:",t),n(t=>({...t,[e]:{...t[e]||l,loading:!1}})),!1}},[t]);return(0,r.jsx)(a.Provider,{value:{toolStates:s,toggleLike:x,getToolState:c,initializeToolState:d,refreshToolState:m},children:e})}function c(){let e=(0,o.useContext)(a);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},3845:(e,t,s)=>{var r={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function o(e){if(!s.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],o=t[0];return s.e(t[1]).then(()=>s.t(o,19))}o.keys=()=>Object.keys(r),o.id=3845,e.exports=o},10456:()=>{},12340:(e,t,s)=>{"use strict";s.d(t,{N_:()=>l,a8:()=>n,rd:()=>c});var r=s(85484),o=s(48022);let i=(0,r.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:l,redirect:a,usePathname:n,useRouter:c}=(0,o.A)(i)},12623:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx","default")},12688:(e,t,s)=>{"use strict";s.d(t,{IB:()=>r,q:()=>o});let r=["en","zh"],o="en"},17941:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(35471),o=s(12688);let i=(0,r.A)(async({locale:e})=>(o.IB.includes(e)||(e=o.q),{locale:e,messages:(await s(3845)(`./${e}.json`)).default}))},22017:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(60687),o=s(43210),i=s(12340),l=s(23877),a=s(51188);let n=({children:e,href:t})=>(0,r.jsx)(i.N_,{href:t,className:"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors",children:e});function c({links:e,locale:t}){let[s,i]=(0,o.useState)(!1);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{className:"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",onClick:()=>i(!s),"aria-label":"Open Menu",children:s?(0,r.jsx)(l.QCr,{}):(0,r.jsx)(l.OXb,{})}),s&&(0,r.jsx)("div",{className:"md:hidden pb-4",children:(0,r.jsxs)("nav",{className:"space-y-4",children:[e.map(e=>(0,r.jsx)(n,{href:e.href,children:e.name},e.name)),(0,r.jsx)("div",{className:"pt-4",children:(0,r.jsx)(a.default,{locale:t})})]})})]})}},23399:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx","default")},23440:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx","default")},25536:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},32410:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A,generateMetadata:()=>C,generateStaticParams:()=>k});var r=s(37413),o=s(49521),i=s.n(o),l=s(7905),a=s.n(l),n=s(26920),c=s(83930),d=s(39916);s(61135);var m=s(23440),x=s(78878),u=s(64348),h=s(89780),p=s(80408),g=s(23399),f=s(12623),b=s(37062);let v=({children:e,href:t})=>(0,r.jsx)(x.N_,{href:t,className:"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors",children:e});async function y(){let e=await (0,u.A)("navigation"),t=await (0,h.A)(),s=[{name:e("home"),href:"/"},{name:e("tools"),href:"/tools"},{name:e("categories"),href:"/categories"},{name:e("submit"),href:"/submit"}];return(0,r.jsx)("header",{className:"bg-white px-4 shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,r.jsxs)(x.N_,{href:"/",className:"flex items-center space-x-2 hover:no-underline",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"zh"===t?"AI工具导航":"AI Tools"})]}),(0,r.jsx)("nav",{className:"hidden md:flex space-x-4",children:s.map(e=>(0,r.jsx)(v,{href:e.href,children:e.name},e.name))})]}),(0,r.jsx)("div",{className:"flex-1 max-w-md mx-8 hidden md:block",children:(0,r.jsx)(b.default,{locale:t})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(g.default,{currentLocale:t}),(0,r.jsx)(p.default,{}),(0,r.jsx)(f.default,{links:s,locale:t})]})]})})}async function j(){let e=await (0,u.A)("layout"),t=await (0,h.A)();return(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"AI"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"zh"===t?"AI工具导航":"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:e("footer.description")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:e("footer.quick_links")}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(x.N_,{href:"/tools",className:"text-gray-600 hover:text-blue-600",children:e("footer.tools_directory")})}),(0,r.jsx)("li",{children:(0,r.jsx)(x.N_,{href:"/categories",className:"text-gray-600 hover:text-blue-600",children:e("footer.browse_categories")})}),(0,r.jsx)("li",{children:(0,r.jsx)(x.N_,{href:"/submit",className:"text-gray-600 hover:text-blue-600",children:e("footer.submit_tool")})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:e("footer.support")}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:e("footer.help_center")})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:e("footer.contact_us")})}),(0,r.jsx)("li",{children:(0,r.jsx)("a",{href:"#",className:"text-gray-600 hover:text-blue-600",children:e("footer.privacy_policy")})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:e("footer.copyright")})})]})})}var w=s(50290),N=s(12688);async function k(){return N.IB.map(e=>({locale:e}))}async function C({params:e}){let{locale:t}=await e;return"zh"===t?{title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。",keywords:"AI工具,人工智能,AI导航,机器学习工具,深度学习,自动化工具,AI应用",authors:[{name:"AI工具导航团队"}],creator:"AI工具导航",publisher:"AI工具导航",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"zh_CN",url:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",siteName:"AI工具导航",title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"AI工具导航 - 发现最好的人工智能工具"}]},twitter:{card:"summary_large_image",title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。",images:["/og-image.jpg"]},alternates:{canonical:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",languages:{zh:"/",en:"/en"}},verification:{google:"your-google-verification-code"}}:{title:"AI Tools Directory - Discover the Best AI Tools",description:"Explore curated collection of AI tools to boost your productivity and creativity. From text generation to image creation, find the perfect AI tool for your needs.",keywords:"AI tools,artificial intelligence,AI directory,machine learning tools,deep learning,automation tools,AI applications",authors:[{name:"AI Tools Directory Team"}],creator:"AI Tools Directory",publisher:"AI Tools Directory",robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",siteName:"AI Tools Directory",title:"AI Tools Directory - Discover the Best AI Tools",description:"Explore curated collection of AI tools to boost your productivity and creativity. From text generation to image creation, find the perfect AI tool for your needs.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"AI Tools Directory - Discover the Best AI Tools"}]},twitter:{card:"summary_large_image",title:"AI Tools Directory - Discover the Best AI Tools",description:"Explore curated collection of AI tools to boost your productivity and creativity.",images:["/og-image.jpg"]},alternates:{canonical:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",languages:{zh:"/",en:"/en"}},verification:{google:"your-google-verification-code"}}}async function A({children:e,params:t}){let{locale:s}=await t;N.IB.includes(s)||(0,d.notFound)();let o=await (0,c.A)();return(0,r.jsx)(n.A,{messages:o,children:(0,r.jsx)(m.default,{children:(0,r.jsx)(w.LikeProvider,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[(0,r.jsx)(y,{}),(0,r.jsx)("main",{className:`${i().variable} ${a().variable} antialiased flex-1`,children:e}),(0,r.jsx)(j,{})]})})})})}},37062:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx","default")},39254:(e,t,s)=>{"use strict";s.d(t,{default:()=>m});var r=s(60687),o=s(43210),i=s(82136),l=s(23877),a=s(12340),n=s(77618),c=s(78521),d=s(48577);function m(){let{data:e,status:t}=(0,i.useSession)(),s=(0,a.rd)(),[m,x]=(0,o.useState)(!1),[u,h]=(0,o.useState)(!1),p=(0,n.c3)("common");(0,c.Ym)();let g=async()=>{await (0,i.signOut)({callbackUrl:"/"})},f=e=>{h(!1),s.push(e)};return"loading"===t?(0,r.jsx)("button",{className:"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse",disabled:!0,children:p("loading")}):e?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors",onClick:()=>h(!u),children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:e.user?.image?(0,r.jsx)("img",{src:e.user.image,alt:e.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:e.user?.name?.charAt(0)||"U"})}),(0,r.jsx)("span",{className:"text-sm hidden md:block",children:e.user?.name}),(0,r.jsx)(l.Vr3,{className:`text-xs transition-transform ${u?"rotate-180":""}`})]}),u&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>h(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20",children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:e.user?.image?(0,r.jsx)("img",{src:e.user.image,alt:e.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-lg font-medium text-gray-600",children:e.user?.name?.charAt(0)||"U"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.user?.name}),(0,r.jsx)("p",{className:"text-gray-500 text-xs",children:e.user?.email}),e.user?.role==="admin"&&(0,r.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:p("admin")})]})]})}),(0,r.jsxs)("div",{className:"py-2",children:[(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>f("/profile"),children:[(0,r.jsx)(l.x$1,{}),p("profile")]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>f("/profile/submitted"),children:[(0,r.jsx)(l.svy,{}),p("my_submissions")]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>f("/profile/liked"),children:[(0,r.jsx)(l.Mbv,{}),p("my_favorites")]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>f("/submit"),children:[(0,r.jsx)(l.OiG,{}),p("submit_tool")]})]}),e.user?.role==="admin"&&(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{className:"border-t py-2",children:(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>f("/admin"),children:[(0,r.jsx)(l.Pcn,{}),p("admin_panel")]})})}),(0,r.jsxs)("div",{className:"border-t py-2",children:[(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>f("/settings"),children:[(0,r.jsx)(l.Pcn,{}),p("settings")]}),(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",onClick:g,children:[(0,r.jsx)(l.axc,{}),p("logout")]})]})]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors",onClick:()=>x(!0),children:[(0,r.jsx)(l.Zu,{}),p("login")]}),(0,r.jsx)(d.A,{isOpen:m,onClose:()=>x(!1)})]})}},45304:()=>{},48577:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(60687),o=s(43210),i=s(82136),l=s(12340),a=s(77618),n=s(78521),c=s(23877);function d({isOpen:e,onClose:t}){let[s,d]=(0,o.useState)("method"),[m,x]=(0,o.useState)(""),[u,h]=(0,o.useState)(""),[p,g]=(0,o.useState)(!1),[f,b]=(0,o.useState)("");(0,l.a8)();let v=(0,a.c3)("auth");(0,n.Ym)();let y=(e,t="success")=>{let s=document.createElement("div");s.className=`fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${"success"===t?"bg-green-500":"bg-red-500"}`,s.textContent=e,document.body.appendChild(s),setTimeout(()=>document.body.removeChild(s),3e3)},j=()=>{d("method"),x(""),h(""),b(""),t()},w=async e=>{try{g(!0),await (0,i.signIn)(e,{callbackUrl:"/"})}catch(e){y(v("login_failed"),"error")}finally{g(!1)}},N=async()=>{if(!m)return void b(v("email_required"));if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(m))return void b(v("email_invalid"));b(""),g(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:m})}),t=await e.json();t.success?(h(t.token),d("code"),y(v("verification_sent"))):y(t.error||v("send_failed"),"error")}catch(e){y(v("network_error"),"error")}finally{g(!1)}},k=async e=>{if(6===e.length){g(!0);try{let t=await (0,i.signIn)("email-code",{email:m,code:e,token:u,redirect:!1});t?.ok?(y(v("login_success")),j()):y(t?.error||v("verification_error"),"error")}catch(e){y(v("network_error"),"error")}finally{g(!1)}}},C=(e,t)=>{if(t.length>1)return;let s=document.querySelectorAll(".code-input");s[e].value=t,t&&e<5&&s[e+1]?.focus();let r=Array.from(s).map(e=>e.value).join("");6===r.length&&k(r)};return e?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:j}),(0,r.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===s&&v("login_title"),"email"===s&&v("email_login_title"),"code"===s&&v("verification_title")]}),(0,r.jsx)("button",{onClick:j,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(c.QCr,{})})]}),(0,r.jsxs)("div",{className:"p-6",children:["method"===s&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:v("choose_method")}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>w("google"),disabled:p,children:[(0,r.jsx)(c.DSS,{}),v("google_login")]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>w("github"),disabled:p,children:[(0,r.jsx)(c.hL4,{}),v("github_login")]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:v("or")})})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>d("email"),children:[(0,r.jsx)(c.maD,{}),v("email_login")]})]}),"email"===s&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:v("email_instruction")}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:v("email_address")}),(0,r.jsx)("input",{type:"email",value:m,onChange:e=>x(e.target.value),placeholder:v("email_placeholder"),onKeyPress:e=>"Enter"===e.key&&N(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),f&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:N,disabled:p,children:p?v("sending"):v("send_code")}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>d("method"),children:v("back")})]})]}),"code"===s&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:v("verification_instruction",{email:m})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:v("verification_code")}),(0,r.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,r.jsx)("input",{type:"text",maxLength:1,onChange:t=>C(e,t.target.value),disabled:p,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>d("email"),children:v("resend_code")}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>d("method"),children:v("back")})]})]})]})]})]}):null}},50290:(e,t,s)=>{"use strict";s.d(t,{LikeProvider:()=>o});var r=s(12907);let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call LikeProvider() from the server but LikeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx","LikeProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useLike() from the server but useLike is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx","useLike")},51188:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(60687),o=s(12340),i=s(77618),l=s(23877);function a({locale:e,className:t=""}){let s=(0,o.rd)(),a=(0,i.c3)("navigation");return(0,r.jsx)("form",{onSubmit:e=>{e.preventDefault();let t=new FormData(e.currentTarget).get("search");t.trim()&&s.push(`/search?q=${encodeURIComponent(t.trim())}`)},className:t,children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(l.KSO,{className:"text-gray-400"})}),(0,r.jsx)("input",{name:"search",type:"text",placeholder:a("search_placeholder"),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})}},61135:()=>{},61984:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72785:(e,t,s)=>{Promise.resolve().then(s.bind(s,39130)),Promise.resolve().then(s.bind(s,45196)),Promise.resolve().then(s.bind(s,39254)),Promise.resolve().then(s.bind(s,84541)),Promise.resolve().then(s.bind(s,22017)),Promise.resolve().then(s.bind(s,51188)),Promise.resolve().then(s.bind(s,76242)),Promise.resolve().then(s.bind(s,2328))},76242:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(60687),o=s(82136);function i({children:e}){return(0,r.jsx)(o.SessionProvider,{children:e})}},78878:(e,t,s)=>{"use strict";s.d(t,{N_:()=>l,V2:()=>a});var r=s(55946),o=s(92118);let i=(0,r.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:l,redirect:a,usePathname:n,useRouter:c}=(0,o.A)(i)},80408:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx","default")},82513:(e,t,s)=>{Promise.resolve().then(s.bind(s,75788)),Promise.resolve().then(s.bind(s,80994)),Promise.resolve().then(s.bind(s,80408)),Promise.resolve().then(s.bind(s,23399)),Promise.resolve().then(s.bind(s,12623)),Promise.resolve().then(s.bind(s,37062)),Promise.resolve().then(s.bind(s,23440)),Promise.resolve().then(s.bind(s,50290))},84541:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(60687),o=s(43210),i=s(12340),l=s(23877);let a=["en","zh"],n={zh:"中文",en:"English"};function c({currentLocale:e}){let[t,s]=(0,o.useState)(!1),c=(0,i.a8)(),d=(0,i.rd)(),m=e=>{d.replace(c,{locale:e}),s(!1)};return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>s(!t),className:"flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors",children:[(0,r.jsx)(l.f35,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:n[e]})]}),t&&(0,r.jsx)("div",{className:"absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50",children:a.map(t=>(0,r.jsx)("button",{onClick:()=>m(t),className:`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${t===e?"bg-blue-50 text-blue-600":"text-gray-700"}`,children:n[t]},t))})]})}},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(37413),o=s(7339),i=s.n(o);function l({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:i().className,children:e})})}}};