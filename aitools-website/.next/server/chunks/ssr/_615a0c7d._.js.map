{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts"], "sourcesContent": ["// 支持的语言列表\nexport const locales = ['en', 'zh'] as const;\nexport type Locale = (typeof locales)[number];\n\n// 默认语言\nexport const defaultLocale: Locale = 'en';\n\n// 语言显示名称\nexport const localeNames: Record<Locale, string> = {\n  zh: '中文',\n  en: 'English',\n};\n\n// 验证语言是否有效\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;;;;AACH,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,gBAAwB;AAG9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { locales, defaultLocale, type Locale } from './config';\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as Locale)) {\n    locale = defaultLocale;\n  }\n\n  return {\n    locale,\n    messages: (await import(`./messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAEe,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,qHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAmB;QACvC,SAAS,qHAAA,CAAA,gBAAa;IACxB;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\nimport { createNavigation } from 'next-intl/navigation';\n\nexport const routing = defineRouting({\n  // 支持的语言列表\n  locales: ['en', 'zh'],\n  \n  // 默认语言\n  defaultLocale: 'en',\n  \n  // 语言前缀配置 - 始终显示语言前缀\n  localePrefix: 'always',\n  \n  // 启用语言检测\n  localeDetection: true,\n  \n  // 启用备用链接\n  alternateLinks: true,\n  \n  // 语言 cookie 配置\n  localeCookie: {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    maxAge: 60 * 60 * 24 * 365 // 1 year\n  }\n});\n\n// 轻量级的包装器，围绕 Next.js 的导航 API\n// 它们将自动处理用户的语言环境\nexport const { Link, redirect, usePathname, useRouter } = createNavigation(routing);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,kOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,UAAU;IACV,SAAS;QAAC;QAAM;KAAK;IAErB,OAAO;IACP,eAAe;IAEf,oBAAoB;IACpB,cAAc;IAEd,SAAS;IACT,iBAAiB;IAEjB,SAAS;IACT,gBAAgB;IAEhB,eAAe;IACf,cAAc;QACZ,MAAM;QACN,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK,IAAI,SAAS;IACtC;AACF;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8PAAA,CAAA,mBAAgB,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx"], "sourcesContent": ["import { redirect } from '@/i18n/routing';\n\n// Root page that redirects to the default locale\nexport default function RootPage() {\n  // Redirect to the home page, middleware will handle locale detection\n  redirect({ href: '/', locale: 'en' });\n}"], "names": [], "mappings": ";;;AAAA;;AAGe,SAAS;IACtB,qEAAqE;IACrE,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAK,QAAQ;IAAK;AACrC", "debugId": null}}]}