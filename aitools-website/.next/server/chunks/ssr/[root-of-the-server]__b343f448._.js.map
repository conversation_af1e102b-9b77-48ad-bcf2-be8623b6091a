{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { SessionProvider as NextAuthSessionProvider } from 'next-auth/react';\nimport { ReactNode } from 'react';\n\ninterface SessionProviderProps {\n  children: ReactNode;\n}\n\nexport default function SessionProvider({ children }: SessionProviderProps) {\n  return (\n    <NextAuthSessionProvider>\n      {children}\n    </NextAuthSessionProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,qBACE,8OAAC,8IAAA,CAAA,kBAAuB;kBACrB;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { signIn } from 'next-auth/react';\nimport { usePathname } from 'next/navigation';\nimport { useTranslations, useLocale } from 'next-intl';\nimport { FaGoogle, FaGithub, FaEnvelope, FaTimes } from 'react-icons/fa';\nimport { Locale } from '@/i18n/config';\n\ninterface LoginModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\ntype LoginStep = 'method' | 'email' | 'code';\n\nexport default function LoginModal({ isOpen, onClose }: LoginModalProps) {\n  const [step, setStep] = useState<LoginStep>('method');\n  const [email, setEmail] = useState('');\n  const [verificationToken, setVerificationToken] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [emailError, setEmailError] = useState('');\n\n  const pathname = usePathname();\n  const t = useTranslations('auth');\n  const locale = useLocale() as Locale;\n\n  const showToast = (message: string, type: 'success' | 'error' = 'success') => {\n    // Simple toast implementation\n    const toast = document.createElement('div');\n    toast.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${\n      type === 'success' ? 'bg-green-500' : 'bg-red-500'\n    }`;\n    toast.textContent = message;\n    document.body.appendChild(toast);\n    setTimeout(() => document.body.removeChild(toast), 3000);\n  };\n\n  const handleClose = () => {\n    setStep('method');\n    setEmail('');\n    setVerificationToken('');\n    setEmailError('');\n    onClose();\n  };\n\n  const handleOAuthLogin = async (provider: 'google' | 'github') => {\n    try {\n      setIsLoading(true);\n      await signIn(provider, { callbackUrl: '/' });\n    } catch (error) {\n      showToast(t('login_failed'), 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleEmailSubmit = async () => {\n    if (!email) {\n      setEmailError(t('email_required'));\n      return;\n    }\n\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(email)) {\n      setEmailError(t('email_invalid'));\n      return;\n    }\n\n    setEmailError('');\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('/api/auth/send-code', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setVerificationToken(data.token);\n        setStep('code');\n        showToast(t('verification_sent'));\n      } else {\n        showToast(data.error || t('send_failed'), 'error');\n      }\n    } catch (error) {\n      showToast(t('network_error'), 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleCodeVerify = async (code: string) => {\n    if (code.length !== 6) return;\n\n    setIsLoading(true);\n\n    try {\n      const result = await signIn('email-code', {\n        email,\n        code,\n        token: verificationToken,\n        redirect: false,\n      });\n\n      if (result?.ok) {\n        showToast(t('login_success'));\n        handleClose();\n        // NextAuth会自动更新session，不需要手动刷新页面\n      } else {\n        showToast(result?.error || t('verification_error'), 'error');\n      }\n    } catch (error) {\n      showToast(t('network_error'), 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const renderMethodStep = () => (\n    <div className=\"space-y-4\">\n      <p className=\"text-gray-600 text-center\">\n        {t('choose_method')}\n      </p>\n\n      <div className=\"space-y-3\">\n        <button\n          className=\"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50\"\n          onClick={() => handleOAuthLogin('google')}\n          disabled={isLoading}\n        >\n          <FaGoogle />\n          {t('google_login')}\n        </button>\n\n        <button\n          className=\"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50\"\n          onClick={() => handleOAuthLogin('github')}\n          disabled={isLoading}\n        >\n          <FaGithub />\n          {t('github_login')}\n        </button>\n      </div>\n\n      <div className=\"relative\">\n        <div className=\"absolute inset-0 flex items-center\">\n          <div className=\"w-full border-t border-gray-300\" />\n        </div>\n        <div className=\"relative flex justify-center text-sm\">\n          <span className=\"px-2 bg-white text-gray-500\">{t('or')}</span>\n        </div>\n      </div>\n\n      <button\n        className=\"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors\"\n        onClick={() => setStep('email')}\n      >\n        <FaEnvelope />\n        {t('email_login')}\n      </button>\n    </div>\n  );\n\n  const renderEmailStep = () => (\n    <div className=\"space-y-4\">\n      <p className=\"text-gray-600 text-center\">\n        {t('email_instruction')}\n      </p>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          {t('email_address')}\n        </label>\n        <input\n          type=\"email\"\n          value={email}\n          onChange={(e) => setEmail(e.target.value)}\n          placeholder={t('email_placeholder')}\n          onKeyPress={(e) => e.key === 'Enter' && handleEmailSubmit()}\n          className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n        />\n        {emailError && (\n          <p className=\"mt-1 text-sm text-red-600\">{emailError}</p>\n        )}\n      </div>\n\n      <div className=\"space-y-3\">\n        <button\n          className=\"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50\"\n          onClick={handleEmailSubmit}\n          disabled={isLoading}\n        >\n          {isLoading ? t('sending') : t('send_code')}\n        </button>\n\n        <button\n          className=\"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n          onClick={() => setStep('method')}\n        >\n          {t('back')}\n        </button>\n      </div>\n    </div>\n  );\n\n  const handleCodeInputChange = (index: number, value: string) => {\n    if (value.length > 1) return;\n\n    const inputs = document.querySelectorAll('.code-input') as NodeListOf<HTMLInputElement>;\n    inputs[index].value = value;\n\n    // Auto-focus next input\n    if (value && index < 5) {\n      inputs[index + 1]?.focus();\n    }\n\n    // Check if all inputs are filled\n    const code = Array.from(inputs).map(input => input.value).join('');\n    if (code.length === 6) {\n      handleCodeVerify(code);\n    }\n  };\n\n  const renderCodeStep = () => (\n    <div className=\"space-y-4\">\n      <p className=\"text-gray-600 text-center\">\n        {t('verification_instruction', { email })}\n      </p>\n\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          {t('verification_code')}\n        </label>\n        <div className=\"flex justify-center gap-2\">\n          {[0, 1, 2, 3, 4, 5].map((index) => (\n            <input\n              key={index}\n              type=\"text\"\n              maxLength={1}\n              onChange={(e) => handleCodeInputChange(index, e.target.value)}\n              disabled={isLoading}\n              className=\"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50\"\n            />\n          ))}\n        </div>\n      </div>\n\n      <div className=\"space-y-3\">\n        <button\n          className=\"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors\"\n          onClick={() => setStep('email')}\n        >\n          {t('resend_code')}\n        </button>\n\n        <button\n          className=\"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\"\n          onClick={() => setStep('method')}\n        >\n          {t('back')}\n        </button>\n      </div>\n    </div>\n  );\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Overlay */}\n      <div\n        className=\"absolute inset-0 bg-black bg-opacity-50\"\n        onClick={handleClose}\n      />\n\n      {/* Modal */}\n      <div className=\"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b\">\n          <h2 className=\"text-xl font-semibold text-gray-900 text-center flex-1\">\n            {step === 'method' && t('login_title')}\n            {step === 'email' && t('email_login_title')}\n            {step === 'code' && t('verification_title')}\n          </h2>\n          <button\n            onClick={handleClose}\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <FaTimes />\n          </button>\n        </div>\n\n        {/* Body */}\n        <div className=\"p-6\">\n          {step === 'method' && renderMethodStep()}\n          {step === 'email' && renderEmailStep()}\n          {step === 'code' && renderCodeStep()}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AANA;;;;;;;AAgBe,SAAS,WAAW,EAAE,MAAM,EAAE,OAAO,EAAmB;IACrE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IAC5C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,YAAY,CAAC,SAAiB,OAA4B,SAAS;QACvE,8BAA8B;QAC9B,MAAM,QAAQ,SAAS,aAAa,CAAC;QACrC,MAAM,SAAS,GAAG,CAAC,mDAAmD,EACpE,SAAS,YAAY,iBAAiB,cACtC;QACF,MAAM,WAAW,GAAG;QACpB,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,WAAW,IAAM,SAAS,IAAI,CAAC,WAAW,CAAC,QAAQ;IACrD;IAEA,MAAM,cAAc;QAClB,QAAQ;QACR,SAAS;QACT,qBAAqB;QACrB,cAAc;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,aAAa;YACb,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAI;QAC5C,EAAE,OAAO,OAAO;YACd,UAAU,EAAE,iBAAiB;QAC/B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,OAAO;YACV,cAAc,EAAE;YAChB;QACF;QAEA,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,cAAc,EAAE;YAChB;QACF;QAEA,cAAc;QACd,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,qBAAqB,KAAK,KAAK;gBAC/B,QAAQ;gBACR,UAAU,EAAE;YACd,OAAO;gBACL,UAAU,KAAK,KAAK,IAAI,EAAE,gBAAgB;YAC5C;QACF,EAAE,OAAO,OAAO;YACd,UAAU,EAAE,kBAAkB;QAChC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,KAAK,MAAM,KAAK,GAAG;QAEvB,aAAa;QAEb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,cAAc;gBACxC;gBACA;gBACA,OAAO;gBACP,UAAU;YACZ;YAEA,IAAI,QAAQ,IAAI;gBACd,UAAU,EAAE;gBACZ;YACA,iCAAiC;YACnC,OAAO;gBACL,UAAU,QAAQ,SAAS,EAAE,uBAAuB;YACtD;QACF,EAAE,OAAO,OAAO;YACd,UAAU,EAAE,kBAAkB;QAChC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB,kBACvB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;8BAGL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,iBAAiB;4BAChC,UAAU;;8CAEV,8OAAC,8IAAA,CAAA,WAAQ;;;;;gCACR,EAAE;;;;;;;sCAGL,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,iBAAiB;4BAChC,UAAU;;8CAEV,8OAAC,8IAAA,CAAA,WAAQ;;;;;gCACR,EAAE;;;;;;;;;;;;;8BAIP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA+B,EAAE;;;;;;;;;;;;;;;;;8BAIrD,8OAAC;oBACC,WAAU;oBACV,SAAS,IAAM,QAAQ;;sCAEvB,8OAAC,8IAAA,CAAA,aAAU;;;;;wBACV,EAAE;;;;;;;;;;;;;IAKT,MAAM,kBAAkB,kBACtB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BACV,EAAE;;;;;;8BAGL,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCACd,EAAE;;;;;;sCAEL,8OAAC;4BACC,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,aAAa,EAAE;4BACf,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;4BACxC,WAAU;;;;;;wBAEX,4BACC,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAI9C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,SAAS;4BACT,UAAU;sCAET,YAAY,EAAE,aAAa,EAAE;;;;;;sCAGhC,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,QAAQ;sCAEtB,EAAE;;;;;;;;;;;;;;;;;;IAMX,MAAM,wBAAwB,CAAC,OAAe;QAC5C,IAAI,MAAM,MAAM,GAAG,GAAG;QAEtB,MAAM,SAAS,SAAS,gBAAgB,CAAC;QACzC,MAAM,CAAC,MAAM,CAAC,KAAK,GAAG;QAEtB,wBAAwB;QACxB,IAAI,SAAS,QAAQ,GAAG;YACtB,MAAM,CAAC,QAAQ,EAAE,EAAE;QACrB;QAEA,iCAAiC;QACjC,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAA,QAAS,MAAM,KAAK,EAAE,IAAI,CAAC;QAC/D,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB,kBACrB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BACV,EAAE,4BAA4B;wBAAE;oBAAM;;;;;;8BAGzC,8OAAC;;sCACC,8OAAC;4BAAM,WAAU;sCACd,EAAE;;;;;;sCAEL,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,sBACvB,8OAAC;oCAEC,MAAK;oCACL,WAAW;oCACX,UAAU,CAAC,IAAM,sBAAsB,OAAO,EAAE,MAAM,CAAC,KAAK;oCAC5D,UAAU;oCACV,WAAU;mCALL;;;;;;;;;;;;;;;;8BAWb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,QAAQ;sCAEtB,EAAE;;;;;;sCAGL,8OAAC;4BACC,WAAU;4BACV,SAAS,IAAM,QAAQ;sCAEtB,EAAE;;;;;;;;;;;;;;;;;;IAMX,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCACX,SAAS,YAAY,EAAE;oCACvB,SAAS,WAAW,EAAE;oCACtB,SAAS,UAAU,EAAE;;;;;;;0CAExB,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;kCAKZ,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,YAAY;4BACrB,SAAS,WAAW;4BACpB,SAAS,UAAU;;;;;;;;;;;;;;;;;;;AAK9B", "debugId": null}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useSession, signOut } from 'next-auth/react';\nimport { Fa<PERSON>ser, FaHeart, FaPlus, FaCog, FaSignOutAlt, FaSignInAlt, FaChevronDown, FaList } from 'react-icons/fa';\nimport { useRouter } from 'next/navigation';\nimport { useTranslations, useLocale } from 'next-intl';\nimport LoginModal from './LoginModal';\nimport { Locale } from '@/i18n/config';\n\nexport default function UserMenuClient() {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const t = useTranslations('common');\n  const locale = useLocale() as Locale;\n\n  // Generate localized href\n  const getLocalizedHref = (path: string) => {\n    return locale === 'zh' ? path : `/en${path}`;\n  };\n\n  const handleSignOut = async () => {\n    await signOut({ callbackUrl: '/' });\n  };\n\n  const handleNavigation = (path: string) => {\n    setIsMenuOpen(false);\n    router.push(getLocalizedHref(path));\n  };\n\n  // 如果正在加载，显示加载状态\n  if (status === 'loading') {\n    return (\n      <button\n        className=\"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse\"\n        disabled\n      >\n        {t('loading')}\n      </button>\n    );\n  }\n\n  // 如果未登录，显示登录按钮\n  if (!session) {\n    return (\n      <>\n        <button\n          className=\"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors\"\n          onClick={() => setIsLoginModalOpen(true)}\n        >\n          <FaSignInAlt />\n          {t('login')}\n        </button>\n        <LoginModal isOpen={isLoginModalOpen} onClose={() => setIsLoginModalOpen(false)} />\n      </>\n    );\n  }\n\n  // 如果已登录，显示用户菜单\n  return (\n    <div className=\"relative\">\n      <button\n        className=\"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors\"\n        onClick={() => setIsMenuOpen(!isMenuOpen)}\n      >\n        <div className=\"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\">\n          {session.user?.image ? (\n            <img\n              src={session.user.image}\n              alt={session.user.name || ''}\n              className=\"w-full h-full object-cover\"\n            />\n          ) : (\n            <span className=\"text-sm font-medium text-gray-600\">\n              {session.user?.name?.charAt(0) || 'U'}\n            </span>\n          )}\n        </div>\n        <span className=\"text-sm hidden md:block\">\n          {session.user?.name}\n        </span>\n        <FaChevronDown className={`text-xs transition-transform ${isMenuOpen ? 'rotate-180' : ''}`} />\n      </button>\n\n      {isMenuOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsMenuOpen(false)}\n          />\n\n          {/* Menu */}\n          <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20\">\n            {/* 用户信息 */}\n            <div className=\"p-4 border-b\">\n              <div className=\"flex items-center gap-3\">\n                <div className=\"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden\">\n                  {session.user?.image ? (\n                    <img\n                      src={session.user.image}\n                      alt={session.user.name || ''}\n                      className=\"w-full h-full object-cover\"\n                    />\n                  ) : (\n                    <span className=\"text-lg font-medium text-gray-600\">\n                      {session.user?.name?.charAt(0) || 'U'}\n                    </span>\n                  )}\n                </div>\n                <div>\n                  <p className=\"font-medium text-sm\">\n                    {session.user?.name}\n                  </p>\n                  <p className=\"text-gray-500 text-xs\">\n                    {session.user?.email}\n                  </p>\n                  {session.user?.role === 'admin' && (\n                    <span className=\"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded\">\n                      {t('admin')}\n                    </span>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* 用户功能 */}\n            <div className=\"py-2\">\n              <button\n                className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                onClick={() => handleNavigation('/profile')}\n              >\n                <FaUser />\n                {t('profile')}\n              </button>\n\n              <button\n                className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                onClick={() => handleNavigation('/profile/submitted')}\n              >\n                <FaList />\n                {t('my_submissions')}\n              </button>\n\n              <button\n                className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                onClick={() => handleNavigation('/profile/liked')}\n              >\n                <FaHeart />\n                {t('my_favorites')}\n              </button>\n\n              <button\n                className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                onClick={() => handleNavigation('/submit')}\n              >\n                <FaPlus />\n                {t('submit_tool')}\n              </button>\n            </div>\n\n            {/* 管理员功能 */}\n            {session.user?.role === 'admin' && (\n              <>\n                <div className=\"border-t py-2\">\n                  <button\n                    className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                    onClick={() => handleNavigation('/admin')}\n                  >\n                    <FaCog />\n                    {t('admin_panel')}\n                  </button>\n                </div>\n              </>\n            )}\n\n            {/* 设置和登出 */}\n            <div className=\"border-t py-2\">\n              <button\n                className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                onClick={() => handleNavigation('/settings')}\n              >\n                <FaCog />\n                {t('settings')}\n              </button>\n\n              <button\n                className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors\"\n                onClick={handleSignOut}\n              >\n                <FaSignOutAlt />\n                {t('logout')}\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,YAAS,AAAD;IAEvB,0BAA0B;IAC1B,MAAM,mBAAmB,CAAC;QACxB,OAAO,WAAW,OAAO,OAAO,CAAC,GAAG,EAAE,MAAM;IAC9C;IAEA,MAAM,gBAAgB;QACpB,MAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;YAAE,aAAa;QAAI;IACnC;IAEA,MAAM,mBAAmB,CAAC;QACxB,cAAc;QACd,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA,gBAAgB;IAChB,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YACC,WAAU;YACV,QAAQ;sBAEP,EAAE;;;;;;IAGT;IAEA,eAAe;IACf,IAAI,CAAC,SAAS;QACZ,qBACE;;8BACE,8OAAC;oBACC,WAAU;oBACV,SAAS,IAAM,oBAAoB;;sCAEnC,8OAAC,8IAAA,CAAA,cAAW;;;;;wBACX,EAAE;;;;;;;8BAEL,8OAAC,wIAAA,CAAA,UAAU;oBAAC,QAAQ;oBAAkB,SAAS,IAAM,oBAAoB;;;;;;;;IAG/E;IAEA,eAAe;IACf,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,cAAc,CAAC;;kCAE9B,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,IAAI,EAAE,sBACb,8OAAC;4BACC,KAAK,QAAQ,IAAI,CAAC,KAAK;4BACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;4BAC1B,WAAU;;;;;iDAGZ,8OAAC;4BAAK,WAAU;sCACb,QAAQ,IAAI,EAAE,MAAM,OAAO,MAAM;;;;;;;;;;;kCAIxC,8OAAC;wBAAK,WAAU;kCACb,QAAQ,IAAI,EAAE;;;;;;kCAEjB,8OAAC,8IAAA,CAAA,gBAAa;wBAAC,WAAW,CAAC,6BAA6B,EAAE,aAAa,eAAe,IAAI;;;;;;;;;;;;YAG3F,4BACC;;kCAEE,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,cAAc;;;;;;kCAI/B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,IAAI,EAAE,sBACb,8OAAC;gDACC,KAAK,QAAQ,IAAI,CAAC,KAAK;gDACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;gDAC1B,WAAU;;;;;qEAGZ,8OAAC;gDAAK,WAAU;0DACb,QAAQ,IAAI,EAAE,MAAM,OAAO,MAAM;;;;;;;;;;;sDAIxC,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DACV,QAAQ,IAAI,EAAE;;;;;;8DAEjB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,IAAI,EAAE;;;;;;gDAEhB,QAAQ,IAAI,EAAE,SAAS,yBACtB,8OAAC;oDAAK,WAAU;8DACb,EAAE;;;;;;;;;;;;;;;;;;;;;;;0CAQb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB;;0DAEhC,8OAAC,8IAAA,CAAA,SAAM;;;;;4CACN,EAAE;;;;;;;kDAGL,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB;;0DAEhC,8OAAC,8IAAA,CAAA,SAAM;;;;;4CACN,EAAE;;;;;;;kDAGL,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB;;0DAEhC,8OAAC,8IAAA,CAAA,UAAO;;;;;4CACP,EAAE;;;;;;;kDAGL,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB;;0DAEhC,8OAAC,8IAAA,CAAA,SAAM;;;;;4CACN,EAAE;;;;;;;;;;;;;4BAKN,QAAQ,IAAI,EAAE,SAAS,yBACtB;0CACE,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB;;0DAEhC,8OAAC,8IAAA,CAAA,QAAK;;;;;4CACL,EAAE;;;;;;;;;;;;;0CAOX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB;;0DAEhC,8OAAC,8IAAA,CAAA,QAAK;;;;;4CACL,EAAE;;;;;;;kDAGL,8OAAC;wCACC,WAAU;wCACV,SAAS;;0DAET,8OAAC,8IAAA,CAAA,eAAY;;;;;4CACZ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB", "debugId": null}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\n\n// 支持的语言列表\nexport const locales = ['en', 'zh'] as const;\nexport type Locale = (typeof locales)[number];\n\n// 默认语言\nexport const defaultLocale: Locale = 'en';\n\n// 语言显示名称\nexport const localeNames: Record<Locale, string> = {\n  zh: '中文',\n  en: 'English',\n};\n\n// 验证语言是否有效\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n\n// Next-intl 配置\nexport default getRequestConfig(async ({ locale }) => {\n  // 验证传入的语言是否有效\n  if (!isValidLocale(locale)) {\n    notFound();\n  }\n\n  return {\n    messages: (await import(`./messages/${locale}.json`)).default,\n  };\n});\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAGO,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,gBAAwB;AAG9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B;uCAGe,CAAA,GAAA,gMAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,cAAc;IACd,IAAI,CAAC,cAAc,SAAS;QAC1B,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACT;IAEA,OAAO;QACL,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 975, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { FaGlobe } from 'react-icons/fa';\nimport { locales, localeNames, type Locale } from '@/i18n/config';\n\ninterface LanguageSwitcherClientProps {\n  currentLocale: Locale;\n}\n\nexport default function LanguageSwitcherClient({ currentLocale }: LanguageSwitcherClientProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const pathname = usePathname();\n  const router = useRouter();\n\n  const switchLanguage = (newLocale: Locale) => {\n    // Simple approach: just navigate to the new locale root\n    const newPath = `/${newLocale}`;\n    router.push(newPath);\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors\"\n      >\n        <FaGlobe className=\"w-4 h-4\" />\n        <span className=\"text-sm\">{localeNames[currentLocale]}</span>\n      </button>\n\n      {isOpen && (\n        <div className=\"absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50\">\n          {locales.map((locale) => (\n            <button\n              key={locale}\n              onClick={() => switchLanguage(locale)}\n              className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${\n                locale === currentLocale ? 'bg-blue-50 text-blue-600' : 'text-gray-700'\n              }`}\n            >\n              {localeNames[locale]}\n            </button>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAWe,SAAS,uBAAuB,EAAE,aAAa,EAA+B;IAC3F,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,iBAAiB,CAAC;QACtB,wDAAwD;QACxD,MAAM,UAAU,CAAC,CAAC,EAAE,WAAW;QAC/B,OAAO,IAAI,CAAC;QACZ,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC,8IAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAAW,qHAAA,CAAA,cAAW,CAAC,cAAc;;;;;;;;;;;;YAGtD,wBACC,8OAAC;gBAAI,WAAU;0BACZ,qHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,uBACZ,8OAAC;wBAEC,SAAS,IAAM,eAAe;wBAC9B,WAAW,CAAC,6EAA6E,EACvF,WAAW,gBAAgB,6BAA6B,iBACxD;kCAED,qHAAA,CAAA,cAAW,CAAC,OAAO;uBANf;;;;;;;;;;;;;;;;AAanB", "debugId": null}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useTranslations } from 'next-intl';\nimport { FaSearch } from 'react-icons/fa';\n\ninterface SearchFormClientProps {\n  locale: string;\n  className?: string;\n}\n\nexport default function SearchFormClient({ locale, className = \"\" }: SearchFormClientProps) {\n  const router = useRouter();\n  const t = useTranslations('navigation');\n\n  // Generate localized href\n  const getLocalizedHref = (path: string) => {\n    return locale === 'zh' ? path : `/en${path}`;\n  };\n\n  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    const formData = new FormData(e.currentTarget);\n    const query = formData.get('search') as string;\n    if (query.trim()) {\n      router.push(getLocalizedHref(`/search?q=${encodeURIComponent(query.trim())}`));\n    }\n  };\n\n  return (\n    <form onSubmit={handleSearch} className={className}>\n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <FaSearch className=\"text-gray-400\" />\n        </div>\n        <input\n          name=\"search\"\n          type=\"text\"\n          placeholder={t('search_placeholder')}\n          className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n        />\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,iBAAiB,EAAE,MAAM,EAAE,YAAY,EAAE,EAAyB;IACxF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,IAAI,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,0BAA0B;IAC1B,MAAM,mBAAmB,CAAC;QACxB,OAAO,WAAW,OAAO,OAAO,CAAC,GAAG,EAAE,MAAM;IAC9C;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,MAAM,WAAW,IAAI,SAAS,EAAE,aAAa;QAC7C,MAAM,QAAQ,SAAS,GAAG,CAAC;QAC3B,IAAI,MAAM,IAAI,IAAI;YAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,mBAAmB,MAAM,IAAI,KAAK;QAC9E;IACF;IAEA,qBACE,8OAAC;QAAK,UAAU;QAAc,WAAW;kBACvC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,8IAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;8BAEtB,8OAAC;oBACC,MAAK;oBACL,MAAK;oBACL,aAAa,EAAE;oBACf,WAAU;;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport NextLink from 'next/link';\nimport { FaBars, FaTimes } from 'react-icons/fa';\nimport SearchFormClient from './SearchFormClient';\n\ninterface MobileMenuClientProps {\n  links: Array<{ name: string; href: string }>;\n  locale: string;\n}\n\nconst NavLink = ({ children, href }: { children: React.ReactNode; href: string }) => (\n  <NextLink\n    href={href}\n    className=\"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors\"\n  >\n    {children}\n  </NextLink>\n);\n\nexport default function MobileMenuClient({ links, locale }: MobileMenuClientProps) {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  return (\n    <>\n      {/* Mobile menu button */}\n      <button\n        className=\"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\n        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n        aria-label=\"Open Menu\"\n      >\n        {isMobileMenuOpen ? <FaTimes /> : <FaBars />}\n      </button>\n\n      {/* Mobile Navigation */}\n      {isMobileMenuOpen && (\n        <div className=\"md:hidden pb-4\">\n          <nav className=\"space-y-4\">\n            {links.map((link) => (\n              <NavLink key={link.name} href={link.href}>\n                {link.name}\n              </NavLink>\n            ))}\n\n            {/* Mobile Search */}\n            <div className=\"pt-4\">\n              <SearchFormClient locale={locale} />\n            </div>\n          </nav>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAYA,MAAM,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAA+C,iBAC9E,8OAAC,4JAAA,CAAA,UAAQ;QACP,MAAM;QACN,WAAU;kBAET;;;;;;AAIU,SAAS,iBAAiB,EAAE,KAAK,EAAE,MAAM,EAAyB;IAC/E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE;;0BAEE,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,oBAAoB,CAAC;gBACpC,cAAW;0BAEV,iCAAmB,8OAAC,8IAAA,CAAA,UAAO;;;;yCAAM,8OAAC,8IAAA,CAAA,SAAM;;;;;;;;;;YAI1C,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gCAAwB,MAAM,KAAK,IAAI;0CACrC,KAAK,IAAI;+BADE,KAAK,IAAI;;;;;sCAMzB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gJAAA,CAAA,UAAgB;gCAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 1223, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useCallback, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\n\n// 工具点赞状态接口\ninterface ToolLikeState {\n  liked: boolean;\n  likes: number;\n  loading: boolean;\n}\n\n// Context状态接口\ninterface LikeContextState {\n  // 工具点赞状态映射 toolId -> ToolLikeState\n  toolStates: Record<string, ToolLikeState>;\n  // 切换点赞状态\n  toggleLike: (toolId: string, forceUnlike?: boolean) => Promise<boolean>;\n  // 获取工具点赞状态\n  getToolState: (toolId: string) => ToolLikeState;\n  // 初始化工具状态\n  initializeToolState: (toolId: string, initialLikes: number, initialLiked?: boolean) => void;\n  // 刷新工具状态\n  refreshToolState: (toolId: string) => Promise<void>;\n}\n\n// 默认工具状态\nconst defaultToolState: ToolLikeState = {\n  liked: false,\n  likes: 0,\n  loading: false\n};\n\n// 创建Context\nconst LikeContext = createContext<LikeContextState | null>(null);\n\n// Provider组件\nexport function LikeProvider({ children }: { children: React.ReactNode }) {\n  const { data: session } = useSession();\n  const [toolStates, setToolStates] = useState<Record<string, ToolLikeState>>({});\n\n  // 获取工具状态\n  const getToolState = useCallback((toolId: string): ToolLikeState => {\n    return toolStates[toolId] || defaultToolState;\n  }, [toolStates]);\n\n  // 初始化工具状态\n  const initializeToolState = useCallback((toolId: string, initialLikes: number, initialLiked = false) => {\n    setToolStates(prev => {\n      // 如果已经存在状态，不覆盖（避免重复初始化）\n      if (prev[toolId]) {\n        return prev;\n      }\n      return {\n        ...prev,\n        [toolId]: {\n          liked: initialLiked,\n          likes: initialLikes,\n          loading: false\n        }\n      };\n    });\n  }, []);\n\n  // 刷新工具状态（从服务器获取最新状态）\n  const refreshToolState = useCallback(async (toolId: string) => {\n    if (!session) return;\n\n    try {\n      const response = await fetch(`/api/tools/${toolId}/like`);\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setToolStates(prev => ({\n            ...prev,\n            [toolId]: {\n              liked: data.data.liked,\n              likes: data.data.likes,\n              loading: false\n            }\n          }));\n        }\n      }\n    } catch (error) {\n      console.error('Failed to refresh tool state:', error);\n    }\n  }, [session]);\n\n  // 切换点赞状态\n  const toggleLike = useCallback(async (toolId: string, forceUnlike = false): Promise<boolean> => {\n    if (!session) {\n      return false; // 需要登录\n    }\n\n    // 设置加载状态\n    setToolStates(prev => ({\n      ...prev,\n      [toolId]: {\n        ...prev[toolId] || defaultToolState,\n        loading: true\n      }\n    }));\n\n    try {\n      const requestBody = forceUnlike ? { forceUnlike: true } : {};\n      \n      const response = await fetch(`/api/tools/${toolId}/like`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(requestBody),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          // 更新状态\n          setToolStates(prev => ({\n            ...prev,\n            [toolId]: {\n              liked: data.data.liked,\n              likes: data.data.likes,\n              loading: false\n            }\n          }));\n          return true; // 操作成功\n        }\n      }\n      \n      // 操作失败，恢复加载状态\n      setToolStates(prev => ({\n        ...prev,\n        [toolId]: {\n          ...prev[toolId] || defaultToolState,\n          loading: false\n        }\n      }));\n      return false;\n    } catch (error) {\n      console.error('Like request failed:', error);\n      // 操作失败，恢复加载状态\n      setToolStates(prev => ({\n        ...prev,\n        [toolId]: {\n          ...prev[toolId] || defaultToolState,\n          loading: false\n        }\n      }));\n      return false;\n    }\n  }, [session]);\n\n  // 当用户登录状态改变时，刷新所有已初始化的工具状态\n  useEffect(() => {\n    if (session) {\n      // 用户登录后，刷新所有工具状态\n      const toolIds = Object.keys(toolStates);\n      toolIds.forEach(toolId => {\n        refreshToolState(toolId);\n      });\n    } else {\n      // 用户登出后，重置所有点赞状态为false\n      setToolStates(prev => {\n        const newStates: Record<string, ToolLikeState> = {};\n        Object.keys(prev).forEach(toolId => {\n          newStates[toolId] = {\n            ...prev[toolId],\n            liked: false,\n            loading: false\n          };\n        });\n        return newStates;\n      });\n    }\n  }, [session]); // 移除refreshToolState依赖，避免无限循环\n\n  const contextValue: LikeContextState = {\n    toolStates,\n    toggleLike,\n    getToolState,\n    initializeToolState,\n    refreshToolState\n  };\n\n  return (\n    <LikeContext.Provider value={contextValue}>\n      {children}\n    </LikeContext.Provider>\n  );\n}\n\n// Hook for using the context\nexport function useLike() {\n  const context = useContext(LikeContext);\n  if (!context) {\n    throw new Error('useLike must be used within a LikeProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AA0BA,SAAS;AACT,MAAM,mBAAkC;IACtC,OAAO;IACP,OAAO;IACP,SAAS;AACX;AAEA,YAAY;AACZ,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA2B;AAGpD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC,CAAC;IAE7E,SAAS;IACT,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,OAAO,UAAU,CAAC,OAAO,IAAI;IAC/B,GAAG;QAAC;KAAW;IAEf,UAAU;IACV,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAgB,cAAsB,eAAe,KAAK;QACjG,cAAc,CAAA;YACZ,wBAAwB;YACxB,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,OAAO;YACT;YACA,OAAO;gBACL,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,OAAO;oBACP,OAAO;oBACP,SAAS;gBACX;YACF;QACF;IACF,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC;YACxD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc,CAAA,OAAQ,CAAC;4BACrB,GAAG,IAAI;4BACP,CAAC,OAAO,EAAE;gCACR,OAAO,KAAK,IAAI,CAAC,KAAK;gCACtB,OAAO,KAAK,IAAI,CAAC,KAAK;gCACtB,SAAS;4BACX;wBACF,CAAC;gBACH;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF,GAAG;QAAC;KAAQ;IAEZ,SAAS;IACT,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,QAAgB,cAAc,KAAK;QACvE,IAAI,CAAC,SAAS;YACZ,OAAO,OAAO,OAAO;QACvB;QAEA,SAAS;QACT,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,OAAO,EAAE;oBACR,GAAG,IAAI,CAAC,OAAO,IAAI,gBAAgB;oBACnC,SAAS;gBACX;YACF,CAAC;QAED,IAAI;YACF,MAAM,cAAc,cAAc;gBAAE,aAAa;YAAK,IAAI,CAAC;YAE3D,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,EAAE;gBACxD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,OAAO;oBACP,cAAc,CAAA,OAAQ,CAAC;4BACrB,GAAG,IAAI;4BACP,CAAC,OAAO,EAAE;gCACR,OAAO,KAAK,IAAI,CAAC,KAAK;gCACtB,OAAO,KAAK,IAAI,CAAC,KAAK;gCACtB,SAAS;4BACX;wBACF,CAAC;oBACD,OAAO,MAAM,OAAO;gBACtB;YACF;YAEA,cAAc;YACd,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,OAAO,EAAE;wBACR,GAAG,IAAI,CAAC,OAAO,IAAI,gBAAgB;wBACnC,SAAS;oBACX;gBACF,CAAC;YACD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,cAAc;YACd,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,OAAO,EAAE;wBACR,GAAG,IAAI,CAAC,OAAO,IAAI,gBAAgB;wBACnC,SAAS;oBACX;gBACF,CAAC;YACD,OAAO;QACT;IACF,GAAG;QAAC;KAAQ;IAEZ,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,iBAAiB;YACjB,MAAM,UAAU,OAAO,IAAI,CAAC;YAC5B,QAAQ,OAAO,CAAC,CAAA;gBACd,iBAAiB;YACnB;QACF,OAAO;YACL,uBAAuB;YACvB,cAAc,CAAA;gBACZ,MAAM,YAA2C,CAAC;gBAClD,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;oBACxB,SAAS,CAAC,OAAO,GAAG;wBAClB,GAAG,IAAI,CAAC,OAAO;wBACf,OAAO;wBACP,SAAS;oBACX;gBACF;gBACA,OAAO;YACT;QACF;IACF,GAAG;QAAC;KAAQ,GAAG,8BAA8B;IAE7C,MAAM,eAAiC;QACrC;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}