module.exports = {

"[project]/.next-internal/server/app/[locale]/submit/launch-date/[toolId]/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
    throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function dbConnect() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = dbConnect;
}}),
"[project]/src/models/User.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const AccountSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    provider: {
        type: String,
        required: true,
        enum: [
            'google',
            'github',
            'email'
        ]
    },
    providerId: {
        type: String,
        required: true
    },
    providerAccountId: {
        type: String,
        required: true
    },
    accessToken: String,
    refreshToken: String,
    expiresAt: Date
}, {
    _id: false
});
const UserSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    email: {
        type: String,
        required: [
            true,
            'Email is required'
        ],
        unique: true,
        trim: true,
        lowercase: true,
        validate: {
            validator: function(v) {
                return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
            },
            message: 'Please enter a valid email address'
        }
    },
    name: {
        type: String,
        required: [
            true,
            'Name is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Name cannot exceed 100 characters'
        ]
    },
    avatar: {
        type: String,
        trim: true
    },
    role: {
        type: String,
        required: true,
        enum: [
            'user',
            'admin'
        ],
        default: 'user'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    // 认证相关
    emailVerified: {
        type: Boolean,
        default: false
    },
    emailVerificationToken: {
        type: String,
        trim: true
    },
    emailVerificationExpires: {
        type: Date
    },
    // OAuth账户关联
    accounts: [
        AccountSchema
    ],
    // 用户行为
    submittedTools: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Tool'
        }
    ],
    likedTools: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Tool'
        }
    ],
    comments: [
        {
            type: __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"].Types.ObjectId,
            ref: 'Comment'
        }
    ],
    // 时间戳
    lastLoginAt: {
        type: Date
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes
UserSchema.index({
    email: 1
});
UserSchema.index({
    role: 1
});
UserSchema.index({
    emailVerificationToken: 1
});
UserSchema.index({
    'accounts.provider': 1,
    'accounts.providerAccountId': 1
});
// 实例方法
UserSchema.methods.addAccount = function(account) {
    // 检查是否已存在相同的账户
    const existingAccount = this.accounts.find((acc)=>acc.provider === account.provider && acc.providerAccountId === account.providerAccountId);
    if (!existingAccount) {
        this.accounts.push(account);
    } else {
        // 更新现有账户信息
        Object.assign(existingAccount, account);
    }
};
UserSchema.methods.removeAccount = function(provider, providerAccountId) {
    this.accounts = this.accounts.filter((acc)=>!(acc.provider === provider && acc.providerAccountId === providerAccountId));
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.User || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('User', UserSchema);
}}),
"[project]/src/lib/auth.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/google.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/github.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const authOptions = {
    // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略
    // adapter: MongoDBAdapter(client),
    // 动态配置基础URL，支持不同环境
    // NextAuth会自动从环境变量或请求头中检测URL，但我们也可以显式设置
    // 在生产环境中，这将被环境变量覆盖
    ...("TURBOPACK compile-time value", "development") === 'development' && {
    },
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$google$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$github$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.GITHUB_CLIENT_ID,
            clientSecret: process.env.GITHUB_CLIENT_SECRET
        }),
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])({
            id: 'email-code',
            name: 'Email Code',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                code: {
                    label: 'Code',
                    type: 'text'
                },
                token: {
                    label: 'Token',
                    type: 'text'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.code || !credentials?.token) {
                    return null;
                }
                try {
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])();
                    // 查找用户
                    const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].findOne({
                        email: credentials.email.toLowerCase(),
                        emailVerificationExpires: {
                            $gt: new Date()
                        }
                    });
                    if (!user) {
                        return null;
                    }
                    // 验证token和验证码
                    const storedData = user.emailVerificationToken;
                    if (!storedData || !storedData.includes(':')) {
                        return null;
                    }
                    const [storedToken, storedCode] = storedData.split(':');
                    if (storedToken !== credentials.token || storedCode !== credentials.code) {
                        return null;
                    }
                    // 验证成功，更新用户状态
                    user.emailVerified = true;
                    user.emailVerificationToken = undefined;
                    user.emailVerificationExpires = undefined;
                    user.lastLoginAt = new Date();
                    // 如果用户没有邮箱账户记录，添加一个
                    const hasEmailAccount = user.accounts.some((acc)=>acc.provider === 'email');
                    if (!hasEmailAccount) {
                        user.accounts.push({
                            provider: 'email',
                            providerId: 'email',
                            providerAccountId: user.email
                        });
                    }
                    await user.save();
                    return {
                        id: user._id.toString(),
                        email: user.email,
                        name: user.name,
                        image: user.avatar,
                        role: user.role
                    };
                } catch (error) {
                    console.error('Email code authorization error:', error);
                    return null;
                }
            }
        })
    ],
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        async signIn ({ user, account, profile }) {
            // 对于credentials provider，用户已经在authorize中处理过了
            if (account?.provider === 'email-code') {
                return true;
            }
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])();
            try {
                // 查找或创建用户（仅用于OAuth providers）
                let existingUser = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].findOne({
                    email: user.email
                });
                if (!existingUser) {
                    // 创建新用户
                    existingUser = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"]({
                        email: user.email,
                        name: user.name || profile?.name || 'User',
                        avatar: user.image || profile?.image,
                        emailVerified: true,
                        lastLoginAt: new Date()
                    });
                    await existingUser.save();
                } else {
                    // 更新最后登录时间
                    existingUser.lastLoginAt = new Date();
                    await existingUser.save();
                }
                // 添加或更新账户信息
                if (account && account.provider !== 'email-code') {
                    existingUser.addAccount({
                        provider: account.provider,
                        providerId: account.provider,
                        providerAccountId: account.providerAccountId || account.id || '',
                        accessToken: account.access_token,
                        refreshToken: account.refresh_token,
                        expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined
                    });
                    await existingUser.save();
                }
                return true;
            } catch (error) {
                console.error('Sign in error:', error);
                return false;
            }
        },
        async jwt ({ token, user }) {
            if (user) {
                // 对于credentials provider，user对象已经包含了我们需要的信息
                token.userId = user.id;
                token.role = user.role || 'user';
            }
            return token;
        },
        async session ({ session, token }) {
            if (token && session.user) {
                session.user.id = token.userId;
                session.user.role = token.role;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        error: '/auth/error'
    },
    secret: process.env.NEXTAUTH_SECRET
};
}}),
"[project]/src/constants/categories-i18n.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// 国际化分类配置文件
// 支持多语言的AI工具分类配置
__turbopack_context__.s({
    "CATEGORY_BASE_CONFIGS": (()=>CATEGORY_BASE_CONFIGS),
    "CATEGORY_BASE_METADATA": (()=>CATEGORY_BASE_METADATA),
    "CATEGORY_SLUGS": (()=>CATEGORY_SLUGS),
    "getCategoryConfig": (()=>getCategoryConfig),
    "getCategoryConfigs": (()=>getCategoryConfigs),
    "getCategoryDescription": (()=>getCategoryDescription),
    "getCategoryName": (()=>getCategoryName),
    "getCategoryOptions": (()=>getCategoryOptions),
    "getCategoryOptionsWithAll": (()=>getCategoryOptionsWithAll),
    "isValidCategory": (()=>isValidCategory),
    "useCategoryConfigs": (()=>useCategoryConfigs),
    "useCategoryDescription": (()=>useCategoryDescription),
    "useCategoryName": (()=>useCategoryName),
    "useCategoryOptions": (()=>useCategoryOptions),
    "useCategoryOptionsWithAll": (()=>useCategoryOptionsWithAll)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-server/useTranslations.js [app-rsc] (ecmascript) <export default as useTranslations>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js [app-rsc] (ecmascript) <export default as getTranslations>");
;
;
const CATEGORY_BASE_CONFIGS = [
    {
        slug: 'text-generation',
        icon: '📝',
        color: '#3B82F6'
    },
    {
        slug: 'image-generation',
        icon: '🎨',
        color: '#10B981'
    },
    {
        slug: 'code-generation',
        icon: '💻',
        color: '#8B5CF6'
    },
    {
        slug: 'data-analysis',
        icon: '📊',
        color: '#F59E0B'
    },
    {
        slug: 'audio-processing',
        icon: '🎵',
        color: '#EF4444'
    },
    {
        slug: 'video-editing',
        icon: '🎬',
        color: '#06B6D4'
    },
    {
        slug: 'translation',
        icon: '🌐',
        color: '#84CC16'
    },
    {
        slug: 'search-engines',
        icon: '🔍',
        color: '#F97316'
    },
    {
        slug: 'education',
        icon: '📚',
        color: '#A855F7'
    },
    {
        slug: 'marketing',
        icon: '📈',
        color: '#EC4899'
    },
    {
        slug: 'productivity',
        icon: '⚡',
        color: '#14B8A6'
    },
    {
        slug: 'customer-service',
        icon: '🎧',
        color: '#F59E0B'
    }
];
function useCategoryConfigs() {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    return CATEGORY_BASE_CONFIGS.map((config)=>({
            slug: config.slug,
            name: t(`category_names.${config.slug}`),
            description: t(`category_descriptions.${config.slug}`),
            icon: config.icon,
            color: config.color
        }));
}
function useCategoryOptions() {
    const configs = useCategoryConfigs();
    return configs.map((config)=>({
            value: config.slug,
            label: config.name
        }));
}
function useCategoryOptionsWithAll() {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    const options = useCategoryOptions();
    return [
        {
            value: '',
            label: t('all_categories')
        },
        ...options
    ];
}
function useCategoryName(slug) {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    return t(`category_names.${slug}`) || slug;
}
function useCategoryDescription(slug) {
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$useTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__useTranslations$3e$__["useTranslations"])('categories');
    return t(`category_descriptions.${slug}`) || '';
}
async function getCategoryConfigs(locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    return CATEGORY_BASE_CONFIGS.map((config)=>({
            slug: config.slug,
            name: t(`category_names.${config.slug}`),
            description: t(`category_descriptions.${config.slug}`),
            icon: config.icon,
            color: config.color
        }));
}
async function getCategoryOptions(locale) {
    const configs = await getCategoryConfigs(locale);
    return configs.map((config)=>({
            value: config.slug,
            label: config.name
        }));
}
async function getCategoryOptionsWithAll(locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    const options = await getCategoryOptions(locale);
    return [
        {
            value: '',
            label: t('all_categories')
        },
        ...options
    ];
}
async function getCategoryName(slug, locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    return t(`category_names.${slug}`) || slug;
}
async function getCategoryDescription(slug, locale) {
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'categories'
    });
    return t(`category_descriptions.${slug}`) || '';
}
async function getCategoryConfig(slug, locale) {
    const configs = await getCategoryConfigs(locale);
    return configs.find((config)=>config.slug === slug);
}
function isValidCategory(slug) {
    return CATEGORY_BASE_CONFIGS.some((config)=>config.slug === slug);
}
const CATEGORY_SLUGS = CATEGORY_BASE_CONFIGS.map((config)=>config.slug);
const CATEGORY_BASE_METADATA = CATEGORY_BASE_CONFIGS.reduce((acc, config)=>{
    acc[config.slug] = config;
    return acc;
}, {});
}}),
"[project]/src/models/Tool.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$categories$2d$i18n$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/categories-i18n.ts [app-rsc] (ecmascript)");
;
;
const ToolSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    name: {
        type: String,
        required: [
            true,
            'Tool name is required'
        ],
        trim: true,
        maxlength: [
            100,
            'Tool name cannot exceed 100 characters'
        ]
    },
    tagline: {
        type: String,
        trim: true,
        maxlength: [
            200,
            'Tagline cannot exceed 200 characters'
        ]
    },
    description: {
        type: String,
        required: [
            true,
            'Tool description is required'
        ],
        trim: true,
        maxlength: [
            500,
            'Description cannot exceed 500 characters'
        ]
    },
    longDescription: {
        type: String,
        trim: true,
        maxlength: [
            2000,
            'Long description cannot exceed 2000 characters'
        ]
    },
    website: {
        type: String,
        required: [
            true,
            'Website URL is required'
        ],
        trim: true,
        validate: {
            validator: function(v) {
                return /^https?:\/\/.+/.test(v);
            },
            message: 'Please enter a valid URL'
        }
    },
    logo: {
        type: String,
        trim: true
    },
    category: {
        type: String,
        required: [
            true,
            'Category is required'
        ],
        enum: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$categories$2d$i18n$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["CATEGORY_SLUGS"]
    },
    tags: [
        {
            type: String,
            trim: true,
            lowercase: true
        }
    ],
    pricing: {
        type: String,
        required: [
            true,
            'Pricing model is required'
        ],
        enum: [
            'free',
            'freemium',
            'paid'
        ]
    },
    pricingDetails: {
        type: String,
        trim: true,
        maxlength: [
            500,
            'Pricing details cannot exceed 500 characters'
        ]
    },
    screenshots: [
        {
            type: String,
            trim: true
        }
    ],
    submittedBy: {
        type: String,
        required: [
            true,
            'Submitter ID is required'
        ],
        trim: true
    },
    submittedAt: {
        type: Date,
        default: Date.now
    },
    launchDate: {
        type: Date
    },
    status: {
        type: String,
        required: true,
        enum: [
            'draft',
            'pending',
            'approved',
            'rejected'
        ],
        default: 'draft'
    },
    reviewNotes: {
        type: String,
        trim: true,
        maxlength: [
            1000,
            'Review notes cannot exceed 1000 characters'
        ]
    },
    reviewedBy: {
        type: String,
        trim: true
    },
    reviewedAt: {
        type: Date
    },
    // 发布日期选择相关
    launchDateSelected: {
        type: Boolean,
        default: false
    },
    selectedLaunchDate: {
        type: Date
    },
    launchOption: {
        type: String,
        enum: [
            'free',
            'paid'
        ]
    },
    // 付费相关
    paymentRequired: {
        type: Boolean,
        default: false
    },
    paymentAmount: {
        type: Number,
        min: 0
    },
    paymentStatus: {
        type: String,
        enum: [
            'pending',
            'completed',
            'failed',
            'refunded'
        ]
    },
    orderId: {
        type: String,
        trim: true
    },
    paymentMethod: {
        type: String,
        trim: true
    },
    paidAt: {
        type: Date
    },
    views: {
        type: Number,
        default: 0,
        min: 0
    },
    likes: {
        type: Number,
        default: 0,
        min: 0
    },
    likedBy: [
        {
            type: String,
            trim: true
        }
    ],
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true,
    toJSON: {
        virtuals: true
    },
    toObject: {
        virtuals: true
    }
});
// Indexes for better query performance
ToolSchema.index({
    status: 1,
    isActive: 1
});
ToolSchema.index({
    category: 1,
    status: 1
});
ToolSchema.index({
    tags: 1,
    status: 1
});
ToolSchema.index({
    submittedBy: 1
});
ToolSchema.index({
    launchDate: -1
});
ToolSchema.index({
    views: -1
});
ToolSchema.index({
    likes: -1
});
// Text search index
ToolSchema.index({
    name: 'text',
    tagline: 'text',
    description: 'text',
    longDescription: 'text',
    tags: 'text'
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Tool || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Tool', ToolSchema);
}}),
"[project]/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx <module evaluation>", "default");
}}),
"[project]/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx", "default");
}}),
"[project]/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f5b$locale$5d2f$submit$2f$launch$2d$date$2f5b$toolId$5d2f$LaunchDateClient$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f5b$locale$5d2f$submit$2f$launch$2d$date$2f5b$toolId$5d2f$LaunchDateClient$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f5b$locale$5d2f$submit$2f$launch$2d$date$2f5b$toolId$5d2f$LaunchDateClient$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LaunchDatePage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/next/index.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/i18n/routing.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Tool.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/User.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-check-big.js [app-rsc] (ecmascript) <export default as CheckCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/circle-alert.js [app-rsc] (ecmascript) <export default as AlertCircle>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js [app-rsc] (ecmascript) <export default as getTranslations>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f5b$locale$5d2f$submit$2f$launch$2d$date$2f5b$toolId$5d2f$LaunchDateClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
async function getToolData(toolId, userEmail) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"])();
        // 获取用户信息
        const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$User$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].findOne({
            email: userEmail
        });
        if (!user) {
            return {
                tool: null,
                error: 'unauthorized_access'
            };
        }
        const tool = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Tool$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].findById(toolId).lean();
        if (!tool) {
            return {
                tool: null,
                error: 'tool_not_found'
            };
        }
        // 检查工具是否属于当前用户 - 比较用户ID而不是邮箱
        if (tool.submittedBy !== user._id.toString()) {
            return {
                tool: null,
                error: 'unauthorized_access'
            };
        }
        // 检查工具状态
        if (tool.status !== 'draft') {
            return {
                tool: null,
                error: 'launch_date_already_set'
            };
        }
        return {
            tool: {
                _id: tool._id.toString(),
                name: tool.name,
                description: tool.description,
                status: tool.status
            },
            error: null
        };
    } catch (error) {
        console.error('Failed to fetch tool:', error);
        return {
            tool: null,
            error: 'fetch_failed'
        };
    }
}
async function LaunchDatePage({ params }) {
    const { locale, toolId } = await params;
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])('submit');
    // 检查用户认证
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$next$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["authOptions"]);
    if (!session?.user?.email) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$i18n$2f$routing$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["redirect"])('/');
    }
    // 获取工具数据
    const { tool, error } = await getToolData(toolId, session.user.email);
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$alert$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__AlertCircle$3e$__["AlertCircle"], {
                        className: "h-12 w-12 text-red-500 mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                        lineNumber: 77,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-2xl font-bold text-gray-900 mb-2",
                        children: "出错了"
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                        lineNumber: 78,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600 mb-4",
                        children: t(`errors.${error}`)
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                        lineNumber: 81,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                        href: "/profile/submitted",
                        className: "px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",
                        children: t('actions.back_to_submitted')
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                        lineNumber: 84,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                lineNumber: 76,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
            lineNumber: 75,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center mb-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$circle$2d$check$2d$big$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__CheckCircle$3e$__["CheckCircle"], {
                        className: "h-12 w-12 text-green-500 mx-auto mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                        lineNumber: 99,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-3xl font-bold text-gray-900 mb-2",
                        children: "工具信息提交成功！"
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                        lineNumber: 100,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-lg text-gray-600",
                        children: "现在请选择您的发布日期和选项"
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                        lineNumber: 103,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                lineNumber: 98,
                columnNumber: 7
            }, this),
            tool && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-gray-50 rounded-lg p-6 mb-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-xl font-semibold text-gray-900 mb-2",
                        children: tool.name
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                        lineNumber: 111,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-600",
                        children: tool.description
                    }, void 0, false, {
                        fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                        lineNumber: 114,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                lineNumber: 110,
                columnNumber: 9
            }, this),
            tool && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$app$2f5b$locale$5d2f$submit$2f$launch$2d$date$2f5b$toolId$5d2f$LaunchDateClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
                toolId: toolId,
                locale: locale
            }, void 0, false, {
                fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
                lineNumber: 120,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx",
        lineNumber: 96,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/submit/launch-date/[toolId]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4da1a2b4._.js.map