{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\n\n// 支持的语言列表\nexport const locales = ['en', 'zh'] as const;\nexport type Locale = (typeof locales)[number];\n\n// 默认语言\nexport const defaultLocale: Locale = 'en';\n\n// 语言显示名称\nexport const localeNames: Record<Locale, string> = {\n  zh: '中文',\n  en: 'English',\n};\n\n// 验证语言是否有效\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n\n// Next-intl 配置\nexport default getRequestConfig(async ({ locale }) => {\n  // 验证传入的语言是否有效\n  if (!isValidLocale(locale)) {\n    notFound();\n  }\n\n  return {\n    messages: (await import(`./messages/${locale}.json`)).default,\n  };\n});\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAAA;;;AAGO,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,gBAAwB;AAG9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B;uCAGe,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,cAAc;IACd,IAAI,CAAC,cAAc,SAAS;QAC1B,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,OAAO;QACL,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\nimport { createNavigation } from 'next-intl/navigation';\nimport { getRequestConfig } from 'next-intl/server';\nimport { locales, defaultLocale, type Locale } from './config';\n\nexport const routing = defineRouting({\n  // A list of all locales that are supported\n  locales,\n\n  // Used when no locale matches\n  defaultLocale,\n\n  // Always use locale prefix for all routes\n  localePrefix: 'always',\n\n  // Enable locale detection based on accept-language header\n  localeDetection: true\n});\n\n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nexport const { Link, redirect, usePathname, useRouter } =\n  createNavigation(routing);\n\n// Request configuration for next-intl\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as Locale)) {\n    locale = defaultLocale;\n  }\n\n  return {\n    locale,\n    messages: (await import(`./messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,UAAU,CAAA,GAAA,kOAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAA,qHAAA,CAAA,UAAO;IAEP,8BAA8B;IAC9B,eAAA,qHAAA,CAAA,gBAAa;IAEb,0CAA0C;IAC1C,cAAc;IAEd,0DAA0D;IAC1D,iBAAiB;AACnB;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GACrD,CAAA,GAAA,8PAAA,CAAA,mBAAgB,AAAD,EAAE;uCAGJ,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,qHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAmB;QACvC,SAAS,qHAAA,CAAA,gBAAa;IACxB;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/providers/SessionProvider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/SessionProvider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/providers/SessionProvider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/providers/SessionProvider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/auth/UserMenuClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/UserMenuClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/auth/UserMenuClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/UserMenuClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/LanguageSwitcherClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/LanguageSwitcherClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoT,GACjV,kFACA", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/LanguageSwitcherClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/LanguageSwitcherClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 283, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/MobileMenuClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/MobileMenuClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/MobileMenuClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/MobileMenuClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 311, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 321, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/SearchFormClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/SearchFormClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/SearchFormClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/SearchFormClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 359, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx"], "sourcesContent": ["import NextLink from 'next/link';\nimport { getTranslations, getLocale } from 'next-intl/server';\nimport { type Locale } from '@/i18n/config';\nimport UserMenuClient from '../auth/UserMenuClient';\nimport LanguageSwitcherClient from './LanguageSwitcherClient';\nimport MobileMenuClient from './MobileMenuClient';\nimport SearchFormClient from './SearchFormClient';\n\nconst NavLink = ({ children, href }: { children: React.ReactNode; href: string }) => (\n  <NextLink\n    href={href}\n    className=\"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors\"\n  >\n    {children}\n  </NextLink>\n);\n\nexport default async function Header() {\n  const t = await getTranslations('navigation');\n  const locale = await getLocale() as Locale;\n\n  // Generate navigation links with current locale\n  const getLocalizedHref = (path: string) => {\n    return locale === 'zh' ? path : `/en${path}`;\n  };\n\n  const links = [\n    { name: t('home'), href: getLocalizedHref('/') },\n    { name: t('tools'), href: getLocalizedHref('/tools') },\n    { name: t('categories'), href: getLocalizedHref('/categories') },\n    { name: t('submit'), href: getLocalizedHref('/submit') },\n  ];\n\n  return (\n    <header className=\"bg-white px-4 shadow-sm border-b border-gray-200\">\n      <div className=\"flex h-16 items-center justify-between\">\n        {/* Logo */}\n        <div className=\"flex items-center space-x-8\">\n          <NextLink href={getLocalizedHref('/')} className=\"flex items-center space-x-2 hover:no-underline\">\n            <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">AI</span>\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">\n              {locale === 'zh' ? 'AI工具导航' : 'AI Tools'}\n            </span>\n          </NextLink>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-4\">\n            {links.map((link) => (\n              <NavLink key={link.name} href={link.href}>\n                {link.name}\n              </NavLink>\n            ))}\n          </nav>\n        </div>\n\n        {/* Search Bar */}\n        <div className=\"flex-1 max-w-md mx-8 hidden md:block\">\n          <SearchFormClient locale={locale} />\n        </div>\n\n        {/* Right side */}\n        <div className=\"flex items-center space-x-2\">\n          {/* Language Switcher */}\n          <LanguageSwitcherClient currentLocale={locale} />\n\n          {/* User Menu */}\n          <UserMenuClient />\n\n          {/* Mobile Menu */}\n          <MobileMenuClient links={links} locale={locale} />\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAEA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAA+C,iBAC9E,8OAAC,4JAAA,CAAA,UAAQ;QACP,MAAM;QACN,WAAU;kBAET;;;;;;AAIU,eAAe;IAC5B,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,SAAS,MAAM,CAAA,GAAA,4OAAA,CAAA,YAAS,AAAD;IAE7B,gDAAgD;IAChD,MAAM,mBAAmB,CAAC;QACxB,OAAO,WAAW,OAAO,OAAO,CAAC,GAAG,EAAE,MAAM;IAC9C;IAEA,MAAM,QAAQ;QACZ;YAAE,MAAM,EAAE;YAAS,MAAM,iBAAiB;QAAK;QAC/C;YAAE,MAAM,EAAE;YAAU,MAAM,iBAAiB;QAAU;QACrD;YAAE,MAAM,EAAE;YAAe,MAAM,iBAAiB;QAAe;QAC/D;YAAE,MAAM,EAAE;YAAW,MAAM,iBAAiB;QAAW;KACxD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAQ;4BAAC,MAAM,iBAAiB;4BAAM,WAAU;;8CAC/C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CACb,WAAW,OAAO,WAAW;;;;;;;;;;;;sCAKlC,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;oCAAwB,MAAM,KAAK,IAAI;8CACrC,KAAK,IAAI;mCADE,KAAK,IAAI;;;;;;;;;;;;;;;;8BAQ7B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gJAAA,CAAA,UAAgB;wBAAC,QAAQ;;;;;;;;;;;8BAI5B,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,sJAAA,CAAA,UAAsB;4BAAC,eAAe;;;;;;sCAGvC,8OAAC,4IAAA,CAAA,UAAc;;;;;sCAGf,8OAAC,gJAAA,CAAA,UAAgB;4BAAC,OAAO;4BAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAKlD", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Footer.tsx"], "sourcesContent": ["import NextLink from 'next/link';\nimport { getTranslations, getLocale } from 'next-intl/server';\nimport { type Locale } from '@/i18n/config';\n\nexport default async function Footer() {\n  const t = await getTranslations('layout');\n  const locale = await getLocale() as Locale;\n\n  // Generate navigation links with current locale\n  const getLocalizedHref = (path: string) => {\n    return locale === 'zh' ? path : `/en${path}`;\n  };\n\n  return (\n    <footer className=\"bg-white border-t border-gray-200 mt-16\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">AI</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">\n                {locale === 'zh' ? 'AI工具导航' : 'AI Tools'}\n              </span>\n            </div>\n            <p className=\"text-gray-600 mb-4\">\n              {t('footer.description')}\n            </p>\n          </div>\n          \n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n              {t('footer.quick_links')}\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <NextLink href={getLocalizedHref('/tools')} className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.tools_directory')}\n                </NextLink>\n              </li>\n              <li>\n                <NextLink href={getLocalizedHref('/categories')} className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.browse_categories')}\n                </NextLink>\n              </li>\n              <li>\n                <NextLink href={getLocalizedHref('/submit')} className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.submit_tool')}\n                </NextLink>\n              </li>\n            </ul>\n          </div>\n          \n          <div>\n            <h3 className=\"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4\">\n              {t('footer.support')}\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.help_center')}\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.contact_us')}\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-gray-600 hover:text-blue-600\">\n                  {t('footer.privacy_policy')}\n                </a>\n              </li>\n            </ul>\n          </div>\n        </div>\n        \n        <div className=\"border-t border-gray-200 mt-8 pt-8\">\n          <p className=\"text-center text-gray-600\">\n            {t('footer.copyright')}\n          </p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;;AAGe,eAAe;IAC5B,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,SAAS,MAAM,CAAA,GAAA,4OAAA,CAAA,YAAS,AAAD;IAE7B,gDAAgD;IAChD,MAAM,mBAAmB,CAAC;QACxB,OAAO,WAAW,OAAO,OAAO,CAAC,GAAG,EAAE,MAAM;IAC9C;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDACb,WAAW,OAAO,WAAW;;;;;;;;;;;;8CAGlC,8OAAC;oCAAE,WAAU;8CACV,EAAE;;;;;;;;;;;;sCAIP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAQ;gDAAC,MAAM,iBAAiB;gDAAW,WAAU;0DACnD,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAQ;gDAAC,MAAM,iBAAiB;gDAAgB,WAAU;0DACxD,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAQ;gDAAC,MAAM,iBAAiB;gDAAY,WAAU;0DACpD,EAAE;;;;;;;;;;;;;;;;;;;;;;;sCAMX,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;8CAEL,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACnB,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACnB,EAAE;;;;;;;;;;;sDAGP,8OAAC;sDACC,cAAA,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACnB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 787, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LikeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call LikeProvider() from the server but LikeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/LikeContext.tsx <module evaluation>\",\n    \"LikeProvider\",\n);\nexport const useLike = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLike() from the server but useLike is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/LikeContext.tsx <module evaluation>\",\n    \"useLike\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8DACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LikeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call LikeProvider() from the server but LikeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/LikeContext.tsx\",\n    \"LikeProvider\",\n);\nexport const useLike = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLike() from the server but useLike is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/LikeContext.tsx\",\n    \"useLike\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0CACA;AAEG,MAAM,UAAU,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "debugId": null}}, {"offset": {"line": 823, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport { NextIntlClientProvider } from 'next-intl';\nimport { getMessages } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\nimport \"../globals.css\";\nimport SessionProvider from \"@/components/providers/SessionProvider\";\nimport Header from \"@/components/layout/Header\";\nimport Footer from \"@/components/layout/Footer\";\nimport { LikeProvider } from \"@/contexts/LikeContext\";\nimport { locales, type Locale } from '@/i18n/config';\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\ntype Props = {\n  children: React.ReactNode;\n  params: { locale: string };\n};\n\nexport async function generateStaticParams() {\n  return locales.map((locale) => ({ locale }));\n}\n\nexport async function generateMetadata({ params }: Props): Promise<Metadata> {\n  const { locale } = await params;\n  \n  if (locale === 'zh') {\n    return {\n      title: \"AI工具导航 - 发现最好的人工智能工具\",\n      description: \"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。\",\n      keywords: \"AI工具,人工智能,AI导航,机器学习工具,深度学习,自动化工具,AI应用\",\n      authors: [{ name: \"AI工具导航团队\" }],\n      creator: \"AI工具导航\",\n      publisher: \"AI工具导航\",\n      robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n          index: true,\n          follow: true,\n          'max-video-preview': -1,\n          'max-image-preview': 'large',\n          'max-snippet': -1,\n        },\n      },\n      openGraph: {\n        type: 'website',\n        locale: 'zh_CN',\n        url: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',\n        siteName: 'AI工具导航',\n        title: 'AI工具导航 - 发现最好的人工智能工具',\n        description: '探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。',\n        images: [\n          {\n            url: '/og-image.jpg',\n            width: 1200,\n            height: 630,\n            alt: 'AI工具导航 - 发现最好的人工智能工具',\n          },\n        ],\n      },\n      twitter: {\n        card: 'summary_large_image',\n        title: 'AI工具导航 - 发现最好的人工智能工具',\n        description: '探索精选的人工智能工具集合，提升您的工作效率和创造力。',\n        images: ['/og-image.jpg'],\n      },\n      alternates: {\n        canonical: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',\n        languages: {\n          'zh': '/',\n          'en': '/en',\n        },\n      },\n      verification: {\n        google: 'your-google-verification-code',\n      },\n    };\n  } else {\n    return {\n      title: \"AI Tools Directory - Discover the Best AI Tools\",\n      description: \"Explore curated collection of AI tools to boost your productivity and creativity. From text generation to image creation, find the perfect AI tool for your needs.\",\n      keywords: \"AI tools,artificial intelligence,AI directory,machine learning tools,deep learning,automation tools,AI applications\",\n      authors: [{ name: \"AI Tools Directory Team\" }],\n      creator: \"AI Tools Directory\",\n      publisher: \"AI Tools Directory\",\n      robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n          index: true,\n          follow: true,\n          'max-video-preview': -1,\n          'max-image-preview': 'large',\n          'max-snippet': -1,\n        },\n      },\n      openGraph: {\n        type: 'website',\n        locale: 'en_US',\n        url: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',\n        siteName: 'AI Tools Directory',\n        title: 'AI Tools Directory - Discover the Best AI Tools',\n        description: 'Explore curated collection of AI tools to boost your productivity and creativity. From text generation to image creation, find the perfect AI tool for your needs.',\n        images: [\n          {\n            url: '/og-image.jpg',\n            width: 1200,\n            height: 630,\n            alt: 'AI Tools Directory - Discover the Best AI Tools',\n          },\n        ],\n      },\n      twitter: {\n        card: 'summary_large_image',\n        title: 'AI Tools Directory - Discover the Best AI Tools',\n        description: 'Explore curated collection of AI tools to boost your productivity and creativity.',\n        images: ['/og-image.jpg'],\n      },\n      alternates: {\n        canonical: process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com',\n        languages: {\n          'zh': '/',\n          'en': '/en',\n        },\n      },\n      verification: {\n        google: 'your-google-verification-code',\n      },\n    };\n  }\n}\n\nexport default async function LocaleLayout({\n  children,\n  params\n}: Props) {\n  const { locale } = await params;\n\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as Locale)) {\n    notFound();\n  }\n\n  // Providing all messages to the client side is the easiest way to get started\n  const messages = await getMessages();\n\n  return (\n    <NextIntlClientProvider messages={messages}>\n      <SessionProvider>\n        <LikeProvider>\n          <div className=\"min-h-screen bg-gray-50 flex flex-col\">\n            <Header />\n            <main className={`${geistSans.variable} ${geistMono.variable} antialiased flex-1`}>\n              {children}\n            </main>\n            <Footer />\n          </div>\n        </LikeProvider>\n      </SessionProvider>\n    </NextIntlClientProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AAAA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAiBO,eAAe;IACpB,OAAO,qHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,SAAW,CAAC;YAAE;QAAO,CAAC;AAC5C;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAS;IACtD,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IAEzB,IAAI,WAAW,MAAM;QACnB,OAAO;YACL,OAAO;YACP,aAAa;YACb,UAAU;YACV,SAAS;gBAAC;oBAAE,MAAM;gBAAW;aAAE;YAC/B,SAAS;YACT,WAAW;YACX,QAAQ;gBACN,OAAO;gBACP,QAAQ;gBACR,WAAW;oBACT,OAAO;oBACP,QAAQ;oBACR,qBAAqB,CAAC;oBACtB,qBAAqB;oBACrB,eAAe,CAAC;gBAClB;YACF;YACA,WAAW;gBACT,MAAM;gBACN,QAAQ;gBACR,KAAK,QAAQ,GAAG,CAAC,oBAAoB,IAAI;gBACzC,UAAU;gBACV,OAAO;gBACP,aAAa;gBACb,QAAQ;oBACN;wBACE,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,KAAK;oBACP;iBACD;YACH;YACA,SAAS;gBACP,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,QAAQ;oBAAC;iBAAgB;YAC3B;YACA,YAAY;gBACV,WAAW,QAAQ,GAAG,CAAC,oBAAoB,IAAI;gBAC/C,WAAW;oBACT,MAAM;oBACN,MAAM;gBACR;YACF;YACA,cAAc;gBACZ,QAAQ;YACV;QACF;IACF,OAAO;QACL,OAAO;YACL,OAAO;YACP,aAAa;YACb,UAAU;YACV,SAAS;gBAAC;oBAAE,MAAM;gBAA0B;aAAE;YAC9C,SAAS;YACT,WAAW;YACX,QAAQ;gBACN,OAAO;gBACP,QAAQ;gBACR,WAAW;oBACT,OAAO;oBACP,QAAQ;oBACR,qBAAqB,CAAC;oBACtB,qBAAqB;oBACrB,eAAe,CAAC;gBAClB;YACF;YACA,WAAW;gBACT,MAAM;gBACN,QAAQ;gBACR,KAAK,QAAQ,GAAG,CAAC,oBAAoB,IAAI;gBACzC,UAAU;gBACV,OAAO;gBACP,aAAa;gBACb,QAAQ;oBACN;wBACE,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,KAAK;oBACP;iBACD;YACH;YACA,SAAS;gBACP,MAAM;gBACN,OAAO;gBACP,aAAa;gBACb,QAAQ;oBAAC;iBAAgB;YAC3B;YACA,YAAY;gBACV,WAAW,QAAQ,GAAG,CAAC,oBAAoB,IAAI;gBAC/C,WAAW;oBACT,MAAM;oBACN,MAAM;gBACR;YACF;YACA,cAAc;gBACZ,QAAQ;YACV;QACF;IACF;AACF;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EACA;IACN,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IAEzB,yDAAyD;IACzD,IAAI,CAAC,qHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAmB;QACvC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,8EAA8E;IAC9E,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD;IAEjC,qBACE,8OAAC,kQAAA,CAAA,yBAAsB;QAAC,UAAU;kBAChC,cAAA,8OAAC,kJAAA,CAAA,UAAe;sBACd,cAAA,8OAAC,+HAAA,CAAA,eAAY;0BACX,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,UAAM;;;;;sCACP,8OAAC;4BAAK,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,mBAAmB,CAAC;sCAC9E;;;;;;sCAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnB", "debugId": null}}]}