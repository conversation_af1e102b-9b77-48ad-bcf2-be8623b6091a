(()=>{var e={};e.id=9759,e.ids=[9759],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3845:(e,t,r)=>{var s={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function i(e){if(!r.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],i=t[0];return r.e(t[1]).then(()=>r.t(i,19))}i.keys=()=>Object.keys(s),i.id=3845,e.exports=i},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12688:(e,t,r)=>{"use strict";r.d(t,{IB:()=>s,q:()=>i});let s=["en","zh"],i="en"},17941:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(35471),i=r(12688);let n=(0,s.A)(async({locale:e})=>(i.IB.includes(e)||(e=i.q),{locale:e,messages:(await r(3845)(`./${e}.json`)).default}))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),i=r.n(s),n=r(60366);let a=new s.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:n.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({status:1,isActive:1}),a.index({category:1,status:1}),a.index({tags:1,status:1}),a.index({submittedBy:1}),a.index({launchDate:-1}),a.index({views:-1}),a.index({likes:-1}),a.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let o=i().models.Tool||i().model("Tool",a)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45196:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(78521),i=r(60687);function n({locale:e,...t}){if(!e)throw Error(void 0);return(0,i.jsx)(s.Dk,{locale:e,...t})}},46930:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},51639:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var s={};r.r(s),r.d(s,{GET:()=>d});var i=r(96559),n=r(48088),a=r(37719),o=r(32190),c=r(75745),u=r(30762);async function d(e){try{let t;await (0,c.A)();let{searchParams:r}=new URL(e.url),s=r.get("timeRange")||"7d",i=new Date;switch(s){case"1d":t=new Date(i.getTime()-864e5);break;case"7d":default:t=new Date(i.getTime()-6048e5);break;case"30d":t=new Date(i.getTime()-2592e6);break;case"90d":t=new Date(i.getTime()-7776e6)}let[n,a,d,l,m,p,g,v,w]=await Promise.all([u.A.countDocuments(),u.A.countDocuments({status:"pending"}),u.A.countDocuments({status:"approved"}),u.A.countDocuments({status:"rejected"}),u.A.aggregate([{$group:{_id:null,total:{$sum:"$views"}}}]),u.A.aggregate([{$group:{_id:null,total:{$sum:"$likes"}}}]),u.A.countDocuments({submittedAt:{$gte:t}}),u.A.countDocuments({status:"approved",reviewedAt:{$gte:t}}),u.A.countDocuments({status:"rejected",reviewedAt:{$gte:t}})]),y=await u.A.aggregate([{$match:{status:"approved"}},{$group:{_id:"$category",count:{$sum:1},totalViews:{$sum:"$views"},totalLikes:{$sum:"$likes"}}},{$sort:{count:-1}}]),f=await u.A.find({status:"approved"}).sort({views:-1}).limit(10).select("name category views likes").lean(),h=await u.A.find({$or:[{submittedAt:{$gte:t}},{reviewedAt:{$gte:t}}]}).sort({updatedAt:-1}).limit(20).select("name status submittedAt reviewedAt submittedBy reviewedBy").lean(),x=await u.A.aggregate([{$match:{submittedAt:{$gte:new Date(i.getTime()-6048e5)}}},{$group:{_id:{date:{$dateToString:{format:"%Y-%m-%d",date:"$submittedAt"}},status:"$status"},count:{$sum:1}}},{$sort:{"_id.date":1}}]),A=[];for(let e=6;e>=0;e--){let t=new Date(i.getTime()-24*e*36e5),r=t.toISOString().split("T")[0],s=t.toLocaleDateString("zh-CN",{weekday:"short"}),n=x.filter(e=>e._id.date===r),a=n.find(e=>"pending"===e._id.status)?.count||0,o=n.find(e=>"approved"===e._id.status)?.count||0,c=n.find(e=>"rejected"===e._id.status)?.count||0;A.push({date:r,day:s,submissions:a,approvals:o,rejections:c})}let $=await u.A.aggregate([{$match:{status:{$in:["approved","rejected"]},reviewedAt:{$exists:!0},submittedAt:{$exists:!0}}},{$project:{reviewTime:{$divide:[{$subtract:["$reviewedAt","$submittedAt"]},36e5]}}},{$group:{_id:null,avgReviewTime:{$avg:"$reviewTime"}}}]),b={totalTools:n,pendingTools:a,approvedTools:d,rejectedTools:l,totalViews:m[0]?.total||0,totalLikes:p[0]?.total||0,recentSubmissions:g,recentApprovals:v,recentRejections:w,avgReviewTime:$[0]?.avgReviewTime||0};return o.NextResponse.json({success:!0,data:{overview:b,categoryStats:y,topTools:f,recentActivity:h,dailyStats:A,timeRange:s}})}catch(e){return console.error("Error fetching admin stats:",e),o.NextResponse.json({success:!1,error:"获取统计数据失败"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/stats/route",pathname:"/api/admin/stats",filename:"route",bundlePath:"app/api/admin/stats/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:g}=l;function v(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,r)=>{"use strict";r.d(t,{PZ:()=>a,RI:()=>c,ut:()=>o});var s=r(64348);let i=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function n(e){let t=await (0,s.A)({locale:e,namespace:"categories"});return i.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function a(e,t){return(await n(t)).find(t=>t.slug===e)}let o=i.map(e=>e.slug),c=i.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64348:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(61120),i=r(92440),n=r(84604),a=(0,s.cache)(function(e,t){return function({_cache:e=(0,n.d)(),_formatters:t=(0,n.b)(e),getMessageFallback:r=n.f,messages:s,namespace:i,onError:a=n.g,...o}){return function({messages:e,namespace:t,...r},s){return e=e["!"],t=(0,n.r)(t,"!"),(0,n.e)({...r,messages:e,namespace:t})}({...o,onError:a,cache:e,formatters:t,getMessageFallback:r,messages:{"!":s},namespace:i?`!.${i}`:"!"},"!")}({...e,namespace:t})}),o=(0,s.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),a(await (0,i.A)(r),t)})},70490:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),i=r.n(s);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let o=async function(){if(a.conn)return a.conn;a.promise||(a.promise=i().connect(n,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},80994:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,580],()=>r(51639));module.exports=s})();