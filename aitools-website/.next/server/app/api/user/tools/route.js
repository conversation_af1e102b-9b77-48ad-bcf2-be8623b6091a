const CHUNK_PUBLIC_PATH = "server/app/api/user/tools/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/src_i18n_messages_452eb43f._.js");
runtime.loadChunk("server/chunks/node_modules_next_af4bca0c._.js");
runtime.loadChunk("server/chunks/node_modules_next-auth_2070941b._.js");
runtime.loadChunk("server/chunks/node_modules_openid-client_ef38b3be._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_cjs_b4a80197._.js");
runtime.loadChunk("server/chunks/node_modules_dfee31e8._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__c49f6433._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/user/tools/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/user/tools/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/user/tools/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
