(()=>{var e={};e.id=8644,e.ids=[8644],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3845:(e,t,s)=>{var r={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function i(e){if(!s.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],i=t[0];return s.e(t[1]).then(()=>s.t(i,19))}i.keys=()=>Object.keys(r),i.id=3845,e.exports=i},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12688:(e,t,s)=>{"use strict";s.d(t,{IB:()=>r,q:()=>i});let r=["en","zh"],i="en"},12909:(e,t,s)=>{"use strict";s.d(t,{N:()=>d});var r=s(36344),i=s(65752),a=s(13581),n=s(75745),o=s(17063);let d={...!1,providers:[(0,r.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,a.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,n.A)();let t=await o.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let s=t.emailVerificationToken;if(!s||!s.includes(":"))return null;let[r,i]=s.split(":");if(r!==e.token||i!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:s}){if(t?.provider==="email-code")return!0;await (0,n.A)();try{let r=await o.A.findOne({email:e.email});return r?r.lastLoginAt=new Date:r=new o.A({email:e.email,name:e.name||s?.name||"User",avatar:e.image||s?.image,emailVerified:!0,lastLoginAt:new Date}),await r.save(),t&&"email-code"!==t.provider&&(r.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await r.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(56037),i=s.n(r);let a=new r.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),n=new r.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[a],submittedTools:[{type:r.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:r.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:r.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({email:1}),n.index({role:1}),n.index({emailVerificationToken:1}),n.index({"accounts.provider":1,"accounts.providerAccountId":1}),n.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},n.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(s=>s.provider!==e||s.providerAccountId!==t)};let o=i().models.User||i().model("User",n)},17941:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(35471),i=s(12688);let a=(0,r.A)(async({locale:e})=>(i.IB.includes(e)||(e=i.q),{locale:e,messages:(await s(3845)(`./${e}.json`)).default}))},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(56037),i=s.n(r),a=s(60366);let n=new r.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:a.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({status:1,isActive:1}),n.index({category:1,status:1}),n.index({tags:1,status:1}),n.index({submittedBy:1}),n.index({launchDate:-1}),n.index({views:-1}),n.index({likes:-1}),n.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let o=i().models.Tool||i().model("Tool",n)},31098:(e,t,s)=>{"use strict";s.d(t,{A:()=>n});var r=s(56037),i=s.n(r);let a=new r.Schema({userId:{type:r.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},toolId:{type:r.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},type:{type:String,required:!0,enum:["launch_date_priority"],default:"launch_date_priority"},amount:{type:Number,required:[!0,"Amount is required"],min:[0,"Amount must be positive"]},currency:{type:String,required:!0,default:"CNY",enum:["CNY","USD"]},status:{type:String,required:!0,enum:["pending","completed","failed","cancelled","refunded"],default:"pending"},paymentMethod:{type:String,trim:!0},paymentIntentId:{type:String,trim:!0},paymentSessionId:{type:String,trim:!0},stripePaymentIntentId:{type:String,trim:!0},stripeCustomerId:{type:String,trim:!0},stripePaymentDetails:{paymentIntentId:String,amount:Number,currency:String,status:String,created:Date,failureReason:String},description:{type:String,required:[!0,"Description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},selectedLaunchDate:{type:Date,required:[!0,"Selected launch date is required"]},paidAt:{type:Date},cancelledAt:{type:Date},refundedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({userId:1,createdAt:-1}),a.index({toolId:1}),a.index({status:1}),a.index({paymentIntentId:1}),a.index({paymentSessionId:1}),a.index({stripePaymentIntentId:1}),a.index({stripeCustomerId:1}),a.virtual("user",{ref:"User",localField:"userId",foreignField:"_id",justOne:!0}),a.virtual("tool",{ref:"Tool",localField:"toolId",foreignField:"_id",justOne:!0}),a.methods.markAsPaid=function(){return this.status="completed",this.paidAt=new Date,this.save()},a.methods.markAsFailed=function(){return this.status="failed",this.save()},a.methods.cancel=function(){return this.status="cancelled",this.cancelledAt=new Date,this.save()},a.methods.refund=function(){return this.status="refunded",this.refundedAt=new Date,this.save()};let n=i().models.Order||i().model("Order",a)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45196:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(78521),i=s(60687);function a({locale:e,...t}){if(!e)throw Error(void 0);return(0,i.jsx)(r.Dk,{locale:e,...t})}},46930:(e,t,s)=>{Promise.resolve().then(s.bind(s,80994))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,s)=>{"use strict";s.d(t,{PZ:()=>n,RI:()=>d,ut:()=>o});var r=s(64348);let i=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function a(e){let t=await (0,r.A)({locale:e,namespace:"categories"});return i.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function n(e,t){return(await a(t)).find(t=>t.slug===e)}let o=i.map(e=>e.slug),d=i.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64348:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(61120),i=s(92440),a=s(84604),n=(0,r.cache)(function(e,t){return function({_cache:e=(0,a.d)(),_formatters:t=(0,a.b)(e),getMessageFallback:s=a.f,messages:r,namespace:i,onError:n=a.g,...o}){return function({messages:e,namespace:t,...s},r){return e=e["!"],t=(0,a.r)(t,"!"),(0,a.e)({...s,messages:e,namespace:t})}({...o,onError:n,cache:e,formatters:t,getMessageFallback:s,messages:{"!":r},namespace:i?`!.${i}`:"!"},"!")}({...e,namespace:t})}),o=(0,r.cache)(async function(e){let t,s;return"string"==typeof e?t=e:e&&(s=e.locale,t=e.namespace),n(await (0,i.A)(s),t)})},70490:(e,t,s)=>{Promise.resolve().then(s.bind(s,45196))},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,s)=>{"use strict";s.d(t,{A:()=>o});var r=s(56037),i=s.n(r);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let o=async function(){if(n.conn)return n.conn;n.promise||(n.promise=i().connect(a,{bufferCommands:!1}).then(e=>e));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},79171:(e,t,s)=>{"use strict";s.d(t,{kX:()=>r});let r={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}};r.FREE_LAUNCH.description,r.FREE_LAUNCH.displayPrice,r.FREE_LAUNCH.features,r.PRIORITY_LAUNCH.description,r.PRIORITY_LAUNCH.displayPrice,r.PRIORITY_LAUNCH.features;let i={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}};i.FREE.value,i.FREE.label,i.FREEMIUM.value,i.FREEMIUM.label,i.PAID.value,i.PAID.label,i.FREE.value,i.FREE.label,i.FREEMIUM.value,i.FREEMIUM.label,i.PAID.value,i.PAID.label},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80972:(e,t,s)=>{"use strict";s.d(t,{Q$:()=>a,y3:()=>n});let r={errors:{fetch_failed:"获取数据失败",network_error:"网络错误，请重试",validation_failed:"验证失败",unauthorized:"未授权访问",forbidden:"禁止访问",not_found:"资源未找到",internal_error:"服务器内部错误",invalid_request:"无效请求",missing_required_field:"缺少必需字段",duplicate_name:"名称已存在",create_failed:"创建失败",update_failed:"更新失败",delete_failed:"删除失败"},success:{created:"创建成功",updated:"更新成功",deleted:"删除成功",submitted:"提交成功",approved:"批准成功",rejected:"拒绝成功",published:"发布成功"},tools:{fetch_failed:"获取工具列表失败",create_failed:"创建工具失败",name_required:"name 是必需的",description_required:"description 是必需的",website_required:"website 是必需的",category_required:"category 是必需的",pricing_required:"pricing 是必需的",submitter_name_required:"submitterName 是必需的",submitter_email_required:"submitterEmail 是必需的",name_exists:"该工具名称已存在",submit_success:"工具提交成功，等待审核",approve_success:"工具审核通过",reject_success:"工具已拒绝",approve_failed:"审核通过失败",reject_failed:"拒绝失败",not_found:"工具未找到",update_success:"工具更新成功",update_failed:"工具更新失败",launch_date_already_set:"此工具已经选择了发布日期",free_date_restriction:"免费选项只能选择一个月后的日期",paid_date_restriction:"付费选项最早只能选择明天的日期",launch_date_set_success:"发布日期设置成功，工具已进入审核队列",edit_not_allowed:"当前状态不允许修改发布日期",already_published:"工具已发布，无法修改发布日期",launch_date_updated:"发布日期修改成功",publish_success:"工具发布成功",publish_failed:"工具发布失败"},user:{not_found:"用户未找到",unauthorized:"用户未授权",profile_update_success:"个人资料更新成功",profile_update_failed:"个人资料更新失败"},auth:{invalid_credentials:"无效的登录凭据",code_sent:"验证码已发送",code_send_failed:"验证码发送失败",invalid_code:"无效的验证码",login_success:"登录成功",login_failed:"登录失败",logout_success:"退出成功"},payment:{create_intent_failed:"创建支付意图失败",payment_success:"支付成功",payment_failed:"支付失败",webhook_error:"Webhook 处理错误",order_created:"订单创建成功，请完成支付",upgrade_order_created:"升级订单创建成功，请完成支付"},upload:{no_file:"请选择要上传的文件",invalid_type:"只支持 JPEG、PNG、GIF、WebP 格式的图片",file_too_large:"文件大小不能超过 5MB",upload_failed:"文件上传失败",upload_success:"文件上传成功"}},i={errors:{fetch_failed:"Failed to fetch data",network_error:"Network error, please try again",validation_failed:"Validation failed",unauthorized:"Unauthorized access",forbidden:"Access forbidden",not_found:"Resource not found",internal_error:"Internal server error",invalid_request:"Invalid request",missing_required_field:"Missing required field",duplicate_name:"Name already exists",create_failed:"Creation failed",update_failed:"Update failed",delete_failed:"Deletion failed"},success:{created:"Created successfully",updated:"Updated successfully",deleted:"Deleted successfully",submitted:"Submitted successfully",approved:"Approved successfully",rejected:"Rejected successfully",published:"Published successfully"},tools:{fetch_failed:"Failed to fetch tools list",create_failed:"Failed to create tool",name_required:"name is required",description_required:"description is required",website_required:"website is required",category_required:"category is required",pricing_required:"pricing is required",submitter_name_required:"submitterName is required",submitter_email_required:"submitterEmail is required",name_exists:"Tool name already exists",submit_success:"Tool submitted successfully, awaiting review",approve_success:"Tool approved successfully",reject_success:"Tool rejected successfully",approve_failed:"Failed to approve tool",reject_failed:"Failed to reject tool",not_found:"Tool not found",update_success:"Tool updated successfully",update_failed:"Failed to update tool",launch_date_already_set:"This tool has already selected a launch date",free_date_restriction:"Free option can only select dates one month later",paid_date_restriction:"Paid option can only select dates from tomorrow",launch_date_set_success:"Launch date set successfully, tool entered review queue",edit_not_allowed:"Current status does not allow modifying launch date",already_published:"Tool already published, cannot modify launch date",launch_date_updated:"Launch date updated successfully",publish_success:"Tool published successfully",publish_failed:"Failed to publish tool"},user:{not_found:"User not found",unauthorized:"User unauthorized",profile_update_success:"Profile updated successfully",profile_update_failed:"Failed to update profile"},auth:{invalid_credentials:"Invalid credentials",code_sent:"Verification code sent",code_send_failed:"Failed to send verification code",invalid_code:"Invalid verification code",login_success:"Login successful",login_failed:"Login failed",logout_success:"Logout successful"},payment:{create_intent_failed:"Failed to create payment intent",payment_success:"Payment successful",payment_failed:"Payment failed",webhook_error:"Webhook processing error",order_created:"Order created successfully, please complete payment",upgrade_order_created:"Upgrade order created successfully, please complete payment"},upload:{no_file:"Please select a file to upload",invalid_type:"Only JPEG, PNG, GIF, WebP image formats are supported",file_too_large:"File size cannot exceed 5MB",upload_failed:"File upload failed",upload_success:"File uploaded successfully"}};function a(e,t){let s=t.split("."),a="zh"===e?r:i;for(let t of s)if(!a||"object"!=typeof a||!(t in a))return"zh"===e?"操作失败":"Operation failed";else a=a[t];return"string"==typeof a?a:"zh"===e?"操作失败":"Operation failed"}function n(e){let t=e.headers.get("accept-language")||"",s=new URL(e.url).pathname;return s.startsWith("/en/")?"en":s.startsWith("/zh/")?"zh":t.includes("en")?"en":"zh"}},80994:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js","default")},81630:e=>{"use strict";e.exports=require("http")},89765:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>S,routeModule:()=>I,serverHooks:()=>A,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>w});var r={};s.r(r),s.d(r,{GET:()=>v,PATCH:()=>x,POST:()=>h});var i=s(96559),a=s(48088),n=s(37719),o=s(32190),d=s(35426),u=s(75745),l=s(30762),c=s(31098),p=s(17063),m=s(12909),f=s(79171),g=s(56037),_=s.n(g),y=s(80972);async function h(e,{params:t}){try{let s=await (0,d.getServerSession)(m.N),r=(0,y.y3)(e);if(!s?.user?.email)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"user.unauthorized")},{status:401});await (0,u.A)();let{id:i}=await t,{launchOption:a,selectedDate:n}=await e.json();if(!_().Types.ObjectId.isValid(i)||!a||!n||!["free","paid"].includes(a))return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"errors.invalid_request")},{status:400});let g=await p.A.findOne({email:s.user.email});if(!g)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"user.not_found")},{status:404});let h=await l.A.findById(i);if(!h)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"tools.not_found")},{status:404});if(h.submittedBy!==g._id.toString())return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"errors.forbidden")},{status:403});if("draft"!==h.status&&("pending"!==h.status||"free"!==h.launchOption||"paid"!==a))return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"tools.launch_date_already_set")},{status:400});let v=new Date(n);if("free"===a){let e=new Date;e.setMonth(e.getMonth()+1),e.setHours(0,0,0,0);let t=new Date(v);if(t.setHours(0,0,0,0),t.getTime()<e.getTime())return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"tools.free_date_restriction")},{status:400})}else{let e=new Date;if(e.setDate(e.getDate()+1),e.setHours(0,0,0,0),v<e)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"tools.paid_date_restriction")},{status:400})}if("free"===a)return await l.A.findByIdAndUpdate(i,{$set:{launchDateSelected:!0,selectedLaunchDate:v,launchDate:v,launchOption:"free",paymentRequired:!1,status:"pending"}}),o.NextResponse.json({success:!0,data:{message:(0,y.Q$)(r,"tools.launch_date_set_success")}});{let e=f.kX.PRIORITY_LAUNCH.stripeAmount,t=new c.A({userId:g._id,toolId:i,type:"launch_date_priority",amount:e,currency:f.kX.PRIORITY_LAUNCH.currency,status:"pending",description:`工具 "${h.name}" 优先发布服务`,selectedLaunchDate:v});await t.save();let s={launchDateSelected:!0,selectedLaunchDate:v,launchDate:v,launchOption:"paid",paymentRequired:!0,paymentAmount:e,paymentStatus:"pending",orderId:t._id.toString()};s.status="draft",await l.A.findByIdAndUpdate(i,{$set:s});let a=`/payment/checkout?orderId=${t._id}`;return o.NextResponse.json({success:!0,data:{orderId:t._id,paymentUrl:a,amount:e,message:"pending"===h.status?(0,y.Q$)(r,"payment.upgrade_order_created"):(0,y.Q$)(r,"payment.order_created")}})}}catch(s){console.error("Launch date selection error:",s);let t=(0,y.y3)(e);return o.NextResponse.json({success:!1,message:(0,y.Q$)(t,"errors.internal_error")},{status:500})}}async function v(e,{params:t}){try{let s=await (0,d.getServerSession)(m.N),r=(0,y.y3)(e);if(!s?.user?.email)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"user.unauthorized")},{status:401});await (0,u.A)();let{id:i}=await t;if(!_().Types.ObjectId.isValid(i))return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"errors.invalid_request")},{status:400});let a=await p.A.findOne({email:s.user.email});if(!a)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"user.not_found")},{status:404});let n=await l.A.findById(i);if(!n)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"tools.not_found")},{status:404});if(n.submittedBy!==a._id.toString())return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"errors.forbidden")},{status:403});let f=null;return n.orderId&&(f=await c.A.findById(n.orderId)),o.NextResponse.json({success:!0,data:{tool:{id:n._id,name:n.name,status:n.status,launchDateSelected:n.launchDateSelected,selectedLaunchDate:n.selectedLaunchDate,launchOption:n.launchOption,paymentRequired:n.paymentRequired,paymentStatus:n.paymentStatus},order:f}})}catch(s){console.error("Get launch date info error:",s);let t=(0,y.y3)(e);return o.NextResponse.json({success:!1,message:(0,y.Q$)(t,"errors.internal_error")},{status:500})}}async function x(e,{params:t}){try{let s=await (0,d.getServerSession)(m.N),r=(0,y.y3)(e);if(!s?.user?.email)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"user.unauthorized")},{status:401});await (0,u.A)();let{id:i}=await t,{selectedDate:a}=await e.json();if(!_().Types.ObjectId.isValid(i)||!a)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"errors.invalid_request")},{status:400});let n=await p.A.findOne({email:s.user.email});if(!n)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"user.not_found")},{status:404});let f=await l.A.findById(i);if(!f)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"tools.not_found")},{status:404});if(f.submittedBy!==n._id.toString())return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"errors.forbidden")},{status:403});if(!["pending","approved"].includes(f.status))return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"tools.edit_not_allowed")},{status:400});let g=new Date;if("approved"===f.status&&f.launchDate&&new Date(f.launchDate)<=g)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"tools.already_published")},{status:400});let h=new Date(a);if("paid"===f.launchOption){let e=new Date;if(e.setDate(e.getDate()+1),e.setHours(0,0,0,0),h<e)return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"tools.paid_date_restriction")},{status:400})}else{let e=new Date;e.setMonth(e.getMonth()+1),e.setHours(0,0,0,0);let t=new Date(h);if(t.setHours(0,0,0,0),t.getTime()<e.getTime())return o.NextResponse.json({success:!1,message:(0,y.Q$)(r,"tools.free_date_restriction")},{status:400})}return await l.A.findByIdAndUpdate(i,{$set:{selectedLaunchDate:h,launchDate:h}}),f.orderId&&await c.A.findByIdAndUpdate(f.orderId,{$set:{selectedLaunchDate:h}}),o.NextResponse.json({success:!0,data:{message:(0,y.Q$)(r,"tools.launch_date_updated"),selectedLaunchDate:h}})}catch(s){console.error("Update launch date error:",s);let t=(0,y.y3)(e);return o.NextResponse.json({success:!1,message:(0,y.Q$)(t,"errors.internal_error")},{status:500})}}let I=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/tools/[id]/launch-date/route",pathname:"/api/tools/[id]/launch-date",filename:"route",bundlePath:"app/api/tools/[id]/launch-date/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:b,workUnitAsyncStorage:w,serverHooks:A}=I;function S(){return(0,n.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:w})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,9658,580,3136],()=>s(89765));module.exports=r})();