(()=>{var e={};e.id=1569,e.ids=[1569],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(60687),a=s(93613),l=s(11860);function i({message:e,onClose:t,className:s=""}){return(0,r.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${s}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:e})}),t&&(0,r.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20856:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),l=s(88170),i=s.n(l),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c={children:["",{children:["[locale]",{children:["profile",{children:["submitted",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,52549)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/profile/submitted/page",pathname:"/[locale]/profile/submitted",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},25334:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32132:(e,t,s)=>{Promise.resolve().then(s.bind(s,93946))},33823:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(60687);function a({size:e="md",className:t=""}){return(0,r.jsx)("div",{className:`flex justify-center items-center ${t}`,children:(0,r.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49280:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/SubmittedToolsList.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/SubmittedToolsList.tsx","default")},52549:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(37413),a=s(61120),l=s(64348),i=s(49280);async function n(){return await (0,l.A)("profile.submitted"),(0,r.jsx)(a.Fragment,{children:(0,r.jsx)(i.default,{})})}},53411:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>x});var r=s(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:o,...x},m)=>(0,r.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:n("lucide",l),...!i&&!d(x)&&{"aria-hidden":"true"},...x},[...o.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(i)?i:[i]])),x=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...l},d)=>(0,r.createElement)(o,{ref:d,iconNode:t,className:n(`lucide-${a(i(e))}`,`lucide-${e}`,s),...l}));return s.displayName=i(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},78980:(e,t,s)=>{Promise.resolve().then(s.bind(s,49280))},79551:e=>{"use strict";e.exports=require("url")},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},93946:(e,t,s)=>{"use strict";s.d(t,{default:()=>_});var r=s(60687),a=s(43210),l=s(82136),i=s(12340),n=s(78521),d=s(77618),c=s(33823),o=s(11011),x=s(5336),m=s(48730),h=s(35071),p=s(63143),u=s(28559),g=s(96474),b=s(53411),j=s(13861),y=s(40228);let f=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var v=s(25334);let N=e=>{switch(e){case"approved":return"bg-green-100 text-green-800";case"pending":return"bg-yellow-100 text-yellow-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"approved":return(0,r.jsx)(x.A,{className:"h-4 w-4"});case"pending":return(0,r.jsx)(m.A,{className:"h-4 w-4"});case"rejected":return(0,r.jsx)(h.A,{className:"h-4 w-4"});case"draft":return(0,r.jsx)(p.A,{className:"h-4 w-4"});default:return null}};function _({}){let{data:e,status:t}=(0,l.useSession)(),s=(0,i.rd)(),m=(0,n.Ym)(),h=(0,d.c3)("profile.submitted"),[_,k]=(0,a.useState)("all"),[A,$]=(0,a.useState)([]),[C,M]=(0,a.useState)(!0),[D,S]=(0,a.useState)(""),z=async e=>{try{S("");let t=await fetch(`/api/tools/${e}/reapply`,{method:"POST",headers:{"Content-Type":"application/json"}}),r=await t.json();r.success?s.push(`/${m}/submit/reapply-launch-date/${e}`):S(r.message||h("reapply_failed"))}catch(e){S(h("network_error"))}},L=e=>h(`status.${e}`),P=A.filter(e=>"all"===_||e.status===_),q={total:A.length,draft:A.filter(e=>"draft"===e.status).length,approved:A.filter(e=>"approved"===e.status).length,pending:A.filter(e=>"pending"===e.status).length,rejected:A.filter(e=>"rejected"===e.status).length,totalViews:A.reduce((e,t)=>e+t.views,0),totalLikes:A.reduce((e,t)=>e+t.likes,0)};return"loading"===t||C?(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(c.A,{size:"lg",className:"py-20"})}):e?(0,r.jsx)(a.Fragment,{children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)(i.N_,{href:`/${m}/profile`,className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(u.A,{className:"h-5 w-5"})}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:h("title")})]}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:h("subtitle")})]}),(0,r.jsxs)(i.N_,{href:`/${m}/submit`,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(g.A,{className:"mr-2 h-5 w-5"}),h("submit_new_tool")]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(b.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:h("stats.total_submissions")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.total})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:h("stats.approved")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.approved})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(j.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:h("stats.total_views")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.totalViews})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-8 w-8 text-red-600",children:"❤️"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:h("stats.total_likes")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:q.totalLikes})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("button",{onClick:()=>k("all"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"all"===_?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[h("filters.all")," (",q.total,")"]}),(0,r.jsxs)("button",{onClick:()=>k("draft"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"draft"===_?"bg-gray-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[h("filters.draft")," (",q.draft,")"]}),(0,r.jsxs)("button",{onClick:()=>k("approved"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"approved"===_?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[h("filters.approved")," (",q.approved,")"]}),(0,r.jsxs)("button",{onClick:()=>k("pending"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"pending"===_?"bg-yellow-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[h("filters.pending")," (",q.pending,")"]}),(0,r.jsxs)("button",{onClick:()=>k("rejected"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"rejected"===_?"bg-red-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:[h("filters.rejected")," (",q.rejected,")"]})]})}),D&&(0,r.jsx)(o.A,{message:D,onClose:()=>S(""),className:"mb-6"}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:P.length>0?(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:P.map(e=>(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${N(e.status)}`,children:[w(e.status),(0,r.jsx)("span",{className:"ml-1",children:L(e.status)})]})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-3 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(y.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[h("dates.submitted_on")," ",new Date(e.submittedAt).toLocaleDateString("zh"===m?"zh-CN":"en-US")]})]}),e.launchDate&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[h("dates.published_on")," ",new Date(e.launchDate).toLocaleDateString("zh"===m?"zh-CN":"en-US")]})]}),"approved"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[e.views," ",h("metrics.views")]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"❤️"}),(0,r.jsxs)("span",{children:[e.likes," ",h("metrics.likes")]})]})]})]}),"rejected"===e.status&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg",children:[e.reviewNotes&&(0,r.jsxs)("p",{className:"text-sm text-red-800 mb-3",children:[(0,r.jsx)("strong",{children:h("rejection.reason")})," ",e.reviewNotes]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("p",{className:"text-sm text-red-700",children:h("rejection.edit_and_reapply")}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(i.N_,{href:`/${m}/submit/edit/${e._id}`,className:"inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(p.A,{className:"h-3 w-3 mr-1"}),h("actions.edit_info")]}),(0,r.jsxs)("button",{onClick:()=>z(e._id),className:"inline-flex items-center px-3 py-1 bg-red-600 text-white text-xs rounded-lg hover:bg-red-700 transition-colors",children:[(0,r.jsx)(f,{className:"h-3 w-3 mr-1"}),h("actions.reapply")]})]})]})]}),"draft"===e.status&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg",children:[(0,r.jsx)("p",{className:"text-sm text-blue-800 mb-2",children:(0,r.jsx)("strong",{children:h("next_steps.select_launch_date")})}),(0,r.jsx)(i.N_,{href:`/${m}/submit/launch-date/${e._id}`,className:"inline-flex items-center px-3 py-1 bg-blue-600 text-white text-xs rounded-lg hover:bg-blue-700 transition-colors",children:h("actions.set_launch_date")})]}),"pending"===e.status&&e.launchOption&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm text-yellow-800",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:h("next_steps.launch_option")})}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"paid"===e.launchOption?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"}`,children:h(`launch_options.${e.launchOption}`)})]}),e.selectedLaunchDate&&(0,r.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:h("dates.planned_launch")})}),(0,r.jsx)("span",{children:new Date(e.selectedLaunchDate).toLocaleDateString("zh"===m?"zh-CN":"en-US")})]}),e.paymentRequired&&(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:h("next_steps.payment_status")})}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"completed"===e.paymentStatus?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:h(`payment_status.${e.paymentStatus}`)})]}),(0,r.jsx)("div",{className:"flex justify-end mt-2",children:(0,r.jsxs)(i.N_,{href:`/${m}/submit/edit-launch-date/${e._id}`,className:"inline-flex items-center px-3 py-1 bg-yellow-600 text-white text-xs rounded-lg hover:bg-yellow-700 transition-colors",children:[(0,r.jsx)(y.A,{className:"h-3 w-3 mr-1"}),h("actions.modify_launch_date")]})})]})}),"approved"===e.status&&e.launchOption&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-green-50 border border-green-200 rounded-lg",children:(0,r.jsxs)("div",{className:"text-sm text-green-800",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:h("next_steps.launch_option")})}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs ${"paid"===e.launchOption?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"}`,children:h(`launch_options.${e.launchOption}`)})]}),e.selectedLaunchDate&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{children:(0,r.jsx)("strong",{children:h("dates.launch_date")})}),(0,r.jsx)("span",{children:new Date(e.selectedLaunchDate).toLocaleDateString("zh"===m?"zh-CN":"en-US")})]})]})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:["approved"===e.status&&(0,r.jsx)(i.N_,{href:`/${m}/tools/${e._id}`,className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:h("tooltips.view_details"),children:(0,r.jsx)(j.A,{className:"h-5 w-5"})}),"draft"===e.status&&!e.launchDateSelected&&(0,r.jsx)(i.N_,{href:`/${m}/submit/launch-date/${e._id}`,className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:h("tooltips.set_launch_date"),children:(0,r.jsx)(y.A,{className:"h-5 w-5"})}),["pending","approved"].includes(e.status)&&e.launchDateSelected&&(0,r.jsx)(i.N_,{href:`/${m}/submit/edit-launch-date/${e._id}`,className:"p-2 text-gray-400 hover:text-orange-600 transition-colors",title:h("tooltips.modify_launch_date"),children:(0,r.jsx)(y.A,{className:"h-5 w-5"})}),(0,r.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-green-600 transition-colors",title:h("tooltips.visit_website"),children:(0,r.jsx)(v.A,{className:"h-5 w-5"})}),["draft","pending","rejected","approved","published"].includes(e.status)&&(0,r.jsx)(i.N_,{href:`/${m}/submit/edit/${e._id}`,className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"approved"===e.status&&e.launchDate&&new Date(e.launchDate)<=new Date?h("tooltips.edit_basic_info"):"approved"===e.status?h("tooltips.edit_basic_info_no_url"):h("tooltips.edit_tool_info"),children:(0,r.jsx)(p.A,{className:"h-5 w-5"})})]})]})},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(b.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"all"===_?h("empty_states.no_tools"):h("empty_states.no_status_tools",{status:L(_)})}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"all"===_?h("empty_states.get_started"):h("empty_states.try_other_status")}),"all"===_&&(0,r.jsxs)(i.N_,{href:`/${m}/submit`,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4"}),h("submit_new_tool")]})]})})]})}):null}},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,9658,6435,6699,8232,2585],()=>s(20856));module.exports=r})();