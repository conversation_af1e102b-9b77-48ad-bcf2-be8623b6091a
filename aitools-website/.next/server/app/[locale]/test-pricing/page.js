(()=>{var e={};e.id=5385,e.ids=[5385],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10630:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>o});var t=r(65239),a=r(48088),i=r(88170),l=r.n(i),d=r(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(s,n);let o={children:["",{children:["[locale]",{children:["test-pricing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,22827)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/test-pricing/page",pathname:"/[locale]/test-pricing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22827:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43252:(e,s,r)=>{Promise.resolve().then(r.bind(r,58133))},58133:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(60687);r(43210);var a=r(98402),i=r.n(a),l=r(94865);function d(){return(0,t.jsx)(i(),{children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto py-8 px-4",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"统一价格配置测试页面"}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"基础价格配置 (PRICING_CONFIG)"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"优先发布服务"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("p",{children:["显示价格: ",(0,l.$g)(l.kX.PRIORITY_LAUNCH.displayPrice)]}),(0,t.jsxs)("p",{children:["Stripe金额: ",l.kX.PRIORITY_LAUNCH.stripeAmount," 分"]}),(0,t.jsxs)("p",{children:["格式化Stripe金额: ",(0,l.tF)(l.kX.PRIORITY_LAUNCH.stripeAmount)]}),(0,t.jsxs)("p",{children:["产品名称: ",l.kX.PRIORITY_LAUNCH.productName]}),(0,t.jsxs)("p",{children:["描述: ",l.kX.PRIORITY_LAUNCH.description]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"免费发布服务"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("p",{children:["显示价格: ",(0,l.$g)(l.kX.FREE_LAUNCH.displayPrice)]}),(0,t.jsxs)("p",{children:["Stripe金额: ",l.kX.FREE_LAUNCH.stripeAmount," 分"]}),(0,t.jsxs)("p",{children:["格式化Stripe金额: ",(0,l.tF)(l.kX.FREE_LAUNCH.stripeAmount)]}),(0,t.jsxs)("p",{children:["产品名称: ",l.kX.FREE_LAUNCH.productName]}),(0,t.jsxs)("p",{children:["描述: ",l.kX.FREE_LAUNCH.description]})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"发布选项配置 (LAUNCH_OPTIONS)"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:l.vS.map(e=>(0,t.jsxs)("div",{className:`p-4 rounded-lg border-2 ${e.recommended?"border-blue-500 bg-blue-50":"border-gray-200"}`,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:e.title}),e.recommended&&(0,t.jsx)("span",{className:"bg-blue-500 text-white text-xs px-2 py-1 rounded",children:"推荐"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),(0,t.jsx)("p",{className:"text-lg font-semibold text-blue-600 mb-2",children:(0,l.$g)(e.price)}),(0,t.jsx)("ul",{className:"text-xs text-gray-500 space-y-1",children:e.features.map((e,s)=>(0,t.jsxs)("li",{children:["• ",e]},s))})]},e.id))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"工具定价选项"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"筛选选项 (TOOL_PRICING_OPTIONS)"}),(0,t.jsx)("div",{className:"space-y-2",children:l.v4.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:`px-2 py-1 rounded text-xs ${(0,l.Ef)(e.value)}`,children:e.label}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["(",e.value||"全部",")"]})]},e.value))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"表单选项 (TOOL_PRICING_FORM_OPTIONS)"}),(0,t.jsx)("div",{className:"space-y-2",children:l.Y$.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:`px-2 py-1 rounded text-xs ${(0,l.Ef)(e.value)}`,children:(0,l.mV)(e.value)}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["(",e.value,")"]})]},e.value))})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"辅助函数测试"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"价格格式化"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("p",{children:["formatPrice(0): ",(0,l.$g)(0)]}),(0,t.jsxs)("p",{children:["formatPrice(99): ",(0,l.$g)(99)]}),(0,t.jsxs)("p",{children:["formatPrice(199): ",(0,l.$g)(199)]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"Stripe金额格式化"}),(0,t.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,t.jsxs)("p",{children:["formatStripeAmount(0): ",(0,l.tF)(0)]}),(0,t.jsxs)("p",{children:["formatStripeAmount(9900): ",(0,l.tF)(9900)]}),(0,t.jsxs)("p",{children:["formatStripeAmount(19900): ",(0,l.tF)(19900)]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"定价类型样式"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("span",{className:`px-2 py-1 rounded text-xs ${(0,l.Ef)("free")}`,children:(0,l.mV)("free")}),(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:`px-2 py-1 rounded text-xs ${(0,l.Ef)("freemium")}`,children:(0,l.mV)("freemium")}),(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:`px-2 py-1 rounded text-xs ${(0,l.Ef)("paid")}`,children:(0,l.mV)("paid")})]})]})]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},90516:(e,s,r)=>{Promise.resolve().then(r.bind(r,22827))},94865:(e,s,r)=>{"use strict";r.d(s,{$g:()=>c,Ef:()=>n,Y$:()=>d,kX:()=>t,mV:()=>o,tF:()=>p,v4:()=>l,vS:()=>a});let t={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:t.FREE_LAUNCH.description,price:t.FREE_LAUNCH.displayPrice,features:t.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:t.PRIORITY_LAUNCH.description,price:t.PRIORITY_LAUNCH.displayPrice,features:t.PRIORITY_LAUNCH.features,recommended:!0}],i={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},l=[{value:"",label:"所有价格"},{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],d=[{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],n=e=>{switch(e){case i.FREE.value:return i.FREE.color;case i.FREEMIUM.value:return i.FREEMIUM.color;case i.PAID.value:return i.PAID.color;default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case i.FREE.value:return i.FREE.label;case i.FREEMIUM.value:return i.FREEMIUM.label;case i.PAID.value:return i.PAID.label;default:return e}},c=e=>0===e?"免费":`\xa5${e}`,p=(e,s="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:s.toUpperCase(),minimumFractionDigits:2}).format(e/100)},98402:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,4999,9658,6435,6699,8232,2585],()=>r(10630));module.exports=t})();