(()=>{var e={};e.id=6249,e.ids=[6249],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7485:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(60687);s(43210);var a=s(12340),n=s(77618),o=s(82136),i=s(23877),l=s(2328);function c({toolId:e,initialLikes:t=0,initialLiked:s=!1,onLoginRequired:c,onUnlike:d,isInLikedPage:u=!1,showCount:m=!0,size:p="md"}){let{data:h}=(0,o.useSession)(),{getToolState:x,initializeToolState:g,toggleLike:y}=(0,l.X)(),f=(0,a.a8)(),b=(0,n.c3)("common");f?.startsWith("/en");let v=x(e),j=async()=>{if(!h)return void c?.();if(v.loading)return;let t=v.liked;await y(e,u)&&u&&t&&d&&d(e)},N=(()=>{switch(p){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,r.jsxs)("button",{onClick:j,disabled:v.loading,className:`
        ${N.button}
        inline-flex items-center space-x-1
        ${v.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500"}
        transition-colors duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
      `,title:b(v.liked?"unlike":"like"),children:[v.loading?(0,r.jsx)("div",{className:`${N.icon} animate-spin rounded-full border-2 border-gray-300 border-t-red-500`}):v.liked?(0,r.jsx)(i.Mbv,{className:N.icon}):(0,r.jsx)(i.sOK,{className:N.icon}),m&&(0,r.jsx)("span",{className:`${N.text} font-medium`,children:v.likes})]})}},10806:(e,t,s)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function n(){return"production"}function o(){return"development"===n()}s.d(t,{u:()=>d});let i={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:n(),isDevelopment:o(),isProduction:"production"===n(),port:process.env.PORT||"3001"};o()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",i.baseUrl),console.log("  API Base URL:",i.apiBaseUrl),console.log("  NextAuth URL:",i.nextAuthUrl),console.log("  Environment:",i.environment),console.log("  Port:",i.port));let l=a();class c{constructor(e=l){this.baseURL=e}async request(e,t={}){try{let s=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(s,r),n=await a.json();if(!a.ok)throw Error(n.error||`HTTP error! status: ${a.status}`);return n}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/tools${s?`?${s}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/user/liked-tools${s?`?${s}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/admin/tools${s?`?${s}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}}let d=new c},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22338:(e,t,s)=>{"use strict";s.d(t,{default:()=>v});var r=s(60687),a=s(43210),n=s(12340),o=s(77618),i=s(7485),l=s(82136),c=s(23877);function d({toolId:e,onLoginRequired:t}){let{data:s}=(0,l.useSession)(),[i,d]=(0,a.useState)([]),[u,m]=(0,a.useState)(""),[p,h]=(0,a.useState)(null),[x,g]=(0,a.useState)(""),[y,f]=(0,a.useState)(!1),[b,v]=(0,a.useState)(!1),j=(0,n.a8)(),N=(0,o.c3)("comments"),w=j?.startsWith("/en")?"en":"zh",_=async()=>{f(!0);try{let t=await fetch(`/api/tools/${e}/comments`);if(t.ok){let e=await t.json();e.success&&d(e.data.comments)}}catch(e){console.error("Failed to fetch comments:",e)}finally{f(!1)}},A=async()=>{if(!s)return void t?.();if(u.trim()){v(!0);try{let t=await fetch(`/api/tools/${e}/comments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:u.trim()})});if(t.ok)(await t.json()).success&&(m(""),_());else{let e=await t.json();console.error("Comment submission failed:",e.message)}}catch(e){console.error("Comment submission error:",e)}finally{v(!1)}}},E=async r=>{if(!s)return void t?.();if(x.trim()){v(!0);try{let t=await fetch(`/api/tools/${e}/comments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:x.trim(),parentId:r})});if(t.ok)(await t.json()).success&&(g(""),h(null),_());else{let e=await t.json();console.error("Reply submission failed:",e.message)}}catch(e){console.error("Reply submission error:",e)}finally{v(!1)}}},U=e=>{let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/36e5);return s<1?N("just_now"):s<24?N("hours_ago",{hours:s}):s<168?N("days_ago",{days:Math.floor(s/24)}):t.toLocaleDateString("zh"===w?"zh-CN":"en-US")},R=({comment:e,isReply:t=!1})=>(0,r.jsx)("div",{className:`${t?"ml-8 border-l-2 border-gray-100 pl-4":""}`,children:(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:e.userId.image?(0,r.jsx)("img",{src:e.userId.image,alt:e.userId.name,className:"w-8 h-8 rounded-full"}):(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)(c.x$1,{className:"w-4 h-4 text-gray-600"})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsx)("span",{className:"font-medium text-gray-900",children:e.userId.name}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:U(e.createdAt)})]}),(0,r.jsx)("p",{className:"text-gray-700 mb-2",children:e.content}),(0,r.jsx)("div",{className:"flex items-center gap-4",children:!t&&(0,r.jsxs)("button",{onClick:()=>h(p===e._id?null:e._id),className:"text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1",children:[(0,r.jsx)(c.w1Z,{className:"w-3 h-3"}),N("reply")]})}),p===e._id&&(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsx)("textarea",{value:x,onChange:e=>g(e.target.value),placeholder:N("write_reply"),className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3,maxLength:1e3}),(0,r.jsxs)("div",{className:"flex justify-end gap-2 mt-2",children:[(0,r.jsx)("button",{onClick:()=>{h(null),g("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:N("cancel")}),(0,r.jsx)("button",{onClick:()=>E(e._id),disabled:b||!x.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:b?N("submitting"):N("send")})]})]}),e.replies&&e.replies.length>0&&(0,r.jsx)("div",{className:"mt-4 space-y-4",children:e.replies.map(e=>(0,r.jsx)(R,{comment:e,isReply:!0},e._id))})]})]})});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:N("title",{count:i.length})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("textarea",{value:u,onChange:e=>m(e.target.value),placeholder:s?N("write_comment"):N("login_to_comment"),className:"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,maxLength:1e3,disabled:!s}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[u.length,"/1000"]}),(0,r.jsx)("button",{onClick:A,disabled:b||!u.trim()||!s,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:b?N("submitting"):N("submit_comment")})]})]}),y?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"text-gray-500 mt-2",children:N("loading")})]}):0===i.length?(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-gray-500",children:N("no_comments")})}):(0,r.jsx)("div",{className:"space-y-6",children:i.map(e=>(0,r.jsx)(R,{comment:e},e._id))})]})}var u=s(48577);s(56976);var m=s(94865),p=s(62688);let h=(0,p.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var x=s(13861),g=s(67760);let y=(0,p.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var f=s(37360),b=s(25334);function v({initialTool:e,toolId:t}){let[s,l]=(0,a.useState)(e),[c,p]=(0,a.useState)([]),[v,j]=(0,a.useState)(!1),N=(0,n.a8)(),w=(0,o.c3)("tool_detail"),_=N?.startsWith("/en")?"en":"zh";return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)("article",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,r.jsxs)("header",{className:"flex items-start justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[s.logo?(0,r.jsx)("img",{src:s.logo,alt:`${s.name} logo`,className:"w-16 h-16 rounded-lg object-cover"}):(0,r.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-2xl",children:s.name.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:s.name}),s.tagline&&(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-3",children:s.tagline}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${(0,m.Ef)(s.pricing)}`,children:[(0,r.jsx)(h,{className:"mr-1 h-4 w-4"}),(0,m.mV)(s.pricing)]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:w("views",{count:s.views||0})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:w("likes",{count:s.likes||0})})]})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.A,{toolId:s._id,initialLikes:s.likes,onLoginRequired:()=>j(!0)}),(0,r.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-500 transition-colors",children:(0,r.jsx)(y,{className:"h-5 w-5"})})]})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("p",{className:"text-gray-600 text-lg leading-relaxed",children:s.description})}),s.tags&&s.tags.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:s.tags.map((e,t)=>(0,r.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer",children:[(0,r.jsx)(f.A,{className:"mr-1 h-3 w-3"}),e]},t))}),(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-4",children:(0,r.jsxs)("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(b.A,{className:"mr-2 h-5 w-5"}),w("visit_tool",{name:s.name})]})})]})}),(0,r.jsxs)("aside",{className:"lg:col-span-1",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:w("tool_info")}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:w("category")}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:s.category})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:w("pricing_model")}),(0,r.jsx)("span",{className:`px-2 py-1 rounded text-sm font-medium ${(0,m.Ef)(s.pricing)}`,children:(0,m.mV)(s.pricing)})]}),s.launchDate&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-600",children:w("launch_date")}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:new Date(s.launchDate).toLocaleDateString("zh"===_?"zh-CN":"en-US")})]})]})]}),c.length>0&&(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:w("related_tools")}),(0,r.jsx)("div",{className:"space-y-4",children:c.map(e=>(0,r.jsx)("div",{children:(0,r.jsx)(n.N_,{href:`/tools/${e._id}`,className:"block p-3 rounded-lg hover:bg-gray-50 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,r.jsx)("img",{src:e.logo,alt:e.name,className:"w-10 h-10 rounded object-cover"}):(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:e.name.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500 mt-1",children:[(0,r.jsx)("span",{className:`px-2 py-1 rounded ${(0,m.Ef)(e.pricing)}`,children:(0,m.mV)(e.pricing)}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{children:w("views",{count:e.views||0})}),(0,r.jsx)("span",{children:w("likes",{count:e.likes||0})})]})]})]})]})})},e._id))})]})]})]}),(0,r.jsx)("div",{className:"mt-12",children:(0,r.jsx)(d,{toolId:s._id,onLoginRequired:()=>j(!0)})}),(0,r.jsx)(u.A,{isOpen:v,onClose:()=>j(!1)})]})}},25311:(e,t,s)=>{Promise.resolve().then(s.bind(s,39130)),Promise.resolve().then(s.bind(s,22338))},25334:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37360:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},54290:(e,t,s)=>{"use strict";s.d(t,{B3:()=>a,L2:()=>n,eb:()=>l,hC:()=>i,sU:()=>o});let r=process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com";function a(){return{"@context":"https://schema.org","@type":"WebSite",name:"AI工具导航",description:"发现最好的AI工具，提升您的工作效率和创造力",url:r,potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:`${r}/tools?search={search_term_string}`},"query-input":"required name=search_term_string"},publisher:{"@type":"Organization",name:"AI工具导航",url:r}}}function n(){return{"@context":"https://schema.org","@type":"Organization",name:"AI工具导航",description:"专业的AI工具发现和推荐平台",url:r,logo:`${r}/logo.png`,sameAs:[]}}function o(e){return{"@context":"https://schema.org","@type":"SoftwareApplication",name:e.name,description:e.description,url:e.website,applicationCategory:"AI工具",operatingSystem:"Web",offers:{"@type":"Offer",price:"free"===e.pricing?"0":void 0,priceCurrency:"USD",availability:"https://schema.org/InStock"},aggregateRating:e.likes?{"@type":"AggregateRating",ratingValue:Math.min(5,Math.max(1,e.likes/10+3)),reviewCount:e.likes,bestRating:5,worstRating:1}:void 0,image:e.logo||`${r}/default-tool-image.jpg`,datePublished:e.launchDate,publisher:{"@type":"Organization",name:"AI工具导航",url:r}}}function i(e){return{"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:e.map((e,t)=>({"@type":"ListItem",position:t+1,name:e.name,item:`${r}${e.url}`}))}}function l(e,t){return{"@context":"https://schema.org","@type":"ItemList",name:t?`${t} AI工具`:"AI工具列表",description:t?`发现最好的${t} AI工具`:"发现最好的AI工具",numberOfItems:e.length,itemListElement:e.map((e,t)=>({"@type":"ListItem",position:t+1,item:{"@type":"SoftwareApplication",name:e.name,description:e.description,url:`${r}/tools/${e._id}`,image:e.logo||`${r}/default-tool-image.jpg`}}))}}},56976:(e,t,s)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3001";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function n(){return"production"}function o(){return"development"===n()}s.d(t,{u:()=>d});let i={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:n(),isDevelopment:o(),isProduction:"production"===n(),port:process.env.PORT||"3001"};o()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",i.baseUrl),console.log("  API Base URL:",i.apiBaseUrl),console.log("  NextAuth URL:",i.nextAuthUrl),console.log("  Environment:",i.environment),console.log("  Port:",i.port));let l=a();class c{constructor(e=l){this.baseURL=e}async request(e,t={}){try{let s=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(s,r),n=await a.json();if(!a.ok)throw Error(n.error||`HTTP error! status: ${a.status}`);return n}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/tools${s?`?${s}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/user/liked-tools${s?`?${s}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,s])=>{void 0!==s&&t.append(e,s.toString())});let s=t.toString();return this.request(`/admin/tools${s?`?${s}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}}let d=new c},62688:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),o=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=(...e)=>e.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:s=2,absoluteStrokeWidth:a,className:n="",children:o,iconNode:d,...u},m)=>(0,r.createElement)("svg",{ref:m,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(s)/Number(t):s,className:i("lucide",n),...!o&&!l(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(o)?o:[o]])),u=(e,t)=>{let s=(0,r.forwardRef)(({className:s,...n},l)=>(0,r.createElement)(d,{ref:l,iconNode:t,className:i(`lucide-${a(o(e))}`,`lucide-${e}`,s),...n}));return s.displayName=o(e),s}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63687:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u,generateMetadata:()=>d});var r=s(37413);s(61120);var a=s(78878),n=s(39916),o=s(64348),i=s(76106),l=s(10806),c=s(54290);async function d({params:e}){try{let{id:t,locale:s}=await e,r=await (0,o.A)({locale:s,namespace:"tool_detail"}),a=await l.u.getTool(t);if(!a.success||!a.data){let e=await (0,o.A)({locale:s,namespace:"site"});return{title:`${r("not_found")} - ${e("title")}`,description:r("not_found_desc")}}let n=a.data,i=await (0,o.A)({locale:s,namespace:"site"}),c=`${n.name} - ${i("title")}`,d=n.description||`${n.name} is an excellent AI tool to boost your productivity.`,u=[n.name,...n.tags||[],"AI tools",n.category].join(", "),m=process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",p=`/tools/${n._id}`,h=n.logo||"/og-tool-default.jpg";return{title:c,description:d,keywords:u,authors:[{name:i("title")}],robots:{index:!0,follow:!0},openGraph:{type:"article",locale:"zh"===s?"zh_CN":"en_US",url:`${m}${p}`,siteName:i("title"),title:c,description:d,images:[{url:h.startsWith("http")?h:`${m}${h}`,width:1200,height:630,alt:`${n.name} - ${i("title")}`}],publishedTime:n.launchDate?new Date(n.launchDate).toISOString():void 0,modifiedTime:n.updatedAt?new Date(n.updatedAt).toISOString():void 0},twitter:{card:"summary_large_image",title:c,description:d,images:[h.startsWith("http")?h:`${m}${h}`]},alternates:{canonical:`${m}${p}`}}}catch(s){let e=await (0,o.A)({locale:"zh",namespace:"site"}),t=await (0,o.A)({locale:"zh",namespace:"tool_detail"});return{title:`${t("page_title")} - ${e("title")}`,description:t("not_found_desc")}}}async function u({params:e}){try{let{id:t,locale:s}=await e,d=await (0,o.A)({locale:s,namespace:"tool_detail"}),u=await l.u.getTool(t);u.success&&u.data||(0,n.notFound)();let m=u.data;"approved"===m.status&&m.launchDate&&new Date(m.launchDate)<=new Date||(0,n.notFound)();let p=(0,c.sU)(m),h=(0,c.hC)([{name:d("breadcrumb_home"),url:"/"},{name:d("breadcrumb_tools"),url:"/tools"},{name:m.name,url:`/tools/${m._id}`}]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(p)}}),(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(h)}}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6","aria-label":d("breadcrumb_aria_label"),children:[(0,r.jsx)(a.N_,{href:"/",className:"hover:text-blue-600",children:d("breadcrumb_home")}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)(a.N_,{href:"/tools",className:"hover:text-blue-600",children:d("breadcrumb_tools")}),(0,r.jsx)("span",{children:"/"}),(0,r.jsx)("span",{className:"text-gray-900",children:m.name})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)(a.N_,{href:"/tools",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,r.jsx)("svg",{className:"mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),d("back_to_tools")]})}),(0,r.jsx)(i.default,{initialTool:m,toolId:t})]})]})}catch(e){console.error("Error loading tool:",e),(0,n.notFound)()}}},67760:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},76106:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},86442:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),o=s.n(n),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let c={children:["",{children:["[locale]",{children:["tools",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,63687)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/tools/[id]/page",pathname:"/[locale]/tools/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94865:(e,t,s)=>{"use strict";s.d(t,{$g:()=>d,Ef:()=>l,Y$:()=>i,kX:()=>r,mV:()=>c,tF:()=>u,v4:()=>o,vS:()=>a});let r={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:r.FREE_LAUNCH.description,price:r.FREE_LAUNCH.displayPrice,features:r.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:r.PRIORITY_LAUNCH.description,price:r.PRIORITY_LAUNCH.displayPrice,features:r.PRIORITY_LAUNCH.features,recommended:!0}],n={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},o=[{value:"",label:"所有价格"},{value:n.FREE.value,label:n.FREE.label},{value:n.FREEMIUM.value,label:n.FREEMIUM.label},{value:n.PAID.value,label:n.PAID.label}],i=[{value:n.FREE.value,label:n.FREE.label},{value:n.FREEMIUM.value,label:n.FREEMIUM.label},{value:n.PAID.value,label:n.PAID.label}],l=e=>{switch(e){case n.FREE.value:return n.FREE.color;case n.FREEMIUM.value:return n.FREEMIUM.color;case n.PAID.value:return n.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case n.FREE.value:return n.FREE.label;case n.FREEMIUM.value:return n.FREEMIUM.label;case n.PAID.value:return n.PAID.label;default:return e}},d=e=>0===e?"免费":`\xa5${e}`,u=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)},95047:(e,t,s)=>{Promise.resolve().then(s.bind(s,75788)),Promise.resolve().then(s.bind(s,76106))}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,4999,9658,6435,6699,8232,2585],()=>s(86442));module.exports=r})();