(()=>{var e={};e.id=1298,e.ids=[1298],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9962:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=s(65239),l=s(48088),a=s(88170),i=s.n(a),o=s(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);s.d(r,n);let d={children:["",{children:["[locale]",{children:["submit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,41752)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/[locale]/submit/page",pathname:"/[locale]/submit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,r,s)=>{"use strict";s.d(r,{A:()=>i});var t=s(60687),l=s(93613),a=s(11860);function i({message:e,onClose:r,className:s=""}){return(0,t.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${s}`,children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(l.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)("p",{className:"text-red-800 text-sm",children:e})}),r&&(0,t.jsx)("button",{onClick:r,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,t.jsx)(a.A,{className:"w-4 h-4"})})]})})}},11879:(e,r,s)=>{Promise.resolve().then(s.bind(s,42709))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26373:(e,r,s)=>{"use strict";s.d(r,{A:()=>m});var t=s(61120);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,s)=>s?s.toUpperCase():r.toLowerCase()),i=e=>{let r=a(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=(...e)=>e.filter((e,r,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===r).join(" ").trim(),n=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,t.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:a="",children:i,iconNode:c,...m},u)=>(0,t.createElement)("svg",{ref:u,...d,width:r,height:r,stroke:e,strokeWidth:l?24*Number(s)/Number(r):s,className:o("lucide",a),...!i&&!n(m)&&{"aria-hidden":"true"},...m},[...c.map(([e,r])=>(0,t.createElement)(e,r)),...Array.isArray(i)?i:[i]])),m=(e,r)=>{let s=(0,t.forwardRef)(({className:s,...a},n)=>(0,t.createElement)(c,{ref:n,iconNode:r,className:o(`lucide-${l(i(e))}`,`lucide-${e}`,s),...a}));return s.displayName=i(e),s}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41752:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var t=s(37413),l=s(61120);let a=(0,s(26373).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var i=s(64348),o=s(42709);async function n(){let e=await (0,i.A)("submit");return(0,t.jsx)(l.Fragment,{children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"flex justify-center mb-4",children:(0,t.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,t.jsx)(a,{className:"h-8 w-8 text-blue-600"})})}),(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:e("title")}),(0,t.jsx)("p",{className:"text-lg text-gray-600",children:e("subtitle")})]}),(0,t.jsx)(o.default,{})]})})}},42143:(e,r,s)=>{Promise.resolve().then(s.bind(s,71396))},42709:(e,r,s)=>{"use strict";s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitForm.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71396:(e,r,s)=>{"use strict";s.d(r,{default:()=>v});var t=s(60687),l=s(43210),a=s(82136),i=s(12340),o=s(77618),n=s(33823),d=s(11011),c=s(78890),m=s(48577),u=s(94865),x=s(26860),p=s(62688);let h=(0,p.A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);var g=s(16023);let b=(0,p.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var f=s(83938),j=s(15421);function v({}){let e=(0,o.c3)("submit"),{data:r}=(0,a.useSession)(),s=(0,i.rd)(),p=(0,x.OD)(),[v,y]=(0,l.useState)({name:"",tagline:"",description:"",websiteUrl:"",logoFile:null,category:"",tags:[],pricingModel:""}),[w,N]=(0,l.useState)(null),[k,_]=(0,l.useState)(!1),[A,C]=(0,l.useState)("idle"),[P,F]=(0,l.useState)(!1),M=e=>{let{name:r,value:s}=e.target;y(e=>({...e,[r]:s}))},q=async e=>{if(e.preventDefault(),!r)return void F(!0);_(!0),C("idle");try{let e=new FormData;e.append("name",v.name),e.append("tagline",v.tagline),e.append("description",v.description),e.append("websiteUrl",v.websiteUrl),e.append("category",v.category),e.append("tags",JSON.stringify(v.tags)),e.append("pricingModel",v.pricingModel),v.logoFile&&e.append("logo",v.logoFile),(await fetch("/api/tools/submit",{method:"POST",body:e})).ok?(C("success"),y({name:"",tagline:"",description:"",websiteUrl:"",logoFile:null,category:"",tags:[],pricingModel:""}),N(null),setTimeout(()=>{s.push("/profile/submitted")},2e3)):C("error")}catch(e){console.error("Submit error:",e),C("error")}finally{_(!1)}};return(0,t.jsxs)(l.Fragment,{children:["success"===A&&(0,t.jsx)(c.A,{message:e("form.success_message")}),"error"===A&&(0,t.jsx)(d.A,{message:e("form.error_message")}),(0,t.jsxs)("form",{onSubmit:q,className:"space-y-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:e("form.basic_info")}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[e("form.tool_name")," *"]}),(0,t.jsx)("input",{type:"text",id:"name",name:"name",value:v.name,onChange:M,placeholder:e("form.tool_name_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"tagline",className:"block text-sm font-medium text-gray-700 mb-2",children:e("form.tagline")}),(0,t.jsx)("input",{type:"text",id:"tagline",name:"tagline",value:v.tagline,onChange:M,placeholder:e("form.tagline_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"websiteUrl",className:"block text-sm font-medium text-gray-700 mb-2",children:[e("form.website_url")," *"]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(h,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{type:"url",id:"websiteUrl",name:"websiteUrl",value:v.websiteUrl,onChange:M,placeholder:e("form.website_url_placeholder"),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[e("form.description")," *"]}),(0,t.jsx)("textarea",{id:"description",name:"description",value:v.description,onChange:M,placeholder:e("form.description_placeholder"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("form.logo_upload")}),(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,t.jsx)("input",{type:"file",id:"logo",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>{let r=e.target.files?.[0];if(r){if(!["image/jpeg","image/png","image/gif","image/webp"].includes(r.type))return void alert("Please upload a valid image file (JPEG, PNG, GIF, or WebP)");if(r.size>5242880)return void alert("File size must be less than 5MB");y(e=>({...e,logoFile:r}));let e=new FileReader;e.onload=e=>{N(e.target?.result)},e.readAsDataURL(r)}},className:"hidden"}),(0,t.jsxs)("label",{htmlFor:"logo",className:"cursor-pointer",children:[(0,t.jsx)(g.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsx)("span",{className:"text-sm text-blue-600 hover:text-blue-500",children:"Click to upload"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e("form.logo_upload_hint")})]})]})]})}),w&&(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-24 h-24 border border-gray-300 rounded-lg overflow-hidden",children:(0,t.jsx)("img",{src:w,alt:e("form.logo_preview"),className:"w-full h-full object-cover"})})})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:e("form.category_and_pricing")}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:[e("form.category")," *"]}),(0,t.jsxs)("select",{id:"category",name:"category",value:v.category,onChange:M,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,t.jsx)("option",{value:"",children:e("form.category_placeholder")}),p.map(e=>(0,t.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:e("form.tags")}),(0,t.jsx)(j.A,{selectedTags:v.tags,onTagsChange:e=>{y(r=>({...r,tags:e}))},maxTags:f.z,placeholder:e("form.tags_placeholder")})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"pricingModel",className:"block text-sm font-medium text-gray-700 mb-2",children:[e("form.pricing_model")," *"]}),(0,t.jsxs)("select",{id:"pricingModel",name:"pricingModel",value:v.pricingModel,onChange:M,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,t.jsx)("option",{value:"",children:e("form.pricing_placeholder")}),u.Y$.map(r=>(0,t.jsx)("option",{value:r.value,children:e(`form.${r.value}`)},r.value))]})]})]})]}),r&&(0,t.jsxs)("div",{className:"mb-8 bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-green-800 mb-2",children:e("form.submitter_info")}),(0,t.jsxs)("p",{className:"text-sm text-green-700",children:[e("form.submitter"),": ",r.user?.name||r.user?.email]}),(0,t.jsxs)("p",{className:"text-sm text-green-700",children:[e("form.email"),": ",r.user?.email]})]}),(0,t.jsx)("div",{className:"mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(b,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:e("form.guidelines_title")}),(0,t.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsxs)("li",{children:["• ",e("form.guideline_1")]}),(0,t.jsxs)("li",{children:["• ",e("form.guideline_2")]}),(0,t.jsxs)("li",{children:["• ",e("form.guideline_3")]}),(0,t.jsxs)("li",{children:["• ",e("form.guideline_4")]}),(0,t.jsxs)("li",{children:["• ",e("form.guideline_5")]})]})]})]})}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("button",{type:"submit",disabled:k,className:`px-8 py-3 rounded-lg font-medium transition-colors ${k?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:k?(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.A,{size:"sm",className:"mr-2"}),e("form.submitting")]}):e("form.submit_button")})})]}),(0,t.jsx)(m.A,{isOpen:P,onClose:()=>F(!1)})]})}},78890:(e,r,s)=>{"use strict";s.d(r,{A:()=>i});var t=s(60687),l=s(5336),a=s(11860);function i({message:e,onClose:r,className:s=""}){return(0,t.jsx)("div",{className:`bg-green-50 border border-green-200 rounded-lg p-4 ${s}`,children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(l.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)("p",{className:"text-green-800 text-sm",children:e})}),r&&(0,t.jsx)("button",{onClick:r,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,t.jsx)(a.A,{className:"w-4 h-4"})})]})})}},79551:e=>{"use strict";e.exports=require("url")},93613:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,4999,9658,6435,6699,8232,2585,3572],()=>s(9962));module.exports=t})();