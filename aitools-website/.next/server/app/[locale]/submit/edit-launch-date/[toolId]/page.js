(()=>{var e={};e.id=1068,e.ids=[1068],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6130:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=s(65239),r=s(48088),o=s(88170),i=s.n(o),l=s(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(t,n);let d={children:["",{children:["[locale]",{children:["submit",{children:["edit-launch-date",{children:["[toolId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,35786)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit-launch-date/[toolId]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit-launch-date/[toolId]/page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/[locale]/submit/edit-launch-date/[toolId]/page",pathname:"/[locale]/submit/edit-launch-date/[toolId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35786:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit-launch-date/[toolId]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit-launch-date/[toolId]/page.tsx","default")},37955:(e,t,s)=>{Promise.resolve().then(s.bind(s,47316))},47316:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var a=s(60687),r=s(43210),o=s(12340),i=s(16189),l=s(82136),n=s(98402),d=s.n(n),c=s(32498),p=s(93613),u=s(28559),x=s(5336);function m(){let e=(0,i.useParams)(),t=(0,o.rd)(),{status:s}=(0,l.useSession)(),[n,m]=(0,r.useState)(!1),[h,b]=(0,r.useState)(""),[y,g]=(0,r.useState)(null),[f,w]=(0,r.useState)(!0),v=e.toolId,j=async(e,s)=>{m(!0),b("");try{if("paid"===e&&y?.launchOption==="free"){let e=await fetch(`/api/tools/${v}/launch-date`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({launchOption:"paid",selectedDate:s})}),a=await e.json();a.success?t.push(a.data.paymentUrl):b(a.message||"创建订单失败")}else{let a=await fetch(`/api/tools/${v}/launch-date`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedDate:s})}),r=await a.json();r.success?t.push(`/submit/success?toolId=${v}&paid=${"paid"===e}`):b(r.message||"修改失败")}}catch{b("网络错误，请重试")}finally{m(!1)}};return f?(0,a.jsx)(d(),{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"加载中..."})]})})}):h?(0,a.jsx)(d(),{children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(p.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"出错了"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:h}),(0,a.jsx)("button",{onClick:()=>t.back(),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"返回上一页"})]})})}):(0,a.jsx)(d(),{children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("button",{onClick:()=>t.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-1"}),"返回"]}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"修改发布日期"}),(0,a.jsx)("p",{className:"text-gray-600",children:"您可以随时修改工具的发布日期，直到工具正式发布"})]}),y&&(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 mb-8",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:y.name}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:y.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-green-500 mr-2"}),"当前方案：","paid"===y.launchOption?"优先发布服务":"免费发布服务"]}),y.selectedLaunchDate&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["当前发布日期：",(0,a.jsx)("span",{className:"font-medium",children:new Date(y.selectedLaunchDate).toLocaleDateString("zh-CN")})]})]})]}),y&&(0,a.jsx)(c.A,{toolId:v,currentOption:y.launchOption,currentDate:y.selectedLaunchDate?new Date(y.selectedLaunchDate).toISOString().split("T")[0]:void 0,isEditing:!0,onSubmit:j,isSubmitting:n,error:h})]})})}},61931:(e,t,s)=>{Promise.resolve().then(s.bind(s,35786))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},98402:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4243,4999,9658,6435,6699,8232,2585,2498],()=>s(6130));module.exports=a})();