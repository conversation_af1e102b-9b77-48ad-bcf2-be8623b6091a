(()=>{var e={};e.id=3217,e.ids=[3217],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8359:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19761:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var r=t(60687),a=t(43210),l=t(12340),i=t(82136),d=t(33823),o=t(48577),n=t(94865),c=t(26860),m=t(28559),x=t(16023),u=t(83938),p=t(15421);function g({params:e}){let{data:s,status:t}=(0,i.useSession)(),g=(0,l.rd)(),b=(0,c.OD)(),[h,j]=(0,a.useState)(""),[f,y]=(0,a.useState)(null),[v,N]=(0,a.useState)(!0),[w,k]=(0,a.useState)(!1),[P,C]=(0,a.useState)(!1),[_,A]=(0,a.useState)({}),[S,G]=(0,a.useState)("idle"),[I,q]=(0,a.useState)(""),[T,$]=(0,a.useState)(""),[D,O]=(0,a.useState)(!1),[U,z]=(0,a.useState)({name:"",tagline:"",description:"",website:"",logo:"",category:"",tags:[],pricing:""}),E=n.Y$,F=()=>{let e={};return U.name.trim()||(e.name="工具名称是必填项"),U.description.trim()||(e.description="工具描述是必填项"),U.website.trim()||(e.website="官方网站是必填项"),U.category||(e.category="请选择一个分类"),U.pricing||(e.pricing="请选择价格模式"),U.website&&!U.website.match(/^https?:\/\/.+/)&&(e.website="请输入有效的网站地址（以 http:// 或 https:// 开头）"),A(e),0===Object.keys(e).length},M=async e=>{if(e.preventDefault(),!s)return void C(!0);if(F()){k(!0),G("idle");try{let e=await fetch(`/api/tools/${h}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:U.name,tagline:U.tagline,description:U.description,website:U.website,logo:T||void 0,category:U.category,tags:U.tags,pricing:U.pricing})}),s=await e.json();s.success?(G("success"),q("工具信息更新成功！"),setTimeout(()=>{g.push("/profile/submitted")},2e3)):(G("error"),q(s.error||"更新失败，请重试"))}catch(e){console.error("更新工具失败:",e),G("error"),q("网络错误，请重试")}finally{k(!1)}}},L=async e=>{if(!e)return;O(!0);let s=new FormData;s.append("logo",e);try{let e=await fetch("/api/upload/logo",{method:"POST",body:s}),t=await e.json();t.success?($(t.data.url),z(e=>({...e,logo:t.data.url}))):(G("error"),q(t.message||"上传失败"))}catch(e){console.error("上传失败:",e),G("error"),q("上传失败，请重试")}finally{O(!1)}};return"loading"===t||v?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(d.A,{size:"lg"})}):s?f?(0,r.jsxs)(a.Fragment,{children:[(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)(l.N_,{href:"/profile/submitted",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"返回工具列表"]}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"编辑工具信息"}),(0,r.jsx)("p",{className:"text-gray-600 mt-2",children:"更新您的工具信息，让更多用户了解您的产品"})]}),"success"===S&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:(0,r.jsx)("p",{className:"text-green-800",children:I})}),"error"===S&&(0,r.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsx)("p",{className:"text-red-800",children:I})}),f&&(0,r.jsxs)("div",{className:"mb-6 p-4 rounded-lg border",children:["draft"===f.status&&(0,r.jsx)("div",{className:"bg-gray-50 border-gray-200",children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:[(0,r.jsx)("span",{className:"font-medium",children:"草稿状态："}),"可以编辑所有信息"]})}),"pending"===f.status&&(0,r.jsx)("div",{className:"bg-yellow-50 border-yellow-200",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-700",children:[(0,r.jsx)("span",{className:"font-medium",children:"审核中："}),"可以编辑所有信息，但建议谨慎修改"]})}),"approved"===f.status&&(0,r.jsx)("div",{className:"bg-blue-50 border-blue-200",children:(0,r.jsxs)("p",{className:"text-sm text-blue-700",children:[(0,r.jsx)("span",{className:"font-medium",children:"已通过审核："}),"可以编辑基础信息，但不能修改网站地址、分类、价格模式和标签"]})}),"approved"===f.status&&f.launchDate&&new Date(f.launchDate)<=new Date&&(0,r.jsx)("div",{className:"bg-green-50 border-green-200",children:(0,r.jsxs)("p",{className:"text-sm text-green-700",children:[(0,r.jsx)("span",{className:"font-medium",children:"已发布："}),"只能编辑名称、简介、描述等基础信息"]})}),"rejected"===f.status&&(0,r.jsx)("div",{className:"bg-red-50 border-red-200",children:(0,r.jsxs)("p",{className:"text-sm text-red-700",children:[(0,r.jsx)("span",{className:"font-medium",children:"审核被拒："}),"可以编辑所有信息后重新提交"]})})]}),(0,r.jsxs)("form",{onSubmit:M,className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"基本信息"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具名称 *"}),(0,r.jsx)("input",{type:"text",value:U.name,onChange:e=>z(s=>({...s,name:e.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_.name?"border-red-300":"border-gray-300"}`,placeholder:"例如：ChatGPT"}),_.name&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:_.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具标语"}),(0,r.jsx)("input",{type:"text",value:U.tagline,onChange:e=>z(s=>({...s,tagline:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"例如：AI驱动的对话助手"})]})]}),(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具描述 *"}),(0,r.jsx)("textarea",{value:U.description,onChange:e=>z(s=>({...s,description:e.target.value})),rows:3,className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_.description?"border-red-300":"border-gray-300"}`,placeholder:"简要描述您的工具功能和特点..."}),_.description&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:_.description})]}),["draft","pending","rejected"].includes(f?.status||"")&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"官方网站 *"}),(0,r.jsx)("input",{type:"url",value:U.website,onChange:e=>z(s=>({...s,website:e.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_.website?"border-red-300":"border-gray-300"}`,placeholder:"https://example.com"}),_.website&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:_.website})]}),["approved","published"].includes(f?.status||"")&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"官方网站"}),(0,r.jsx)("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:U.website}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:f?.status==="approved"?"审核通过后不可修改网站地址":"已发布工具不可修改网站地址"})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"工具Logo"}),(0,r.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"上传Logo图片"}),(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];s&&L(s)},className:"hidden",id:"logo-upload"}),(0,r.jsxs)("label",{htmlFor:"logo-upload",className:"cursor-pointer",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:D?"上传中...":"点击上传或拖拽图片到此处"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"支持 PNG, JPG, GIF 格式，建议尺寸 200x200px"})]})]})]}),T&&(0,r.jsx)("div",{className:"w-24 h-24 border border-gray-300 rounded-lg overflow-hidden",children:(0,r.jsx)("img",{src:T,alt:"Logo预览",className:"w-full h-full object-cover"})})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"分类和定价"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[["draft","pending","rejected"].includes(f?.status||"")?(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具分类 *"}),(0,r.jsxs)("select",{value:U.category,onChange:e=>z(s=>({...s,category:e.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_.category?"border-red-300":"border-gray-300"}`,children:[(0,r.jsx)("option",{value:"",children:"请选择分类"}),b.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),_.category&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:_.category})]}):(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"工具分类"}),(0,r.jsx)("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:b.find(e=>e.value===U.category)?.label||U.category}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"分类不可修改"})]}),["draft","pending","rejected"].includes(f?.status||"")?(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格模式 *"}),(0,r.jsxs)("select",{value:U.pricing,onChange:e=>z(s=>({...s,pricing:e.target.value})),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${_.pricing?"border-red-300":"border-gray-300"}`,children:[(0,r.jsx)("option",{value:"",children:"请选择价格模式"}),E.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),_.pricing&&(0,r.jsx)("p",{className:"text-red-600 text-sm mt-1",children:_.pricing})]}):(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"价格模式"}),(0,r.jsx)("div",{className:"w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:E.find(e=>e.value===U.pricing)?.label||U.pricing}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"价格模式不可修改"})]})]})]}),["draft","pending","rejected"].includes(f?.status||"")?(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(p.A,{selectedTags:U.tags,onTagsChange:e=>z(s=>({...s,tags:e})),maxTags:u.z})}):(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"标签"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:"标签不可修改"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:U.tags.map((e,s)=>(0,r.jsx)("span",{className:"inline-flex items-center px-3 py-1 bg-gray-100 text-gray-600 text-sm rounded-full",children:e},s))})]}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{type:"submit",disabled:w,className:`px-8 py-3 rounded-lg font-medium transition-colors ${w?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:w?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{size:"sm",className:"mr-2"}),"更新中..."]}):"更新工具信息"})})]})]}),(0,r.jsx)(o.A,{isOpen:P,onClose:()=>C(!1)})]}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"工具不存在"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"您要编辑的工具不存在或已被删除"}),(0,r.jsx)(l.N_,{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"返回工具列表"})]})}):(0,r.jsxs)(a.Fragment,{children:[(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"请先登录"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"您需要登录后才能编辑工具信息"}),(0,r.jsx)("button",{onClick:()=>C(!0),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"登录"})]})}),(0,r.jsx)(o.A,{isOpen:P,onClose:()=>C(!1)})]})}},24054:(e,s,t)=>{Promise.resolve().then(t.bind(t,8359))},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},47614:(e,s,t)=>{Promise.resolve().then(t.bind(t,19761))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68458:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>n});var r=t(65239),a=t(48088),l=t(88170),i=t.n(l),d=t(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(s,o);let n={children:["",{children:["[locale]",{children:["submit",{children:["edit",{children:["[toolId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8359)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/submit/edit/[toolId]/page",pathname:"/[locale]/submit/edit/[toolId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},79551:e=>{"use strict";e.exports=require("url")}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,4999,9658,6435,6699,8232,2585,3572],()=>t(68458));module.exports=r})();