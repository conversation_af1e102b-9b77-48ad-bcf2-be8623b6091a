(()=>{var e={};e.id=245,e.ids=[245],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var i=r(36344),a=r(65752),o=r(13581),s=r(75745),n=r(17063);let l={...!1,providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,o.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,s.A)();let t=await n.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,a]=r.split(":");if(i!==e.token||a!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;await (0,s.A)();try{let i=await n.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new n.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(56037),a=r.n(i);let o=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),s=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[o],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});s.index({email:1}),s.index({role:1}),s.index({emailVerificationToken:1}),s.index({"accounts.provider":1,"accounts.providerAccountId":1}),s.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},s.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let n=a().models.User||a().model("User",s)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23466:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var i=r(60687),a=r(43210),o=r(12340),s=r(32498);function n({toolId:e,locale:t}){let r=(0,o.rd)(),[n,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(""),u=async(i,a)=>{l(!0),d("");try{let o=await fetch(`/api/tools/${e}/launch-date`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({launchOption:i,selectedDate:a})}),s=await o.json();s.success?"paid"===i&&s.data.paymentUrl?window.location.href=s.data.paymentUrl:r.push(`/${t}/submit/success?toolId=${e}`):d(s.message||"提交失败")}catch{d("网络错误，请重试")}finally{l(!1)}};return(0,i.jsx)(s.A,{toolId:e,onSubmit:u,isSubmitting:n,error:c})}},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var i=r(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),s=e=>{let t=o(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:o="",children:s,iconNode:d,...u},p)=>(0,i.createElement)("svg",{ref:p,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:n("lucide",o),...!s&&!l(u)&&{"aria-hidden":"true"},...u},[...d.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(s)?s:[s]])),u=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...o},l)=>(0,i.createElement)(d,{ref:l,iconNode:t,className:n(`lucide-${a(s(e))}`,`lucide-${e}`,r),...o}));return r.displayName=s(e),r}},28354:e=>{"use strict";e.exports=require("util")},28448:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var i=r(65239),a=r(48088),o=r(88170),s=r.n(o),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["submit",{children:["launch-date",{children:["[toolId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60808)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/submit/launch-date/[toolId]/page",pathname:"/[locale]/submit/launch-date/[toolId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(56037),a=r.n(i),o=r(60366);let s=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,maxlength:[100,"Tool name cannot exceed 100 characters"]},tagline:{type:String,trim:!0,maxlength:[200,"Tagline cannot exceed 200 characters"]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},longDescription:{type:String,trim:!0,maxlength:[2e3,"Long description cannot exceed 2000 characters"]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:o.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[500,"Pricing details cannot exceed 500 characters"]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});s.index({status:1,isActive:1}),s.index({category:1,status:1}),s.index({tags:1,status:1}),s.index({submittedBy:1}),s.index({launchDate:-1}),s.index({views:-1}),s.index({likes:-1}),s.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let n=a().models.Tool||a().model("Tool",s)},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},57592:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx","default")},60366:(e,t,r)=>{"use strict";r.d(t,{PZ:()=>s,RI:()=>l,ut:()=>n});var i=r(64348);let a=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];async function o(e){let t=await (0,i.A)({locale:e,namespace:"categories"});return a.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function s(e,t){return(await o(t)).find(t=>t.slug===e)}let n=a.map(e=>e.slug),l=a.reduce((e,t)=>(e[t.slug]=t,e),{})},60808:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var i=r(37413),a=r(35426),o=r(78878),s=r(12909),n=r(75745),l=r(30762),c=r(17063);let d=(0,r(26373).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);var u=r(91142),p=r(64348),m=r(57592);async function g(e,t){try{await (0,n.A)();let r=await c.A.findOne({email:t});if(!r)return{tool:null,error:"unauthorized_access"};let i=await l.A.findById(e).lean();if(!i)return{tool:null,error:"tool_not_found"};if(i.submittedBy!==r._id.toString())return{tool:null,error:"unauthorized_access"};if("draft"!==i.status)return{tool:null,error:"launch_date_already_set"};return{tool:{_id:i._id.toString(),name:i.name,description:i.description,status:i.status},error:null}}catch(e){return console.error("Failed to fetch tool:",e),{tool:null,error:"fetch_failed"}}}async function h({params:e}){let{locale:t,toolId:r}=await e,n=await (0,p.A)("submit"),l=await (0,a.getServerSession)(s.N);l?.user?.email||(0,o.V2)("/");let{tool:c,error:h}=await g(r,l.user.email);return h?(0,i.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(d,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,i.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"出错了"}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:n(`errors.${h}`)}),(0,i.jsx)("a",{href:`/${t}/profile/submitted`,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700",children:n("actions.back_to_submitted")})]})}):(0,i.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsxs)("div",{className:"text-center mb-8",children:[(0,i.jsx)(u.A,{className:"h-12 w-12 text-green-500 mx-auto mb-4"}),(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"工具信息提交成功！"}),(0,i.jsx)("p",{className:"text-lg text-gray-600",children:"现在请选择您的发布日期和选项"})]}),c&&(0,i.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 mb-8",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:c.name}),(0,i.jsx)("p",{className:"text-gray-600",children:c.description})]}),c&&(0,i.jsx)(m.default,{toolId:r,locale:t})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65414:(e,t,r)=>{Promise.resolve().then(r.bind(r,39130)),Promise.resolve().then(r.bind(r,45196)),Promise.resolve().then(r.bind(r,23466))},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(56037),a=r.n(i);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let n=async function(){if(s.conn)return s.conn;s.promise||(s.promise=a().connect(o,{bufferCommands:!1}).then(e=>e));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91142:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(26373).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")},96958:(e,t,r)=>{Promise.resolve().then(r.bind(r,75788)),Promise.resolve().then(r.bind(r,80994)),Promise.resolve().then(r.bind(r,57592))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,4999,9658,6435,6699,8232,3136,2585,2498],()=>r(28448));module.exports=i})();