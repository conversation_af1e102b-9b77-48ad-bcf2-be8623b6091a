{"version": 3, "sources": [], "sections": [{"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/config.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { notFound } from 'next/navigation';\n\n// 支持的语言列表\nexport const locales = ['en', 'zh'] as const;\nexport type Locale = (typeof locales)[number];\n\n// 默认语言\nexport const defaultLocale: Locale = 'en';\n\n// 语言显示名称\nexport const localeNames: Record<Locale, string> = {\n  zh: '中文',\n  en: 'English',\n};\n\n// 验证语言是否有效\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n\n// Next-intl 配置\nexport default getRequestConfig(async ({ locale }) => {\n  // 验证传入的语言是否有效\n  if (!isValidLocale(locale)) {\n    notFound();\n  }\n\n  return {\n    messages: (await import(`./messages/${locale}.json`)).default,\n  };\n});\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAAA;;;AAGO,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,gBAAwB;AAG9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B;uCAGe,CAAA,GAAA,kQAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,cAAc;IACd,IAAI,CAAC,cAAc,SAAS;QAC1B,CAAA,GAAA,mLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,OAAO;QACL,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF"}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\nimport { createNavigation } from 'next-intl/navigation';\nimport { getRequestConfig } from 'next-intl/server';\nimport { locales, defaultLocale, type Locale } from './config';\n\nexport const routing = defineRouting({\n  // A list of all locales that are supported\n  locales,\n\n  // Used when no locale matches\n  defaultLocale,\n\n  // Always use locale prefix for all routes\n  localePrefix: 'always',\n\n  // Enable locale detection based on accept-language header\n  localeDetection: true\n});\n\n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nexport const { Link, redirect, usePathname, useRouter } =\n  createNavigation(routing);\n\n// Request configuration for next-intl\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as Locale)) {\n    locale = defaultLocale;\n  }\n\n  return {\n    locale,\n    messages: (await import(`./messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,UAAU,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAA,6HAAA,CAAA,UAAO;IAEP,8BAA8B;IAC9B,eAAA,6HAAA,CAAA,gBAAa;IAEb,0CAA0C;IAC1C,cAAc;IAEd,0DAA0D;IAC1D,iBAAiB;AACnB;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GACrD,CAAA,GAAA,sQAAA,CAAA,mBAAgB,AAAD,EAAE;uCAGJ,CAAA,GAAA,kQAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,6HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAmB;QACvC,SAAS,6HAAA,CAAA,gBAAa;IACxB;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF"}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\nimport { routing } from './i18n/routing';\n\nexport default createMiddleware(routing);\n\nexport const config = {\n  // Match all pathnames except for\n  // - API routes (/api)\n  // - Static files (_next/static)\n  // - Image files (_next/image)\n  // - favicon.ico\n  // - robots.txt\n  // - sitemap.xml\n  // - uploads directory\n  matcher: [\n    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)'\n  ]\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCAEe,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE,8HAAA,CAAA,UAAO;AAEhC,MAAM,SAAS;IACpB,iCAAiC;IACjC,sBAAsB;IACtB,gCAAgC;IAChC,8BAA8B;IAC9B,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,sBAAsB;IACtB,SAAS;QACP;KACD;AACH"}}]}