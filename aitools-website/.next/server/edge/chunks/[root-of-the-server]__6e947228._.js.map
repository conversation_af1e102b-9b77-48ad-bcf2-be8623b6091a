{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/routing.ts"], "sourcesContent": ["import { defineRouting } from 'next-intl/routing';\nimport { createNavigation } from 'next-intl/navigation';\n\nexport const routing = defineRouting({\n  // A list of all locales that are supported\n  locales: ['en', 'zh'],\n\n  // Used when no locale matches\n  defaultLocale: 'en',\n\n  // Always use locale prefix for all routes\n  localePrefix: 'always'\n});\n\n// Lightweight wrappers around Next.js' navigation APIs\n// that will consider the routing configuration\nexport const { Link, redirect, usePathname, useRouter } =\n  createNavigation(routing);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAEO,MAAM,UAAU,CAAA,GAAA,0OAAA,CAAA,gBAAa,AAAD,EAAE;IACnC,2CAA2C;IAC3C,SAAS;QAAC;QAAM;KAAK;IAErB,8BAA8B;IAC9B,eAAe;IAEf,0CAA0C;IAC1C,cAAc;AAChB;AAIO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,GACrD,CAAA,GAAA,sQAAA,CAAA,mBAAgB,AAAD,EAAE"}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\nimport { routing } from './i18n/routing';\n\nexport default createMiddleware(routing);\n\nexport const config = {\n  // Match all pathnames except for\n  // - API routes (/api)\n  // - Static files (_next/static)\n  // - Image files (_next/image)\n  // - favicon.ico\n  // - robots.txt\n  // - sitemap.xml\n  // - uploads directory\n  matcher: [\n    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)'\n  ]\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCAEe,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE,8HAAA,CAAA,UAAO;AAEhC,MAAM,SAAS;IACpB,iCAAiC;IACjC,sBAAsB;IACtB,gCAAgC;IAChC,8BAA8B;IAC9B,gBAAgB;IAChB,eAAe;IACf,gBAAgB;IAChB,sBAAsB;IACtB,SAAS;QACP;KACD;AACH"}}]}