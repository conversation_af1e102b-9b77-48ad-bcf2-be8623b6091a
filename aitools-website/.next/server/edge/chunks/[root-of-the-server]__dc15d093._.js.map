{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport default function middleware(request: NextRequest) {\n  console.log('🚀🚀🚀 MIDDLEWARE EXECUTING:', request.nextUrl.pathname);\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: '/(.*)'\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEe,SAAS,WAAW,OAAoB;IACrD,QAAQ,GAAG,CAAC,gCAAgC,QAAQ,OAAO,CAAC,QAAQ;IACpE,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;AACX"}}]}