{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport function middleware(request: NextRequest) {\n  console.log('🔥🔥🔥 MIDDLEWARE CALLED:', request.nextUrl.pathname);\n\n  return NextResponse.redirect(new URL('/en', request.url));\n}\n\nexport const config = {\n  matcher: [\n    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)'\n  ]\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,SAAS,WAAW,OAAoB;IAC7C,QAAQ,GAAG,CAAC,6BAA6B,QAAQ,OAAO,CAAC,QAAQ;IAEjE,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,OAAO,QAAQ,GAAG;AACzD;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}