{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport default function middleware(request: NextRequest) {\n  console.log('🔥 MIDDLEWARE CALLED:', request.nextUrl.pathname);\n\n  const pathname = request.nextUrl.pathname;\n\n  // Check if the pathname already has a locale prefix\n  const hasLocalePrefix = pathname.startsWith('/en/') || pathname.startsWith('/zh/') || pathname === '/en' || pathname === '/zh';\n\n  if (hasLocalePrefix) {\n    console.log('✅ Has locale prefix, continuing');\n    return NextResponse.next();\n  }\n\n  // For root path, detect locale based on accept-language header\n  if (pathname === '/') {\n    const acceptLanguage = request.headers.get('accept-language') || '';\n    const isChinesePreferred = acceptLanguage.includes('zh');\n    const locale = isChinesePreferred ? 'zh' : 'en';\n\n    console.log('🏠 Root path, redirecting to:', `/${locale}`);\n    const url = new URL(`/${locale}`, request.url);\n    url.search = request.nextUrl.search;\n    return NextResponse.redirect(url);\n  }\n\n  // For all other paths, redirect to /en prefix\n  console.log('🔀 Other path, redirecting to:', `/en${pathname}`);\n  const url = new URL(`/en${pathname}`, request.url);\n  url.search = request.nextUrl.search;\n  return NextResponse.redirect(url);\n}\n\nexport const config = {\n  matcher: [\n    '/((?!api|_next/static|_next/image|favicon.ico).*)'\n  ]\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEe,SAAS,WAAW,OAAoB;IACrD,QAAQ,GAAG,CAAC,yBAAyB,QAAQ,OAAO,CAAC,QAAQ;IAE7D,MAAM,WAAW,QAAQ,OAAO,CAAC,QAAQ;IAEzC,oDAAoD;IACpD,MAAM,kBAAkB,SAAS,UAAU,CAAC,WAAW,SAAS,UAAU,CAAC,WAAW,aAAa,SAAS,aAAa;IAEzH,IAAI,iBAAiB;QACnB,QAAQ,GAAG,CAAC;QACZ,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,+DAA+D;IAC/D,IAAI,aAAa,KAAK;QACpB,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;QACjE,MAAM,qBAAqB,eAAe,QAAQ,CAAC;QACnD,MAAM,SAAS,qBAAqB,OAAO;QAE3C,QAAQ,GAAG,CAAC,iCAAiC,CAAC,CAAC,EAAE,QAAQ;QACzD,MAAM,MAAM,IAAI,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,QAAQ,GAAG;QAC7C,IAAI,MAAM,GAAG,QAAQ,OAAO,CAAC,MAAM;QACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;IAC/B;IAEA,8CAA8C;IAC9C,QAAQ,GAAG,CAAC,kCAAkC,CAAC,GAAG,EAAE,UAAU;IAC9D,MAAM,MAAM,IAAI,IAAI,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,GAAG;IACjD,IAAI,MAAM,GAAG,QAAQ,OAAO,CAAC,MAAM;IACnC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;AAC/B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}