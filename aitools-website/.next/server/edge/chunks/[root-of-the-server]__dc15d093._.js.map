{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import createMiddleware from 'next-intl/middleware';\n\nexport default createMiddleware({\n  // A list of all locales that are supported\n  locales: ['en', 'zh'],\n\n  // Used when no locale matches\n  defaultLocale: 'en',\n\n  // Only add locale prefix when necessary\n  localePrefix: 'as-needed'\n});\n\nexport const config = {\n  matcher: [\n    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)'\n  ]\n};\n"], "names": [], "mappings": ";;;;AAAA;;uCAEe,CAAA,GAAA,8LAAA,CAAA,UAAgB,AAAD,EAAE;IAC9B,2CAA2C;IAC3C,SAAS;QAAC;QAAM;KAAK;IAErB,8BAA8B;IAC9B,eAAe;IAEf,wCAAwC;IACxC,cAAc;AAChB;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;KACD;AACH"}}]}