{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/routing/config.js"], "sourcesContent": ["function receiveRoutingConfig(input) {\n  return {\n    ...input,\n    localePrefix: receiveLocalePrefixConfig(input.localePrefix),\n    localeCookie: receiveLocaleCookie(input.localeCookie),\n    localeDetection: input.localeDetection ?? true,\n    alternateLinks: input.alternateLinks ?? true\n  };\n}\nfunction receiveLocaleCookie(localeCookie) {\n  return localeCookie ?? true ? {\n    name: 'NEXT_LOCALE',\n    sameSite: 'lax',\n    ...(typeof localeCookie === 'object' && localeCookie)\n\n    // `path` needs to be provided based on a detected base path\n    // that depends on the environment when setting a cookie\n  } : false;\n}\nfunction receiveLocalePrefixConfig(localePrefix) {\n  return typeof localePrefix === 'object' ? localePrefix : {\n    mode: localePrefix || 'always'\n  };\n}\n\nexport { receiveRoutingConfig };\n"], "names": [], "mappings": ";;;AAAA,SAAS,qBAAqB,KAAK;IACjC,OAAO;QACL,GAAG,KAAK;QACR,cAAc,0BAA0B,MAAM,YAAY;QAC1D,cAAc,oBAAoB,MAAM,YAAY;QACpD,iBAAiB,MAAM,eAAe,IAAI;QAC1C,gBAAgB,MAAM,cAAc,IAAI;IAC1C;AACF;AACA,SAAS,oBAAoB,YAAY;IACvC,OAAO,gBAAgB,OAAO;QAC5B,MAAM;QACN,UAAU;QACV,GAAI,OAAO,iBAAiB,YAAY,YAAY;IAItD,IAAI;AACN;AACA,SAAS,0BAA0B,YAAY;IAC7C,OAAO,OAAO,iBAAiB,WAAW,eAAe;QACvD,MAAM,gBAAgB;IACxB;AACF", "ignoreList": [0]}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/shared/constants.js"], "sourcesContent": ["// Used to read the locale from the middleware\nconst HEADER_LOCALE_NAME = 'X-NEXT-INTL-LOCALE';\n\nexport { HEADER_LOCALE_NAME };\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;AAC9C,MAAM,qBAAqB", "ignoreList": [0]}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/shared/utils.js"], "sourcesContent": ["function isRelativeHref(href) {\n  const pathname = typeof href === 'object' ? href.pathname : href;\n  return pathname != null && !pathname.startsWith('/');\n}\nfunction isLocalHref(href) {\n  if (typeof href === 'object') {\n    return href.host == null && href.hostname == null;\n  } else {\n    const hasProtocol = /^[a-z]+:/i.test(href);\n    return !hasProtocol;\n  }\n}\nfunction isLocalizableHref(href) {\n  return isLocalHref(href) && !isRelativeHref(href);\n}\nfunction unprefixPathname(pathname, prefix) {\n  return pathname.replace(new RegExp(`^${prefix}`), '') || '/';\n}\nfunction prefixPathname(prefix, pathname) {\n  let localizedHref = prefix;\n\n  // Avoid trailing slashes\n  if (/^\\/(\\?.*)?$/.test(pathname)) {\n    pathname = pathname.slice(1);\n  }\n  localizedHref += pathname;\n  return localizedHref;\n}\nfunction hasPathnamePrefixed(prefix, pathname) {\n  return pathname === prefix || pathname.startsWith(`${prefix}/`);\n}\nfunction hasTrailingSlash() {\n  try {\n    // Provided via `env` setting in `next.config.js` via the plugin\n    return process.env._next_intl_trailing_slash === 'true';\n  } catch {\n    return false;\n  }\n}\nfunction getLocalizedTemplate(pathnameConfig, locale, internalTemplate) {\n  return typeof pathnameConfig === 'string' ? pathnameConfig : pathnameConfig[locale] || internalTemplate;\n}\nfunction normalizeTrailingSlash(pathname) {\n  const trailingSlash = hasTrailingSlash();\n  const [path, ...hashParts] = pathname.split('#');\n  const hash = hashParts.join('#');\n  let normalizedPath = path;\n  if (normalizedPath !== '/') {\n    const pathnameEndsWithSlash = normalizedPath.endsWith('/');\n    if (trailingSlash && !pathnameEndsWithSlash) {\n      normalizedPath += '/';\n    } else if (!trailingSlash && pathnameEndsWithSlash) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n  }\n  if (hash) {\n    normalizedPath += '#' + hash;\n  }\n  return normalizedPath;\n}\nfunction matchesPathname(/** E.g. `/users/[userId]-[userName]` */\ntemplate, /** E.g. `/users/23-jane` */\npathname) {\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const regex = templateToRegex(normalizedTemplate);\n  return regex.test(normalizedPathname);\n}\nfunction getLocalePrefix(locale, localePrefix) {\n  return localePrefix.mode !== 'never' && localePrefix.prefixes?.[locale] ||\n  // We return a prefix even if `mode: 'never'`. It's up to the consumer\n  // to decide to use it or not.\n  getLocaleAsPrefix(locale);\n}\nfunction getLocaleAsPrefix(locale) {\n  return '/' + locale;\n}\nfunction templateToRegex(template) {\n  const regexPattern = template\n  // Replace optional catchall ('[[...slug]]')\n  .replace(/\\[\\[(\\.\\.\\.[^\\]]+)\\]\\]/g, '?(.*)')\n  // Replace catchall ('[...slug]')\n  .replace(/\\[(\\.\\.\\.[^\\]]+)\\]/g, '(.+)')\n  // Replace regular parameter ('[slug]')\n  .replace(/\\[([^\\]]+)\\]/g, '([^/]+)');\n  return new RegExp(`^${regexPattern}$`);\n}\nfunction isOptionalCatchAllSegment(pathname) {\n  return pathname.includes('[[...');\n}\nfunction isCatchAllSegment(pathname) {\n  return pathname.includes('[...');\n}\nfunction isDynamicSegment(pathname) {\n  return pathname.includes('[');\n}\nfunction comparePathnamePairs(a, b) {\n  const pathA = a.split('/');\n  const pathB = b.split('/');\n  const maxLength = Math.max(pathA.length, pathB.length);\n  for (let i = 0; i < maxLength; i++) {\n    const segmentA = pathA[i];\n    const segmentB = pathB[i];\n\n    // If one of the paths ends, prioritize the shorter path\n    if (!segmentA && segmentB) return -1;\n    if (segmentA && !segmentB) return 1;\n    if (!segmentA && !segmentB) continue;\n\n    // Prioritize static segments over dynamic segments\n    if (!isDynamicSegment(segmentA) && isDynamicSegment(segmentB)) return -1;\n    if (isDynamicSegment(segmentA) && !isDynamicSegment(segmentB)) return 1;\n\n    // Prioritize non-catch-all segments over catch-all segments\n    if (!isCatchAllSegment(segmentA) && isCatchAllSegment(segmentB)) return -1;\n    if (isCatchAllSegment(segmentA) && !isCatchAllSegment(segmentB)) return 1;\n\n    // Prioritize non-optional catch-all segments over optional catch-all segments\n    if (!isOptionalCatchAllSegment(segmentA) && isOptionalCatchAllSegment(segmentB)) {\n      return -1;\n    }\n    if (isOptionalCatchAllSegment(segmentA) && !isOptionalCatchAllSegment(segmentB)) {\n      return 1;\n    }\n    if (segmentA === segmentB) continue;\n  }\n\n  // Both pathnames are completely static\n  return 0;\n}\nfunction getSortedPathnames(pathnames) {\n  return pathnames.sort(comparePathnamePairs);\n}\nfunction isPromise(value) {\n  // https://github.com/amannn/next-intl/issues/1711\n  return typeof value.then === 'function';\n}\n\nexport { getLocaleAsPrefix, getLocalePrefix, getLocalizedTemplate, getSortedPathnames, hasPathnamePrefixed, isLocalizableHref, isPromise, matchesPathname, normalizeTrailingSlash, prefixPathname, templateToRegex, unprefixPathname };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,SAAS,eAAe,IAAI;IAC1B,MAAM,WAAW,OAAO,SAAS,WAAW,KAAK,QAAQ,GAAG;IAC5D,OAAO,YAAY,QAAQ,CAAC,SAAS,UAAU,CAAC;AAClD;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,OAAO,SAAS,UAAU;QAC5B,OAAO,KAAK,IAAI,IAAI,QAAQ,KAAK,QAAQ,IAAI;IAC/C,OAAO;QACL,MAAM,cAAc,YAAY,IAAI,CAAC;QACrC,OAAO,CAAC;IACV;AACF;AACA,SAAS,kBAAkB,IAAI;IAC7B,OAAO,YAAY,SAAS,CAAC,eAAe;AAC9C;AACA,SAAS,iBAAiB,QAAQ,EAAE,MAAM;IACxC,OAAO,SAAS,OAAO,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,QAAQ,GAAG,OAAO;AAC3D;AACA,SAAS,eAAe,MAAM,EAAE,QAAQ;IACtC,IAAI,gBAAgB;IAEpB,yBAAyB;IACzB,IAAI,cAAc,IAAI,CAAC,WAAW;QAChC,WAAW,SAAS,KAAK,CAAC;IAC5B;IACA,iBAAiB;IACjB,OAAO;AACT;AACA,SAAS,oBAAoB,MAAM,EAAE,QAAQ;IAC3C,OAAO,aAAa,UAAU,SAAS,UAAU,CAAC,GAAG,OAAO,CAAC,CAAC;AAChE;AACA,SAAS;IACP,IAAI;QACF,gEAAgE;QAChE,OAAO,QAAQ,GAAG,CAAC,yBAAyB,KAAK;IACnD,EAAE,OAAM;QACN,OAAO;IACT;AACF;AACA,SAAS,qBAAqB,cAAc,EAAE,MAAM,EAAE,gBAAgB;IACpE,OAAO,OAAO,mBAAmB,WAAW,iBAAiB,cAAc,CAAC,OAAO,IAAI;AACzF;AACA,SAAS,uBAAuB,QAAQ;IACtC,MAAM,gBAAgB;IACtB,MAAM,CAAC,MAAM,GAAG,UAAU,GAAG,SAAS,KAAK,CAAC;IAC5C,MAAM,OAAO,UAAU,IAAI,CAAC;IAC5B,IAAI,iBAAiB;IACrB,IAAI,mBAAmB,KAAK;QAC1B,MAAM,wBAAwB,eAAe,QAAQ,CAAC;QACtD,IAAI,iBAAiB,CAAC,uBAAuB;YAC3C,kBAAkB;QACpB,OAAO,IAAI,CAAC,iBAAiB,uBAAuB;YAClD,iBAAiB,eAAe,KAAK,CAAC,GAAG,CAAC;QAC5C;IACF;IACA,IAAI,MAAM;QACR,kBAAkB,MAAM;IAC1B;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,sCAAsC,GAC/D,QAAQ,EAAE,0BAA0B,GACpC,QAAQ;IACN,MAAM,qBAAqB,uBAAuB;IAClD,MAAM,qBAAqB,uBAAuB;IAClD,MAAM,QAAQ,gBAAgB;IAC9B,OAAO,MAAM,IAAI,CAAC;AACpB;AACA,SAAS,gBAAgB,MAAM,EAAE,YAAY;IAC3C,OAAO,aAAa,IAAI,KAAK,WAAW,aAAa,QAAQ,EAAE,CAAC,OAAO,IACvE,sEAAsE;IACtE,8BAA8B;IAC9B,kBAAkB;AACpB;AACA,SAAS,kBAAkB,MAAM;IAC/B,OAAO,MAAM;AACf;AACA,SAAS,gBAAgB,QAAQ;IAC/B,MAAM,eAAe,QACrB,4CAA4C;KAC3C,OAAO,CAAC,2BAA2B,QACpC,iCAAiC;KAChC,OAAO,CAAC,uBAAuB,OAChC,uCAAuC;KACtC,OAAO,CAAC,iBAAiB;IAC1B,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;AACvC;AACA,SAAS,0BAA0B,QAAQ;IACzC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,kBAAkB,QAAQ;IACjC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,iBAAiB,QAAQ;IAChC,OAAO,SAAS,QAAQ,CAAC;AAC3B;AACA,SAAS,qBAAqB,CAAC,EAAE,CAAC;IAChC,MAAM,QAAQ,EAAE,KAAK,CAAC;IACtB,MAAM,QAAQ,EAAE,KAAK,CAAC;IACtB,MAAM,YAAY,KAAK,GAAG,CAAC,MAAM,MAAM,EAAE,MAAM,MAAM;IACrD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;QAClC,MAAM,WAAW,KAAK,CAAC,EAAE;QACzB,MAAM,WAAW,KAAK,CAAC,EAAE;QAEzB,wDAAwD;QACxD,IAAI,CAAC,YAAY,UAAU,OAAO,CAAC;QACnC,IAAI,YAAY,CAAC,UAAU,OAAO;QAClC,IAAI,CAAC,YAAY,CAAC,UAAU;QAE5B,mDAAmD;QACnD,IAAI,CAAC,iBAAiB,aAAa,iBAAiB,WAAW,OAAO,CAAC;QACvE,IAAI,iBAAiB,aAAa,CAAC,iBAAiB,WAAW,OAAO;QAEtE,4DAA4D;QAC5D,IAAI,CAAC,kBAAkB,aAAa,kBAAkB,WAAW,OAAO,CAAC;QACzE,IAAI,kBAAkB,aAAa,CAAC,kBAAkB,WAAW,OAAO;QAExE,8EAA8E;QAC9E,IAAI,CAAC,0BAA0B,aAAa,0BAA0B,WAAW;YAC/E,OAAO,CAAC;QACV;QACA,IAAI,0BAA0B,aAAa,CAAC,0BAA0B,WAAW;YAC/E,OAAO;QACT;QACA,IAAI,aAAa,UAAU;IAC7B;IAEA,uCAAuC;IACvC,OAAO;AACT;AACA,SAAS,mBAAmB,SAAS;IACnC,OAAO,UAAU,IAAI,CAAC;AACxB;AACA,SAAS,UAAU,KAAK;IACtB,kDAAkD;IAClD,OAAO,OAAO,MAAM,IAAI,KAAK;AAC/B", "ignoreList": [0]}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/middleware/utils.js"], "sourcesContent": ["import { normalizeTrailingSlash, getSortedPathnames, matchesPathname, prefixPathname, getLocalePrefix, templateToRegex, getLocalizedTemplate } from '../shared/utils.js';\n\nfunction getInternalTemplate(pathnames, pathname, locale) {\n  const sortedPathnames = getSortedPathnames(Object.keys(pathnames));\n\n  // Try to find a localized pathname that matches\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if (matchesPathname(localizedPathname, pathname)) {\n        return [undefined, internalPathname];\n      }\n    } else {\n      // Prefer the entry with the current locale in case multiple\n      // localized pathnames match the current pathname\n      const sortedEntries = Object.entries(localizedPathnamesOrPathname);\n      const curLocaleIndex = sortedEntries.findIndex(([entryLocale]) => entryLocale === locale);\n      if (curLocaleIndex > 0) {\n        sortedEntries.unshift(sortedEntries.splice(curLocaleIndex, 1)[0]);\n      }\n      for (const [entryLocale] of sortedEntries) {\n        const localizedTemplate = getLocalizedTemplate(pathnames[internalPathname], entryLocale, internalPathname);\n        if (matchesPathname(localizedTemplate, pathname)) {\n          return [entryLocale, internalPathname];\n        }\n      }\n    }\n  }\n\n  // Try to find an internal pathname that matches (this can be the case\n  // if all localized pathnames are different from the internal pathnames)\n  for (const internalPathname of Object.keys(pathnames)) {\n    if (matchesPathname(internalPathname, pathname)) {\n      return [undefined, internalPathname];\n    }\n  }\n\n  // No match\n  return [undefined, undefined];\n}\nfunction formatTemplatePathname(sourcePathname, sourceTemplate, targetTemplate, prefix) {\n  const params = getRouteParams(sourceTemplate, sourcePathname);\n  let targetPathname = '';\n  targetPathname += formatPathnameTemplate(targetTemplate, params);\n\n  // A pathname with an optional catchall like `/categories/[[...slug]]`\n  // should be normalized to `/categories` if the catchall is not present\n  // and no trailing slash is configured\n  targetPathname = normalizeTrailingSlash(targetPathname);\n  return targetPathname;\n}\n\n/**\n * Removes potential prefixes from the pathname.\n */\nfunction getNormalizedPathname(pathname, locales, localePrefix) {\n  // Add trailing slash for consistent handling\n  // both for the root as well as nested paths\n  if (!pathname.endsWith('/')) {\n    pathname += '/';\n  }\n  const localePrefixes = getLocalePrefixes(locales, localePrefix);\n  const regex = new RegExp(`^(${localePrefixes.map(([, prefix]) => prefix.replaceAll('/', '\\\\/')).join('|')})/(.*)`, 'i');\n  const match = pathname.match(regex);\n  let result = match ? '/' + match[2] : pathname;\n  if (result !== '/') {\n    result = normalizeTrailingSlash(result);\n  }\n  return result;\n}\nfunction getLocalePrefixes(locales, localePrefix, sort = true) {\n  const prefixes = locales.map(locale => [locale, getLocalePrefix(locale, localePrefix)]);\n  if (sort) {\n    // More specific ones first\n    prefixes.sort((a, b) => b[1].length - a[1].length);\n  }\n  return prefixes;\n}\nfunction getPathnameMatch(pathname, locales, localePrefix, domain) {\n  const localePrefixes = getLocalePrefixes(locales, localePrefix);\n\n  // Sort to prioritize domain locales\n  if (domain) {\n    localePrefixes.sort(([localeA], [localeB]) => {\n      if (localeA === domain.defaultLocale) return -1;\n      if (localeB === domain.defaultLocale) return 1;\n      const isLocaleAInDomain = domain.locales.includes(localeA);\n      const isLocaleBInDomain = domain.locales.includes(localeB);\n      if (isLocaleAInDomain && !isLocaleBInDomain) return -1;\n      if (!isLocaleAInDomain && isLocaleBInDomain) return 1;\n      return 0;\n    });\n  }\n  for (const [locale, prefix] of localePrefixes) {\n    let exact, matches;\n    if (pathname === prefix || pathname.startsWith(prefix + '/')) {\n      exact = matches = true;\n    } else {\n      const normalizedPathname = pathname.toLowerCase();\n      const normalizedPrefix = prefix.toLowerCase();\n      if (normalizedPathname === normalizedPrefix || normalizedPathname.startsWith(normalizedPrefix + '/')) {\n        exact = false;\n        matches = true;\n      }\n    }\n    if (matches) {\n      return {\n        locale,\n        prefix,\n        matchedPrefix: pathname.slice(0, prefix.length),\n        exact\n      };\n    }\n  }\n}\nfunction getRouteParams(template, pathname) {\n  const normalizedPathname = normalizeTrailingSlash(pathname);\n  const normalizedTemplate = normalizeTrailingSlash(template);\n  const regex = templateToRegex(normalizedTemplate);\n  const match = regex.exec(normalizedPathname);\n  if (!match) return undefined;\n  const params = {};\n  for (let i = 1; i < match.length; i++) {\n    const key = normalizedTemplate.match(/\\[([^\\]]+)\\]/g)?.[i - 1].replace(/[[\\]]/g, '');\n    if (key) params[key] = match[i];\n  }\n  return params;\n}\nfunction formatPathnameTemplate(template, params) {\n  if (!params) return template;\n\n  // Simplify syntax for optional catchall ('[[...slug]]') so\n  // we can replace the value with simple interpolation\n  template = template.replace(/\\[\\[/g, '[').replace(/\\]\\]/g, ']');\n  let result = template;\n  Object.entries(params).forEach(([key, value]) => {\n    result = result.replace(`[${key}]`, value);\n  });\n  return result;\n}\nfunction formatPathname(pathname, prefix, search) {\n  let result = pathname;\n  if (prefix) {\n    result = prefixPathname(prefix, result);\n  }\n  if (search) {\n    result += search;\n  }\n  return result;\n}\nfunction getHost(requestHeaders) {\n  return requestHeaders.get('x-forwarded-host') ?? requestHeaders.get('host') ?? undefined;\n}\nfunction isLocaleSupportedOnDomain(locale, domain) {\n  return domain.defaultLocale === locale || domain.locales.includes(locale);\n}\nfunction getBestMatchingDomain(curHostDomain, locale, domainsConfig) {\n  let domainConfig;\n\n  // Prio 1: Stay on current domain\n  if (curHostDomain && isLocaleSupportedOnDomain(locale, curHostDomain)) {\n    domainConfig = curHostDomain;\n  }\n\n  // Prio 2: Use alternative domain with matching default locale\n  if (!domainConfig) {\n    domainConfig = domainsConfig.find(cur => cur.defaultLocale === locale);\n  }\n\n  // Prio 3: Use alternative domain that supports the locale\n  if (!domainConfig) {\n    domainConfig = domainsConfig.find(cur => cur.locales.includes(locale));\n  }\n  return domainConfig;\n}\nfunction applyBasePath(pathname, basePath) {\n  return normalizeTrailingSlash(basePath + pathname);\n}\nfunction getLocaleAsPrefix(locale) {\n  return `/${locale}`;\n}\nfunction sanitizePathname(pathname) {\n  // Sanitize malicious URIs, e.g.:\n  // '/en/\\\\example.org → /en/%5C%5Cexample.org'\n  // '/en////example.org → /en/example.org'\n  return pathname.replace(/\\\\/g, '%5C').replace(/\\/+/g, '/');\n}\n\nexport { applyBasePath, formatPathname, formatPathnameTemplate, formatTemplatePathname, getBestMatchingDomain, getHost, getInternalTemplate, getLocaleAsPrefix, getLocalePrefixes, getNormalizedPathname, getPathnameMatch, getRouteParams, isLocaleSupportedOnDomain, sanitizePathname };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AAEA,SAAS,oBAAoB,SAAS,EAAE,QAAQ,EAAE,MAAM;IACtD,MAAM,kBAAkB,CAAA,GAAA,qLAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,IAAI,CAAC;IAEvD,gDAAgD;IAChD,KAAK,MAAM,oBAAoB,gBAAiB;QAC9C,MAAM,+BAA+B,SAAS,CAAC,iBAAiB;QAChE,IAAI,OAAO,iCAAiC,UAAU;YACpD,MAAM,oBAAoB;YAC1B,IAAI,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,mBAAmB,WAAW;gBAChD,OAAO;oBAAC;oBAAW;iBAAiB;YACtC;QACF,OAAO;YACL,4DAA4D;YAC5D,iDAAiD;YACjD,MAAM,gBAAgB,OAAO,OAAO,CAAC;YACrC,MAAM,iBAAiB,cAAc,SAAS,CAAC,CAAC,CAAC,YAAY,GAAK,gBAAgB;YAClF,IAAI,iBAAiB,GAAG;gBACtB,cAAc,OAAO,CAAC,cAAc,MAAM,CAAC,gBAAgB,EAAE,CAAC,EAAE;YAClE;YACA,KAAK,MAAM,CAAC,YAAY,IAAI,cAAe;gBACzC,MAAM,oBAAoB,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,CAAC,iBAAiB,EAAE,aAAa;gBACzF,IAAI,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,mBAAmB,WAAW;oBAChD,OAAO;wBAAC;wBAAa;qBAAiB;gBACxC;YACF;QACF;IACF;IAEA,sEAAsE;IACtE,wEAAwE;IACxE,KAAK,MAAM,oBAAoB,OAAO,IAAI,CAAC,WAAY;QACrD,IAAI,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,kBAAkB,WAAW;YAC/C,OAAO;gBAAC;gBAAW;aAAiB;QACtC;IACF;IAEA,WAAW;IACX,OAAO;QAAC;QAAW;KAAU;AAC/B;AACA,SAAS,uBAAuB,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,MAAM;IACpF,MAAM,SAAS,eAAe,gBAAgB;IAC9C,IAAI,iBAAiB;IACrB,kBAAkB,uBAAuB,gBAAgB;IAEzD,sEAAsE;IACtE,uEAAuE;IACvE,sCAAsC;IACtC,iBAAiB,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE;IACxC,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,sBAAsB,QAAQ,EAAE,OAAO,EAAE,YAAY;IAC5D,6CAA6C;IAC7C,4CAA4C;IAC5C,IAAI,CAAC,SAAS,QAAQ,CAAC,MAAM;QAC3B,YAAY;IACd;IACA,MAAM,iBAAiB,kBAAkB,SAAS;IAClD,MAAM,QAAQ,IAAI,OAAO,CAAC,EAAE,EAAE,eAAe,GAAG,CAAC,CAAC,GAAG,OAAO,GAAK,OAAO,UAAU,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,MAAM,CAAC,EAAE;IACnH,MAAM,QAAQ,SAAS,KAAK,CAAC;IAC7B,IAAI,SAAS,QAAQ,MAAM,KAAK,CAAC,EAAE,GAAG;IACtC,IAAI,WAAW,KAAK;QAClB,SAAS,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE;IAClC;IACA,OAAO;AACT;AACA,SAAS,kBAAkB,OAAO,EAAE,YAAY,EAAE,OAAO,IAAI;IAC3D,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAA,SAAU;YAAC;YAAQ,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ;SAAc;IACtF,IAAI,MAAM;QACR,2BAA2B;QAC3B,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,MAAM;IACnD;IACA,OAAO;AACT;AACA,SAAS,iBAAiB,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM;IAC/D,MAAM,iBAAiB,kBAAkB,SAAS;IAElD,oCAAoC;IACpC,IAAI,QAAQ;QACV,eAAe,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ;YACvC,IAAI,YAAY,OAAO,aAAa,EAAE,OAAO,CAAC;YAC9C,IAAI,YAAY,OAAO,aAAa,EAAE,OAAO;YAC7C,MAAM,oBAAoB,OAAO,OAAO,CAAC,QAAQ,CAAC;YAClD,MAAM,oBAAoB,OAAO,OAAO,CAAC,QAAQ,CAAC;YAClD,IAAI,qBAAqB,CAAC,mBAAmB,OAAO,CAAC;YACrD,IAAI,CAAC,qBAAqB,mBAAmB,OAAO;YACpD,OAAO;QACT;IACF;IACA,KAAK,MAAM,CAAC,QAAQ,OAAO,IAAI,eAAgB;QAC7C,IAAI,OAAO;QACX,IAAI,aAAa,UAAU,SAAS,UAAU,CAAC,SAAS,MAAM;YAC5D,QAAQ,UAAU;QACpB,OAAO;YACL,MAAM,qBAAqB,SAAS,WAAW;YAC/C,MAAM,mBAAmB,OAAO,WAAW;YAC3C,IAAI,uBAAuB,oBAAoB,mBAAmB,UAAU,CAAC,mBAAmB,MAAM;gBACpG,QAAQ;gBACR,UAAU;YACZ;QACF;QACA,IAAI,SAAS;YACX,OAAO;gBACL;gBACA;gBACA,eAAe,SAAS,KAAK,CAAC,GAAG,OAAO,MAAM;gBAC9C;YACF;QACF;IACF;AACF;AACA,SAAS,eAAe,QAAQ,EAAE,QAAQ;IACxC,MAAM,qBAAqB,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE;IAClD,MAAM,qBAAqB,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE;IAClD,MAAM,QAAQ,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE;IAC9B,MAAM,QAAQ,MAAM,IAAI,CAAC;IACzB,IAAI,CAAC,OAAO,OAAO;IACnB,MAAM,SAAS,CAAC;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,MAAM,mBAAmB,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,QAAQ,UAAU;QACjF,IAAI,KAAK,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE;IACjC;IACA,OAAO;AACT;AACA,SAAS,uBAAuB,QAAQ,EAAE,MAAM;IAC9C,IAAI,CAAC,QAAQ,OAAO;IAEpB,2DAA2D;IAC3D,qDAAqD;IACrD,WAAW,SAAS,OAAO,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS;IAC3D,IAAI,SAAS;IACb,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC1C,SAAS,OAAO,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;IACtC;IACA,OAAO;AACT;AACA,SAAS,eAAe,QAAQ,EAAE,MAAM,EAAE,MAAM;IAC9C,IAAI,SAAS;IACb,IAAI,QAAQ;QACV,SAAS,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;IAClC;IACA,IAAI,QAAQ;QACV,UAAU;IACZ;IACA,OAAO;AACT;AACA,SAAS,QAAQ,cAAc;IAC7B,OAAO,eAAe,GAAG,CAAC,uBAAuB,eAAe,GAAG,CAAC,WAAW;AACjF;AACA,SAAS,0BAA0B,MAAM,EAAE,MAAM;IAC/C,OAAO,OAAO,aAAa,KAAK,UAAU,OAAO,OAAO,CAAC,QAAQ,CAAC;AACpE;AACA,SAAS,sBAAsB,aAAa,EAAE,MAAM,EAAE,aAAa;IACjE,IAAI;IAEJ,iCAAiC;IACjC,IAAI,iBAAiB,0BAA0B,QAAQ,gBAAgB;QACrE,eAAe;IACjB;IAEA,8DAA8D;IAC9D,IAAI,CAAC,cAAc;QACjB,eAAe,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,aAAa,KAAK;IACjE;IAEA,0DAA0D;IAC1D,IAAI,CAAC,cAAc;QACjB,eAAe,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,OAAO,CAAC,QAAQ,CAAC;IAChE;IACA,OAAO;AACT;AACA,SAAS,cAAc,QAAQ,EAAE,QAAQ;IACvC,OAAO,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE,WAAW;AAC3C;AACA,SAAS,kBAAkB,MAAM;IAC/B,OAAO,CAAC,CAAC,EAAE,QAAQ;AACrB;AACA,SAAS,iBAAiB,QAAQ;IAChC,iCAAiC;IACjC,8CAA8C;IAC9C,yCAAyC;IACzC,OAAO,SAAS,OAAO,CAAC,OAAO,OAAO,OAAO,CAAC,QAAQ;AACxD", "ignoreList": [0]}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/middleware/getAlternateLinksHeaderValue.js"], "sourcesContent": ["import { normalizeTrailingSlash } from '../shared/utils.js';\nimport { getHost, getNormalizedPathname, getLocalePrefixes, isLocaleSupportedOnDomain, applyBasePath, formatTemplatePathname } from './utils.js';\n\n/**\n * See https://developers.google.com/search/docs/specialty/international/localized-versions\n */\nfunction getAlternateLinksHeaderValue({\n  internalTemplateName,\n  localizedPathnames,\n  request,\n  resolvedLocale,\n  routing\n}) {\n  const normalizedUrl = request.nextUrl.clone();\n  const host = getHost(request.headers);\n  if (host) {\n    normalizedUrl.port = '';\n    normalizedUrl.host = host;\n  }\n  normalizedUrl.protocol = request.headers.get('x-forwarded-proto') ?? normalizedUrl.protocol;\n  normalizedUrl.pathname = getNormalizedPathname(normalizedUrl.pathname, routing.locales, routing.localePrefix);\n  function getAlternateEntry(url, locale) {\n    url.pathname = normalizeTrailingSlash(url.pathname);\n    if (request.nextUrl.basePath) {\n      url = new URL(url);\n      url.pathname = applyBasePath(url.pathname, request.nextUrl.basePath);\n    }\n    return `<${url.toString()}>; rel=\"alternate\"; hreflang=\"${locale}\"`;\n  }\n  function getLocalizedPathname(pathname, locale) {\n    if (localizedPathnames && typeof localizedPathnames === 'object') {\n      const sourceTemplate = localizedPathnames[resolvedLocale];\n      return formatTemplatePathname(pathname, sourceTemplate ?? internalTemplateName, localizedPathnames[locale] ?? internalTemplateName);\n    } else {\n      return pathname;\n    }\n  }\n  const links = getLocalePrefixes(routing.locales, routing.localePrefix, false).flatMap(([locale, prefix]) => {\n    function prefixPathname(pathname) {\n      if (pathname === '/') {\n        return prefix;\n      } else {\n        return prefix + pathname;\n      }\n    }\n    let url;\n    if (routing.domains) {\n      const domainConfigs = routing.domains.filter(cur => isLocaleSupportedOnDomain(locale, cur));\n      return domainConfigs.map(domainConfig => {\n        url = new URL(normalizedUrl);\n        url.port = '';\n        url.host = domainConfig.domain;\n\n        // Important: Use `normalizedUrl` here, as `url` potentially uses\n        // a `basePath` that automatically gets applied to the pathname\n        url.pathname = getLocalizedPathname(normalizedUrl.pathname, locale);\n        if (locale !== domainConfig.defaultLocale || routing.localePrefix.mode === 'always') {\n          url.pathname = prefixPathname(url.pathname);\n        }\n        return getAlternateEntry(url, locale);\n      });\n    } else {\n      let pathname;\n      if (localizedPathnames && typeof localizedPathnames === 'object') {\n        pathname = getLocalizedPathname(normalizedUrl.pathname, locale);\n      } else {\n        pathname = normalizedUrl.pathname;\n      }\n      if (locale !== routing.defaultLocale || routing.localePrefix.mode === 'always') {\n        pathname = prefixPathname(pathname);\n      }\n      url = new URL(pathname, normalizedUrl);\n    }\n    return getAlternateEntry(url, locale);\n  });\n\n  // Add x-default entry\n  const shouldAddXDefault =\n  // For domain-based routing there is no reasonable x-default\n  !routing.domains || routing.domains.length === 0;\n  if (shouldAddXDefault) {\n    const localizedPathname = getLocalizedPathname(normalizedUrl.pathname, routing.defaultLocale);\n    if (localizedPathname) {\n      const url = new URL(localizedPathname, normalizedUrl);\n      links.push(getAlternateEntry(url, 'x-default'));\n    }\n  }\n  return links.join(', ');\n}\n\nexport { getAlternateLinksHeaderValue as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA;;CAEC,GACD,SAAS,6BAA6B,EACpC,oBAAoB,EACpB,kBAAkB,EAClB,OAAO,EACP,cAAc,EACd,OAAO,EACR;IACC,MAAM,gBAAgB,QAAQ,OAAO,CAAC,KAAK;IAC3C,MAAM,OAAO,CAAA,GAAA,yLAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,OAAO;IACpC,IAAI,MAAM;QACR,cAAc,IAAI,GAAG;QACrB,cAAc,IAAI,GAAG;IACvB;IACA,cAAc,QAAQ,GAAG,QAAQ,OAAO,CAAC,GAAG,CAAC,wBAAwB,cAAc,QAAQ;IAC3F,cAAc,QAAQ,GAAG,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc,QAAQ,EAAE,QAAQ,OAAO,EAAE,QAAQ,YAAY;IAC5G,SAAS,kBAAkB,GAAG,EAAE,MAAM;QACpC,IAAI,QAAQ,GAAG,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE,IAAI,QAAQ;QAClD,IAAI,QAAQ,OAAO,CAAC,QAAQ,EAAE;YAC5B,MAAM,IAAI,IAAI;YACd,IAAI,QAAQ,GAAG,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,QAAQ,EAAE,QAAQ,OAAO,CAAC,QAAQ;QACrE;QACA,OAAO,CAAC,CAAC,EAAE,IAAI,QAAQ,GAAG,8BAA8B,EAAE,OAAO,CAAC,CAAC;IACrE;IACA,SAAS,qBAAqB,QAAQ,EAAE,MAAM;QAC5C,IAAI,sBAAsB,OAAO,uBAAuB,UAAU;YAChE,MAAM,iBAAiB,kBAAkB,CAAC,eAAe;YACzD,OAAO,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,UAAU,kBAAkB,sBAAsB,kBAAkB,CAAC,OAAO,IAAI;QAChH,OAAO;YACL,OAAO;QACT;IACF;IACA,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,OAAO,EAAE,QAAQ,YAAY,EAAE,OAAO,OAAO,CAAC,CAAC,CAAC,QAAQ,OAAO;QACrG,SAAS,eAAe,QAAQ;YAC9B,IAAI,aAAa,KAAK;gBACpB,OAAO;YACT,OAAO;gBACL,OAAO,SAAS;YAClB;QACF;QACA,IAAI;QACJ,IAAI,QAAQ,OAAO,EAAE;YACnB,MAAM,gBAAgB,QAAQ,OAAO,CAAC,MAAM,CAAC,CAAA,MAAO,CAAA,GAAA,yLAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ;YACtF,OAAO,cAAc,GAAG,CAAC,CAAA;gBACvB,MAAM,IAAI,IAAI;gBACd,IAAI,IAAI,GAAG;gBACX,IAAI,IAAI,GAAG,aAAa,MAAM;gBAE9B,iEAAiE;gBACjE,+DAA+D;gBAC/D,IAAI,QAAQ,GAAG,qBAAqB,cAAc,QAAQ,EAAE;gBAC5D,IAAI,WAAW,aAAa,aAAa,IAAI,QAAQ,YAAY,CAAC,IAAI,KAAK,UAAU;oBACnF,IAAI,QAAQ,GAAG,eAAe,IAAI,QAAQ;gBAC5C;gBACA,OAAO,kBAAkB,KAAK;YAChC;QACF,OAAO;YACL,IAAI;YACJ,IAAI,sBAAsB,OAAO,uBAAuB,UAAU;gBAChE,WAAW,qBAAqB,cAAc,QAAQ,EAAE;YAC1D,OAAO;gBACL,WAAW,cAAc,QAAQ;YACnC;YACA,IAAI,WAAW,QAAQ,aAAa,IAAI,QAAQ,YAAY,CAAC,IAAI,KAAK,UAAU;gBAC9E,WAAW,eAAe;YAC5B;YACA,MAAM,IAAI,IAAI,UAAU;QAC1B;QACA,OAAO,kBAAkB,KAAK;IAChC;IAEA,sBAAsB;IACtB,MAAM,oBACN,4DAA4D;IAC5D,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,KAAK;IAC/C,IAAI,mBAAmB;QACrB,MAAM,oBAAoB,qBAAqB,cAAc,QAAQ,EAAE,QAAQ,aAAa;QAC5F,IAAI,mBAAmB;YACrB,MAAM,MAAM,IAAI,IAAI,mBAAmB;YACvC,MAAM,IAAI,CAAC,kBAAkB,KAAK;QACpC;IACF;IACA,OAAO,MAAM,IAAI,CAAC;AACpB", "ignoreList": [0]}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/middleware/resolveLocale.js"], "sourcesContent": ["import { match } from '@formatjs/intl-localematcher';\nimport <PERSON>egotiator from 'negotiator';\nimport { getPathnameMatch, isLocaleSupportedOnDomain, getHost } from './utils.js';\n\nfunction findDomainFromHost(requestHeaders, domains) {\n  const host = getHost(requestHeaders);\n  if (host) {\n    return domains.find(cur => cur.domain === host);\n  }\n  return undefined;\n}\nfunction orderLocales(locales) {\n  // Workaround for https://github.com/formatjs/formatjs/issues/4469\n  return locales.slice().sort((a, b) => b.length - a.length);\n}\nfunction getAcceptLanguageLocale(requestHeaders, locales, defaultLocale) {\n  let locale;\n  const languages = new Negotiator({\n    headers: {\n      'accept-language': requestHeaders.get('accept-language') || undefined\n    }\n  }).languages();\n  try {\n    const orderedLocales = orderLocales(locales);\n    locale = match(languages, orderedLocales, defaultLocale);\n  } catch {\n    // Invalid language\n  }\n  return locale;\n}\nfunction getLocaleFromCookie(routing, requestCookies) {\n  if (routing.localeCookie && requestCookies.has(routing.localeCookie.name)) {\n    const value = requestCookies.get(routing.localeCookie.name)?.value;\n    if (value && routing.locales.includes(value)) {\n      return value;\n    }\n  }\n}\nfunction resolveLocaleFromPrefix(routing, requestHeaders, requestCookies, pathname) {\n  let locale;\n\n  // Prio 1: Use route prefix\n  if (pathname) {\n    locale = getPathnameMatch(pathname, routing.locales, routing.localePrefix)?.locale;\n  }\n\n  // Prio 2: Use existing cookie\n  if (!locale && routing.localeDetection) {\n    locale = getLocaleFromCookie(routing, requestCookies);\n  }\n\n  // Prio 3: Use the `accept-language` header\n  if (!locale && routing.localeDetection) {\n    locale = getAcceptLanguageLocale(requestHeaders, routing.locales, routing.defaultLocale);\n  }\n\n  // Prio 4: Use default locale\n  if (!locale) {\n    locale = routing.defaultLocale;\n  }\n  return locale;\n}\nfunction resolveLocaleFromDomain(routing, requestHeaders, requestCookies, pathname) {\n  const domains = routing.domains;\n  const domain = findDomainFromHost(requestHeaders, domains);\n  if (!domain) {\n    return {\n      locale: resolveLocaleFromPrefix(routing, requestHeaders, requestCookies, pathname)\n    };\n  }\n  let locale;\n\n  // Prio 1: Use route prefix\n  if (pathname) {\n    const prefixLocale = getPathnameMatch(pathname, routing.locales, routing.localePrefix, domain)?.locale;\n    if (prefixLocale) {\n      if (isLocaleSupportedOnDomain(prefixLocale, domain)) {\n        locale = prefixLocale;\n      } else {\n        // Causes a redirect to a domain that supports the locale\n        return {\n          locale: prefixLocale,\n          domain\n        };\n      }\n    }\n  }\n\n  // Prio 2: Use existing cookie\n  if (!locale && routing.localeDetection) {\n    const cookieLocale = getLocaleFromCookie(routing, requestCookies);\n    if (cookieLocale) {\n      if (isLocaleSupportedOnDomain(cookieLocale, domain)) {\n        locale = cookieLocale;\n      }\n    }\n  }\n\n  // Prio 3: Use the `accept-language` header\n  if (!locale && routing.localeDetection) {\n    const headerLocale = getAcceptLanguageLocale(requestHeaders, domain.locales, domain.defaultLocale);\n    if (headerLocale) {\n      locale = headerLocale;\n    }\n  }\n\n  // Prio 4: Use default locale\n  if (!locale) {\n    locale = domain.defaultLocale;\n  }\n  return {\n    locale,\n    domain\n  };\n}\nfunction resolveLocale(routing, requestHeaders, requestCookies, pathname) {\n  if (routing.domains) {\n    return resolveLocaleFromDomain(routing, requestHeaders, requestCookies, pathname);\n  } else {\n    return {\n      locale: resolveLocaleFromPrefix(routing, requestHeaders, requestCookies, pathname)\n    };\n  }\n}\n\nexport { resolveLocale as default, getAcceptLanguageLocale };\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;;;;AAEA,SAAS,mBAAmB,cAAc,EAAE,OAAO;IACjD,MAAM,OAAO,CAAA,GAAA,yLAAA,CAAA,UAAO,AAAD,EAAE;IACrB,IAAI,MAAM;QACR,OAAO,QAAQ,IAAI,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK;IAC5C;IACA,OAAO;AACT;AACA,SAAS,aAAa,OAAO;IAC3B,kEAAkE;IAClE,OAAO,QAAQ,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;AAC3D;AACA,SAAS,wBAAwB,cAAc,EAAE,OAAO,EAAE,aAAa;IACrE,IAAI;IACJ,MAAM,YAAY,IAAI,2IAAA,CAAA,UAAU,CAAC;QAC/B,SAAS;YACP,mBAAmB,eAAe,GAAG,CAAC,sBAAsB;QAC9D;IACF,GAAG,SAAS;IACZ,IAAI;QACF,MAAM,iBAAiB,aAAa;QACpC,SAAS,CAAA,GAAA,2LAAA,CAAA,QAAK,AAAD,EAAE,WAAW,gBAAgB;IAC5C,EAAE,OAAM;IACN,mBAAmB;IACrB;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,OAAO,EAAE,cAAc;IAClD,IAAI,QAAQ,YAAY,IAAI,eAAe,GAAG,CAAC,QAAQ,YAAY,CAAC,IAAI,GAAG;QACzE,MAAM,QAAQ,eAAe,GAAG,CAAC,QAAQ,YAAY,CAAC,IAAI,GAAG;QAC7D,IAAI,SAAS,QAAQ,OAAO,CAAC,QAAQ,CAAC,QAAQ;YAC5C,OAAO;QACT;IACF;AACF;AACA,SAAS,wBAAwB,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ;IAChF,IAAI;IAEJ,2BAA2B;IAC3B,IAAI,UAAU;QACZ,SAAS,CAAA,GAAA,yLAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,QAAQ,OAAO,EAAE,QAAQ,YAAY,GAAG;IAC9E;IAEA,8BAA8B;IAC9B,IAAI,CAAC,UAAU,QAAQ,eAAe,EAAE;QACtC,SAAS,oBAAoB,SAAS;IACxC;IAEA,2CAA2C;IAC3C,IAAI,CAAC,UAAU,QAAQ,eAAe,EAAE;QACtC,SAAS,wBAAwB,gBAAgB,QAAQ,OAAO,EAAE,QAAQ,aAAa;IACzF;IAEA,6BAA6B;IAC7B,IAAI,CAAC,QAAQ;QACX,SAAS,QAAQ,aAAa;IAChC;IACA,OAAO;AACT;AACA,SAAS,wBAAwB,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ;IAChF,MAAM,UAAU,QAAQ,OAAO;IAC/B,MAAM,SAAS,mBAAmB,gBAAgB;IAClD,IAAI,CAAC,QAAQ;QACX,OAAO;YACL,QAAQ,wBAAwB,SAAS,gBAAgB,gBAAgB;QAC3E;IACF;IACA,IAAI;IAEJ,2BAA2B;IAC3B,IAAI,UAAU;QACZ,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,QAAQ,OAAO,EAAE,QAAQ,YAAY,EAAE,SAAS;QAChG,IAAI,cAAc;YAChB,IAAI,CAAA,GAAA,yLAAA,CAAA,4BAAyB,AAAD,EAAE,cAAc,SAAS;gBACnD,SAAS;YACX,OAAO;gBACL,yDAAyD;gBACzD,OAAO;oBACL,QAAQ;oBACR;gBACF;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,IAAI,CAAC,UAAU,QAAQ,eAAe,EAAE;QACtC,MAAM,eAAe,oBAAoB,SAAS;QAClD,IAAI,cAAc;YAChB,IAAI,CAAA,GAAA,yLAAA,CAAA,4BAAyB,AAAD,EAAE,cAAc,SAAS;gBACnD,SAAS;YACX;QACF;IACF;IAEA,2CAA2C;IAC3C,IAAI,CAAC,UAAU,QAAQ,eAAe,EAAE;QACtC,MAAM,eAAe,wBAAwB,gBAAgB,OAAO,OAAO,EAAE,OAAO,aAAa;QACjG,IAAI,cAAc;YAChB,SAAS;QACX;IACF;IAEA,6BAA6B;IAC7B,IAAI,CAAC,QAAQ;QACX,SAAS,OAAO,aAAa;IAC/B;IACA,OAAO;QACL;QACA;IACF;AACF;AACA,SAAS,cAAc,OAAO,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ;IACtE,IAAI,QAAQ,OAAO,EAAE;QACnB,OAAO,wBAAwB,SAAS,gBAAgB,gBAAgB;IAC1E,OAAO;QACL,OAAO;YACL,QAAQ,wBAAwB,SAAS,gBAAgB,gBAAgB;QAC3E;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/middleware/syncCookie.js"], "sourcesContent": ["import { getAcceptLanguageLocale } from './resolveLocale.js';\n\nfunction syncCookie(request, response, locale, routing, domain) {\n  if (!routing.localeCookie) return;\n  const {\n    name,\n    ...rest\n  } = routing.localeCookie;\n  const acceptLanguageLocale = getAcceptLanguageLocale(request.headers, domain?.locales || routing.locales, routing.defaultLocale);\n  const hasLocaleCookie = request.cookies.has(name);\n  const hasOutdatedCookie = hasLocaleCookie && request.cookies.get(name)?.value !== locale;\n  if (hasLocaleCookie ? hasOutdatedCookie : acceptLanguageLocale !== locale) {\n    response.cookies.set(name, locale, {\n      path: request.nextUrl.basePath || undefined,\n      ...rest\n    });\n  }\n}\n\nexport { syncCookie as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,WAAW,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;IAC5D,IAAI,CAAC,QAAQ,YAAY,EAAE;IAC3B,MAAM,EACJ,IAAI,EACJ,GAAG,MACJ,GAAG,QAAQ,YAAY;IACxB,MAAM,uBAAuB,CAAA,GAAA,iMAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,OAAO,EAAE,QAAQ,WAAW,QAAQ,OAAO,EAAE,QAAQ,aAAa;IAC/H,MAAM,kBAAkB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC5C,MAAM,oBAAoB,mBAAmB,QAAQ,OAAO,CAAC,GAAG,CAAC,OAAO,UAAU;IAClF,IAAI,kBAAkB,oBAAoB,yBAAyB,QAAQ;QACzE,SAAS,OAAO,CAAC,GAAG,CAAC,MAAM,QAAQ;YACjC,MAAM,QAAQ,OAAO,CAAC,QAAQ,IAAI;YAClC,GAAG,IAAI;QACT;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/middleware/middleware.js"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport { receiveRoutingConfig } from '../routing/config.js';\nimport { HEADER_LOCALE_NAME } from '../shared/constants.js';\nimport { matchesPathname, normalizeTrailingSlash, getLocalePrefix, getLocalizedTemplate } from '../shared/utils.js';\nimport getAlternateLinksHeaderValue from './getAlternateLinksHeaderValue.js';\nimport resolveLocale from './resolveLocale.js';\nimport syncCookie from './syncCookie.js';\nimport { sanitizePathname, isLocaleSupportedOnDomain, getNormalizedPathname, getPathnameMatch, getInternalTemplate, formatTemplatePathname, formatPathname, getBestMatchingDomain, applyBasePath, getLocaleAsPrefix } from './utils.js';\n\nfunction createMiddleware(routing) {\n  const resolvedRouting = receiveRoutingConfig(routing);\n  return function middleware(request) {\n    let unsafeExternalPathname;\n    try {\n      // Resolve potential foreign symbols (e.g. /ja/%E7%B4%84 → /ja/約))\n      unsafeExternalPathname = decodeURI(request.nextUrl.pathname);\n    } catch {\n      // In case an invalid pathname is encountered, forward\n      // it to Next.js which in turn responds with a 400\n      return NextResponse.next();\n    }\n\n    // Sanitize malicious URIs to prevent open redirect attacks due to\n    // decodeURI doesn't escape encoded backslashes ('%5C' & '%5c')\n    const externalPathname = sanitizePathname(unsafeExternalPathname);\n    const {\n      domain,\n      locale\n    } = resolveLocale(resolvedRouting, request.headers, request.cookies, externalPathname);\n    const hasMatchedDefaultLocale = domain ? domain.defaultLocale === locale : locale === resolvedRouting.defaultLocale;\n    const domainsConfig = resolvedRouting.domains?.filter(curDomain => isLocaleSupportedOnDomain(locale, curDomain)) || [];\n    const hasUnknownHost = resolvedRouting.domains != null && !domain;\n    function rewrite(url) {\n      const urlObj = new URL(url, request.url);\n      if (request.nextUrl.basePath) {\n        urlObj.pathname = applyBasePath(urlObj.pathname, request.nextUrl.basePath);\n      }\n      const headers = new Headers(request.headers);\n      headers.set(HEADER_LOCALE_NAME, locale);\n      return NextResponse.rewrite(urlObj, {\n        request: {\n          headers\n        }\n      });\n    }\n    function redirect(url, redirectDomain) {\n      const urlObj = new URL(url, request.url);\n      urlObj.pathname = normalizeTrailingSlash(urlObj.pathname);\n      if (domainsConfig.length > 0 && !redirectDomain && domain) {\n        const bestMatchingDomain = getBestMatchingDomain(domain, locale, domainsConfig);\n        if (bestMatchingDomain) {\n          redirectDomain = bestMatchingDomain.domain;\n          if (bestMatchingDomain.defaultLocale === locale && resolvedRouting.localePrefix.mode === 'as-needed') {\n            urlObj.pathname = getNormalizedPathname(urlObj.pathname, resolvedRouting.locales, resolvedRouting.localePrefix);\n          }\n        }\n      }\n      if (redirectDomain) {\n        urlObj.host = redirectDomain;\n        if (request.headers.get('x-forwarded-host')) {\n          urlObj.protocol = request.headers.get('x-forwarded-proto') ?? request.nextUrl.protocol;\n          const redirectDomainPort = redirectDomain.split(':')[1];\n          urlObj.port = redirectDomainPort ?? request.headers.get('x-forwarded-port') ?? '';\n        }\n      }\n      if (request.nextUrl.basePath) {\n        urlObj.pathname = applyBasePath(urlObj.pathname, request.nextUrl.basePath);\n      }\n      hasRedirected = true;\n      return NextResponse.redirect(urlObj.toString());\n    }\n    const unprefixedExternalPathname = getNormalizedPathname(externalPathname, resolvedRouting.locales, resolvedRouting.localePrefix);\n    const pathnameMatch = getPathnameMatch(externalPathname, resolvedRouting.locales, resolvedRouting.localePrefix, domain);\n    const hasLocalePrefix = pathnameMatch != null;\n    const isUnprefixedRouting = resolvedRouting.localePrefix.mode === 'never' || hasMatchedDefaultLocale && resolvedRouting.localePrefix.mode === 'as-needed';\n    let response;\n    let internalTemplateName;\n    let hasRedirected;\n    let unprefixedInternalPathname = unprefixedExternalPathname;\n    const pathnames = resolvedRouting.pathnames;\n    if (pathnames) {\n      let resolvedTemplateLocale;\n      [resolvedTemplateLocale, internalTemplateName] = getInternalTemplate(pathnames, unprefixedExternalPathname, locale);\n      if (internalTemplateName) {\n        const pathnameConfig = pathnames[internalTemplateName];\n        const localeTemplate = getLocalizedTemplate(pathnameConfig, locale, internalTemplateName);\n        if (matchesPathname(localeTemplate, unprefixedExternalPathname)) {\n          unprefixedInternalPathname = formatTemplatePathname(unprefixedExternalPathname, localeTemplate, internalTemplateName);\n        } else {\n          let sourceTemplate;\n          if (resolvedTemplateLocale) {\n            // A localized pathname from another locale has matched\n            sourceTemplate = getLocalizedTemplate(pathnameConfig, resolvedTemplateLocale, internalTemplateName);\n          } else {\n            // An internal pathname has matched that\n            // doesn't have a localized pathname\n            sourceTemplate = internalTemplateName;\n          }\n          const localePrefix = isUnprefixedRouting ? undefined : getLocalePrefix(locale, resolvedRouting.localePrefix);\n          const template = formatTemplatePathname(unprefixedExternalPathname, sourceTemplate, localeTemplate);\n          response = redirect(formatPathname(template, localePrefix, request.nextUrl.search));\n        }\n      }\n    }\n    if (!response) {\n      if (unprefixedInternalPathname === '/' && !hasLocalePrefix) {\n        if (isUnprefixedRouting) {\n          response = rewrite(formatPathname(unprefixedInternalPathname, getLocaleAsPrefix(locale), request.nextUrl.search));\n        } else {\n          response = redirect(formatPathname(unprefixedExternalPathname, getLocalePrefix(locale, resolvedRouting.localePrefix), request.nextUrl.search));\n        }\n      } else {\n        const internalHref = formatPathname(unprefixedInternalPathname, getLocaleAsPrefix(locale), request.nextUrl.search);\n        if (hasLocalePrefix) {\n          const externalHref = formatPathname(unprefixedExternalPathname, pathnameMatch.prefix, request.nextUrl.search);\n          if (resolvedRouting.localePrefix.mode === 'never') {\n            response = redirect(formatPathname(unprefixedExternalPathname, undefined, request.nextUrl.search));\n          } else if (pathnameMatch.exact) {\n            if (hasMatchedDefaultLocale && isUnprefixedRouting) {\n              response = redirect(formatPathname(unprefixedExternalPathname, undefined, request.nextUrl.search));\n            } else {\n              if (resolvedRouting.domains) {\n                const pathDomain = getBestMatchingDomain(domain, pathnameMatch.locale, domainsConfig);\n                if (domain?.domain !== pathDomain?.domain && !hasUnknownHost) {\n                  response = redirect(externalHref, pathDomain?.domain);\n                } else {\n                  response = rewrite(internalHref);\n                }\n              } else {\n                response = rewrite(internalHref);\n              }\n            }\n          } else {\n            response = redirect(externalHref);\n          }\n        } else {\n          if (isUnprefixedRouting) {\n            response = rewrite(internalHref);\n          } else {\n            response = redirect(formatPathname(unprefixedExternalPathname, getLocalePrefix(locale, resolvedRouting.localePrefix), request.nextUrl.search));\n          }\n        }\n      }\n    }\n    syncCookie(request, response, locale, resolvedRouting, domain);\n    if (!hasRedirected && resolvedRouting.localePrefix.mode !== 'never' && resolvedRouting.alternateLinks && resolvedRouting.locales.length > 1) {\n      response.headers.set('Link', getAlternateLinksHeaderValue({\n        routing: resolvedRouting,\n        internalTemplateName,\n        localizedPathnames: internalTemplateName != null && pathnames ? pathnames[internalTemplateName] : undefined,\n        request,\n        resolvedLocale: locale\n      }));\n    }\n    return response;\n  };\n}\n\nexport { createMiddleware as default };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,SAAS,iBAAiB,OAAO;IAC/B,MAAM,kBAAkB,CAAA,GAAA,uLAAA,CAAA,uBAAoB,AAAD,EAAE;IAC7C,OAAO,SAAS,WAAW,OAAO;QAChC,IAAI;QACJ,IAAI;YACF,kEAAkE;YAClE,yBAAyB,UAAU,QAAQ,OAAO,CAAC,QAAQ;QAC7D,EAAE,OAAM;YACN,sDAAsD;YACtD,kDAAkD;YAClD,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;QAC1B;QAEA,kEAAkE;QAClE,+DAA+D;QAC/D,MAAM,mBAAmB,CAAA,GAAA,yLAAA,CAAA,mBAAgB,AAAD,EAAE;QAC1C,MAAM,EACJ,MAAM,EACN,MAAM,EACP,GAAG,CAAA,GAAA,iMAAA,CAAA,UAAa,AAAD,EAAE,iBAAiB,QAAQ,OAAO,EAAE,QAAQ,OAAO,EAAE;QACrE,MAAM,0BAA0B,SAAS,OAAO,aAAa,KAAK,SAAS,WAAW,gBAAgB,aAAa;QACnH,MAAM,gBAAgB,gBAAgB,OAAO,EAAE,OAAO,CAAA,YAAa,CAAA,GAAA,yLAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,eAAe,EAAE;QACtH,MAAM,iBAAiB,gBAAgB,OAAO,IAAI,QAAQ,CAAC;QAC3D,SAAS,QAAQ,GAAG;YAClB,MAAM,SAAS,IAAI,IAAI,KAAK,QAAQ,GAAG;YACvC,IAAI,QAAQ,OAAO,CAAC,QAAQ,EAAE;gBAC5B,OAAO,QAAQ,GAAG,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,QAAQ,EAAE,QAAQ,OAAO,CAAC,QAAQ;YAC3E;YACA,MAAM,UAAU,IAAI,QAAQ,QAAQ,OAAO;YAC3C,QAAQ,GAAG,CAAC,yLAAA,CAAA,qBAAkB,EAAE;YAChC,OAAO,6LAAA,CAAA,eAAY,CAAC,OAAO,CAAC,QAAQ;gBAClC,SAAS;oBACP;gBACF;YACF;QACF;QACA,SAAS,SAAS,GAAG,EAAE,cAAc;YACnC,MAAM,SAAS,IAAI,IAAI,KAAK,QAAQ,GAAG;YACvC,OAAO,QAAQ,GAAG,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,QAAQ;YACxD,IAAI,cAAc,MAAM,GAAG,KAAK,CAAC,kBAAkB,QAAQ;gBACzD,MAAM,qBAAqB,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ,QAAQ;gBACjE,IAAI,oBAAoB;oBACtB,iBAAiB,mBAAmB,MAAM;oBAC1C,IAAI,mBAAmB,aAAa,KAAK,UAAU,gBAAgB,YAAY,CAAC,IAAI,KAAK,aAAa;wBACpG,OAAO,QAAQ,GAAG,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,OAAO,QAAQ,EAAE,gBAAgB,OAAO,EAAE,gBAAgB,YAAY;oBAChH;gBACF;YACF;YACA,IAAI,gBAAgB;gBAClB,OAAO,IAAI,GAAG;gBACd,IAAI,QAAQ,OAAO,CAAC,GAAG,CAAC,qBAAqB;oBAC3C,OAAO,QAAQ,GAAG,QAAQ,OAAO,CAAC,GAAG,CAAC,wBAAwB,QAAQ,OAAO,CAAC,QAAQ;oBACtF,MAAM,qBAAqB,eAAe,KAAK,CAAC,IAAI,CAAC,EAAE;oBACvD,OAAO,IAAI,GAAG,sBAAsB,QAAQ,OAAO,CAAC,GAAG,CAAC,uBAAuB;gBACjF;YACF;YACA,IAAI,QAAQ,OAAO,CAAC,QAAQ,EAAE;gBAC5B,OAAO,QAAQ,GAAG,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,QAAQ,EAAE,QAAQ,OAAO,CAAC,QAAQ;YAC3E;YACA,gBAAgB;YAChB,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,OAAO,QAAQ;QAC9C;QACA,MAAM,6BAA6B,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,kBAAkB,gBAAgB,OAAO,EAAE,gBAAgB,YAAY;QAChI,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,mBAAgB,AAAD,EAAE,kBAAkB,gBAAgB,OAAO,EAAE,gBAAgB,YAAY,EAAE;QAChH,MAAM,kBAAkB,iBAAiB;QACzC,MAAM,sBAAsB,gBAAgB,YAAY,CAAC,IAAI,KAAK,WAAW,2BAA2B,gBAAgB,YAAY,CAAC,IAAI,KAAK;QAC9I,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,6BAA6B;QACjC,MAAM,YAAY,gBAAgB,SAAS;QAC3C,IAAI,WAAW;YACb,IAAI;YACJ,CAAC,wBAAwB,qBAAqB,GAAG,CAAA,GAAA,yLAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,4BAA4B;YAC5G,IAAI,sBAAsB;gBACxB,MAAM,iBAAiB,SAAS,CAAC,qBAAqB;gBACtD,MAAM,iBAAiB,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,QAAQ;gBACpE,IAAI,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,6BAA6B;oBAC/D,6BAA6B,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,4BAA4B,gBAAgB;gBAClG,OAAO;oBACL,IAAI;oBACJ,IAAI,wBAAwB;wBAC1B,uDAAuD;wBACvD,iBAAiB,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,wBAAwB;oBAChF,OAAO;wBACL,wCAAwC;wBACxC,oCAAoC;wBACpC,iBAAiB;oBACnB;oBACA,MAAM,eAAe,sBAAsB,YAAY,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,gBAAgB,YAAY;oBAC3G,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,yBAAsB,AAAD,EAAE,4BAA4B,gBAAgB;oBACpF,WAAW,SAAS,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,cAAc,QAAQ,OAAO,CAAC,MAAM;gBACnF;YACF;QACF;QACA,IAAI,CAAC,UAAU;YACb,IAAI,+BAA+B,OAAO,CAAC,iBAAiB;gBAC1D,IAAI,qBAAqB;oBACvB,WAAW,QAAQ,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,CAAA,GAAA,yLAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,QAAQ,OAAO,CAAC,MAAM;gBACjH,OAAO;oBACL,WAAW,SAAS,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,gBAAgB,YAAY,GAAG,QAAQ,OAAO,CAAC,MAAM;gBAC9I;YACF,OAAO;gBACL,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,CAAA,GAAA,yLAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,QAAQ,OAAO,CAAC,MAAM;gBACjH,IAAI,iBAAiB;oBACnB,MAAM,eAAe,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,cAAc,MAAM,EAAE,QAAQ,OAAO,CAAC,MAAM;oBAC5G,IAAI,gBAAgB,YAAY,CAAC,IAAI,KAAK,SAAS;wBACjD,WAAW,SAAS,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,WAAW,QAAQ,OAAO,CAAC,MAAM;oBAClG,OAAO,IAAI,cAAc,KAAK,EAAE;wBAC9B,IAAI,2BAA2B,qBAAqB;4BAClD,WAAW,SAAS,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,WAAW,QAAQ,OAAO,CAAC,MAAM;wBAClG,OAAO;4BACL,IAAI,gBAAgB,OAAO,EAAE;gCAC3B,MAAM,aAAa,CAAA,GAAA,yLAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ,cAAc,MAAM,EAAE;gCACvE,IAAI,QAAQ,WAAW,YAAY,UAAU,CAAC,gBAAgB;oCAC5D,WAAW,SAAS,cAAc,YAAY;gCAChD,OAAO;oCACL,WAAW,QAAQ;gCACrB;4BACF,OAAO;gCACL,WAAW,QAAQ;4BACrB;wBACF;oBACF,OAAO;wBACL,WAAW,SAAS;oBACtB;gBACF,OAAO;oBACL,IAAI,qBAAqB;wBACvB,WAAW,QAAQ;oBACrB,OAAO;wBACL,WAAW,SAAS,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE,4BAA4B,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,gBAAgB,YAAY,GAAG,QAAQ,OAAO,CAAC,MAAM;oBAC9I;gBACF;YACF;QACF;QACA,CAAA,GAAA,8LAAA,CAAA,UAAU,AAAD,EAAE,SAAS,UAAU,QAAQ,iBAAiB;QACvD,IAAI,CAAC,iBAAiB,gBAAgB,YAAY,CAAC,IAAI,KAAK,WAAW,gBAAgB,cAAc,IAAI,gBAAgB,OAAO,CAAC,MAAM,GAAG,GAAG;YAC3I,SAAS,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAA,GAAA,gNAAA,CAAA,UAA4B,AAAD,EAAE;gBACxD,SAAS;gBACT;gBACA,oBAAoB,wBAAwB,QAAQ,YAAY,SAAS,CAAC,qBAAqB,GAAG;gBAClG;gBACA,gBAAgB;YAClB;QACF;QACA,OAAO;IACT;AACF", "ignoreList": [0]}}, {"offset": {"line": 827, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/routing/defineRouting.js"], "sourcesContent": ["function defineRouting(config) {\n  if (config.domains) {\n    validateUniqueLocalesPerDomain(config.domains);\n  }\n  return config;\n}\nfunction validateUniqueLocalesPerDomain(domains) {\n  const domainsByLocale = new Map();\n  for (const {\n    domain,\n    locales\n  } of domains) {\n    for (const locale of locales) {\n      const localeDomains = domainsByLocale.get(locale) || new Set();\n      localeDomains.add(domain);\n      domainsByLocale.set(locale, localeDomains);\n    }\n  }\n  const duplicateLocaleMessages = Array.from(domainsByLocale.entries()).filter(([, localeDomains]) => localeDomains.size > 1).map(([locale, localeDomains]) => `- \"${locale}\" is used by: ${Array.from(localeDomains).join(', ')}`);\n  if (duplicateLocaleMessages.length > 0) {\n    console.warn('Locales are expected to be unique per domain, but found overlap:\\n' + duplicateLocaleMessages.join('\\n') + '\\nPlease see https://next-intl.dev/docs/routing#domains');\n  }\n}\n\nexport { defineRouting as default };\n"], "names": [], "mappings": ";;;AAAA,SAAS,cAAc,MAAM;IAC3B,IAAI,OAAO,OAAO,EAAE;QAClB,+BAA+B,OAAO,OAAO;IAC/C;IACA,OAAO;AACT;AACA,SAAS,+BAA+B,OAAO;IAC7C,MAAM,kBAAkB,IAAI;IAC5B,KAAK,MAAM,EACT,MAAM,EACN,OAAO,EACR,IAAI,QAAS;QACZ,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,gBAAgB,gBAAgB,GAAG,CAAC,WAAW,IAAI;YACzD,cAAc,GAAG,CAAC;YAClB,gBAAgB,GAAG,CAAC,QAAQ;QAC9B;IACF;IACA,MAAM,0BAA0B,MAAM,IAAI,CAAC,gBAAgB,OAAO,IAAI,MAAM,CAAC,CAAC,GAAG,cAAc,GAAK,cAAc,IAAI,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,cAAc,GAAK,CAAC,GAAG,EAAE,OAAO,cAAc,EAAE,MAAM,IAAI,CAAC,eAAe,IAAI,CAAC,OAAO;IAChO,IAAI,wBAAwB,MAAM,GAAG,GAAG;QACtC,QAAQ,IAAI,CAAC,uEAAuE,wBAAwB,IAAI,CAAC,QAAQ;IAC3H;AACF", "ignoreList": [0]}}, {"offset": {"line": 867, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/shared/use.js"], "sourcesContent": ["import * as react from 'react';\n\n// @ts-expect-error -- Ooof, Next.js doesn't make this easy.\n// `use` is only available in React 19 canary, but we can\n// use it in Next.js already as Next.js \"vendors\" a fixed\n// version of React. However, if we'd simply put `use` in\n// ESM code, then the build doesn't work since React does\n// not export `use` officially. Therefore, we have to use\n// something that is not statically analyzable. Once React\n// 19 is out, we can remove this in the next major version.\nvar use = react['use'.trim()];\n\nexport { use as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,4DAA4D;AAC5D,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,yDAAyD;AACzD,0DAA0D;AAC1D,2DAA2D;AAC3D,IAAI,MAAM,qLAAK,CAAC,MAAM,IAAI,GAAG", "ignoreList": [0]}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6U,GAC1W,2GACA", "ignoreList": [0]}}, {"offset": {"line": 902, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/next-intl/dist/esm/development/navigation/shared/BaseLink.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,yMAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyT,GACtV,uFACA", "ignoreList": [0]}}, {"offset": {"line": 916, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/navigation/shared/utils.js"], "sourcesContent": ["import { getSortedPathnames, matchesPathname, isLocalizableHref, prefixPathname, getLocalizedTemplate, normalizeTrailingSlash, getLocalePrefix } from '../../shared/utils.js';\n\n// Minor false positive: A route that has both optional and\n// required params will allow optional params.\n\n// For `Link`\n\n// For `getPathname` (hence also its consumers: `redirect`, `useRouter`, …)\n\nfunction normalizeNameOrNameWithParams(href) {\n  return typeof href === 'string' ? {\n    pathname: href\n  } : href;\n}\nfunction serializeSearchParams(searchParams) {\n  function serializeValue(value) {\n    return String(value);\n  }\n  const urlSearchParams = new URLSearchParams();\n  for (const [key, value] of Object.entries(searchParams)) {\n    if (Array.isArray(value)) {\n      value.forEach(cur => {\n        urlSearchParams.append(key, serializeValue(cur));\n      });\n    } else {\n      urlSearchParams.set(key, serializeValue(value));\n    }\n  }\n  return '?' + urlSearchParams.toString();\n}\nfunction compileLocalizedPathname({\n  pathname,\n  locale,\n  params,\n  pathnames,\n  query\n}) {\n  function compilePath(value) {\n    const pathnameConfig = pathnames[value];\n    let compiled;\n    if (pathnameConfig) {\n      const template = getLocalizedTemplate(pathnameConfig, locale, value);\n      compiled = template;\n      if (params) {\n        Object.entries(params).forEach(([key, paramValue]) => {\n          let regexp, replacer;\n          if (Array.isArray(paramValue)) {\n            regexp = `(\\\\[)?\\\\[...${key}\\\\](\\\\])?`;\n            replacer = paramValue.map(v => String(v)).join('/');\n          } else {\n            regexp = `\\\\[${key}\\\\]`;\n            replacer = String(paramValue);\n          }\n          compiled = compiled.replace(new RegExp(regexp, 'g'), replacer);\n        });\n      }\n\n      // Clean up optional catch-all segments that were not replaced\n      compiled = compiled.replace(/\\[\\[\\.\\.\\..+\\]\\]/g, '');\n      if (compiled.includes('[')) {\n        // Next.js throws anyway, therefore better provide a more helpful error message\n        throw new Error(`Insufficient params provided for localized pathname.\\nTemplate: ${template}\\nParams: ${JSON.stringify(params)}`);\n      }\n      compiled = encodePathname(compiled);\n    } else {\n      // Unknown pathnames\n      compiled = value;\n    }\n    compiled = normalizeTrailingSlash(compiled);\n    if (query) {\n      // This also encodes non-ASCII characters by\n      // using `new URLSearchParams()` internally\n      compiled += serializeSearchParams(query);\n    }\n    return compiled;\n  }\n  if (typeof pathname === 'string') {\n    return compilePath(pathname);\n  } else {\n    const {\n      pathname: internalPathname,\n      ...rest\n    } = pathname;\n    const compiled = compilePath(internalPathname);\n    const result = {\n      ...rest,\n      pathname: compiled\n    };\n    return result;\n  }\n}\nfunction encodePathname(pathname) {\n  // Generally, to comply with RFC 3986 and Google's best practices for URL structures\n  // (https://developers.google.com/search/docs/crawling-indexing/url-structure),\n  // we should always encode non-ASCII characters.\n  //\n  // There are two places where next-intl interacts with potentially non-ASCII URLs:\n  // 1. Middleware: When mapping a localized pathname to a non-localized pathname internally\n  // 2. Navigation APIs: When generating a URLs to be used for <Link /> & friends\n  //\n  // Next.js normalizes incoming pathnames to always be encoded, therefore we can safely\n  // decode them there (see middleware.tsx). On the other hand, Next.js doesn't consistently\n  // encode non-ASCII characters that are passed to navigation APIs:\n  // 1. <Link /> doesn't encode non-ASCII characters\n  // 2. useRouter() uses `new URL()` internally, which will encode—but only if necessary\n  // 3. redirect() uses useRouter() on the client, but on the server side only\n  //    assigns the location header without encoding.\n  //\n  // In addition to this, for getPathname() we need to encode non-ASCII characters.\n  //\n  // Therefore, the bottom line is that next-intl should take care of encoding non-ASCII\n  // characters in all cases, but can rely on `new URL()` to not double-encode characters.\n  return pathname.split('/').map(segment => encodeURIComponent(segment)).join('/');\n}\nfunction getRoute(locale, pathname, pathnames) {\n  const sortedPathnames = getSortedPathnames(Object.keys(pathnames));\n  const decoded = decodeURI(pathname);\n  for (const internalPathname of sortedPathnames) {\n    const localizedPathnamesOrPathname = pathnames[internalPathname];\n    if (typeof localizedPathnamesOrPathname === 'string') {\n      const localizedPathname = localizedPathnamesOrPathname;\n      if (matchesPathname(localizedPathname, decoded)) {\n        return internalPathname;\n      }\n    } else {\n      if (matchesPathname(getLocalizedTemplate(localizedPathnamesOrPathname, locale, internalPathname), decoded)) {\n        return internalPathname;\n      }\n    }\n  }\n  return pathname;\n}\nfunction getBasePath(pathname, windowPathname = window.location.pathname) {\n  if (pathname === '/') {\n    return windowPathname;\n  } else {\n    return windowPathname.replace(pathname, '');\n  }\n}\nfunction applyPathnamePrefix(pathname, locale, routing, force) {\n  const {\n    mode\n  } = routing.localePrefix;\n  let shouldPrefix;\n  if (force !== undefined) {\n    shouldPrefix = force;\n  } else if (isLocalizableHref(pathname)) {\n    if (mode === 'always') {\n      shouldPrefix = true;\n    } else if (mode === 'as-needed') {\n      shouldPrefix = routing.domains ?\n      // Since locales are unique per domain, any locale that is a\n      // default locale of a domain doesn't require a prefix\n      !routing.domains.some(cur => cur.defaultLocale === locale) : locale !== routing.defaultLocale;\n    }\n  }\n  return shouldPrefix ? prefixPathname(getLocalePrefix(locale, routing.localePrefix), pathname) : pathname;\n}\nfunction validateReceivedConfig(config) {\n  if (config.localePrefix?.mode === 'as-needed' && !('defaultLocale' in config)) {\n    throw new Error(\"`localePrefix: 'as-needed' requires a `defaultLocale`.\");\n  }\n}\n\nexport { applyPathnamePrefix, compileLocalizedPathname, getBasePath, getRoute, normalizeNameOrNameWithParams, serializeSearchParams, validateReceivedConfig };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;;AAEA,2DAA2D;AAC3D,8CAA8C;AAE9C,aAAa;AAEb,2EAA2E;AAE3E,SAAS,8BAA8B,IAAI;IACzC,OAAO,OAAO,SAAS,WAAW;QAChC,UAAU;IACZ,IAAI;AACN;AACA,SAAS,sBAAsB,YAAY;IACzC,SAAS,eAAe,KAAK;QAC3B,OAAO,OAAO;IAChB;IACA,MAAM,kBAAkB,IAAI;IAC5B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,cAAe;QACvD,IAAI,MAAM,OAAO,CAAC,QAAQ;YACxB,MAAM,OAAO,CAAC,CAAA;gBACZ,gBAAgB,MAAM,CAAC,KAAK,eAAe;YAC7C;QACF,OAAO;YACL,gBAAgB,GAAG,CAAC,KAAK,eAAe;QAC1C;IACF;IACA,OAAO,MAAM,gBAAgB,QAAQ;AACvC;AACA,SAAS,yBAAyB,EAChC,QAAQ,EACR,MAAM,EACN,MAAM,EACN,SAAS,EACT,KAAK,EACN;IACC,SAAS,YAAY,KAAK;QACxB,MAAM,iBAAiB,SAAS,CAAC,MAAM;QACvC,IAAI;QACJ,IAAI,gBAAgB;YAClB,MAAM,WAAW,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,QAAQ;YAC9D,WAAW;YACX,IAAI,QAAQ;gBACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,WAAW;oBAC/C,IAAI,QAAQ;oBACZ,IAAI,MAAM,OAAO,CAAC,aAAa;wBAC7B,SAAS,CAAC,YAAY,EAAE,IAAI,SAAS,CAAC;wBACtC,WAAW,WAAW,GAAG,CAAC,CAAA,IAAK,OAAO,IAAI,IAAI,CAAC;oBACjD,OAAO;wBACL,SAAS,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC;wBACvB,WAAW,OAAO;oBACpB;oBACA,WAAW,SAAS,OAAO,CAAC,IAAI,OAAO,QAAQ,MAAM;gBACvD;YACF;YAEA,8DAA8D;YAC9D,WAAW,SAAS,OAAO,CAAC,qBAAqB;YACjD,IAAI,SAAS,QAAQ,CAAC,MAAM;gBAC1B,+EAA+E;gBAC/E,MAAM,IAAI,MAAM,CAAC,gEAAgE,EAAE,SAAS,UAAU,EAAE,KAAK,SAAS,CAAC,SAAS;YAClI;YACA,WAAW,eAAe;QAC5B,OAAO;YACL,oBAAoB;YACpB,WAAW;QACb;QACA,WAAW,CAAA,GAAA,qLAAA,CAAA,yBAAsB,AAAD,EAAE;QAClC,IAAI,OAAO;YACT,4CAA4C;YAC5C,2CAA2C;YAC3C,YAAY,sBAAsB;QACpC;QACA,OAAO;IACT;IACA,IAAI,OAAO,aAAa,UAAU;QAChC,OAAO,YAAY;IACrB,OAAO;QACL,MAAM,EACJ,UAAU,gBAAgB,EAC1B,GAAG,MACJ,GAAG;QACJ,MAAM,WAAW,YAAY;QAC7B,MAAM,SAAS;YACb,GAAG,IAAI;YACP,UAAU;QACZ;QACA,OAAO;IACT;AACF;AACA,SAAS,eAAe,QAAQ;IAC9B,oFAAoF;IACpF,+EAA+E;IAC/E,gDAAgD;IAChD,EAAE;IACF,kFAAkF;IAClF,0FAA0F;IAC1F,+EAA+E;IAC/E,EAAE;IACF,sFAAsF;IACtF,0FAA0F;IAC1F,kEAAkE;IAClE,kDAAkD;IAClD,sFAAsF;IACtF,4EAA4E;IAC5E,mDAAmD;IACnD,EAAE;IACF,iFAAiF;IACjF,EAAE;IACF,sFAAsF;IACtF,wFAAwF;IACxF,OAAO,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,UAAW,mBAAmB,UAAU,IAAI,CAAC;AAC9E;AACA,SAAS,SAAS,MAAM,EAAE,QAAQ,EAAE,SAAS;IAC3C,MAAM,kBAAkB,CAAA,GAAA,qLAAA,CAAA,qBAAkB,AAAD,EAAE,OAAO,IAAI,CAAC;IACvD,MAAM,UAAU,UAAU;IAC1B,KAAK,MAAM,oBAAoB,gBAAiB;QAC9C,MAAM,+BAA+B,SAAS,CAAC,iBAAiB;QAChE,IAAI,OAAO,iCAAiC,UAAU;YACpD,MAAM,oBAAoB;YAC1B,IAAI,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,mBAAmB,UAAU;gBAC/C,OAAO;YACT;QACF,OAAO;YACL,IAAI,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,CAAA,GAAA,qLAAA,CAAA,uBAAoB,AAAD,EAAE,8BAA8B,QAAQ,mBAAmB,UAAU;gBAC1G,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,YAAY,QAAQ,EAAE,iBAAiB,OAAO,QAAQ,CAAC,QAAQ;IACtE,IAAI,aAAa,KAAK;QACpB,OAAO;IACT,OAAO;QACL,OAAO,eAAe,OAAO,CAAC,UAAU;IAC1C;AACF;AACA,SAAS,oBAAoB,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;IAC3D,MAAM,EACJ,IAAI,EACL,GAAG,QAAQ,YAAY;IACxB,IAAI;IACJ,IAAI,UAAU,WAAW;QACvB,eAAe;IACjB,OAAO,IAAI,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;QACtC,IAAI,SAAS,UAAU;YACrB,eAAe;QACjB,OAAO,IAAI,SAAS,aAAa;YAC/B,eAAe,QAAQ,OAAO,GAC9B,4DAA4D;YAC5D,sDAAsD;YACtD,CAAC,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,aAAa,KAAK,UAAU,WAAW,QAAQ,aAAa;QAC/F;IACF;IACA,OAAO,eAAe,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,GAAA,qLAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,QAAQ,YAAY,GAAG,YAAY;AAClG;AACA,SAAS,uBAAuB,MAAM;IACpC,IAAI,OAAO,YAAY,EAAE,SAAS,eAAe,CAAC,CAAC,mBAAmB,MAAM,GAAG;QAC7E,MAAM,IAAI,MAAM;IAClB;AACF", "ignoreList": [0]}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/navigation/shared/createSharedNavigationFns.js"], "sourcesContent": ["import { redirect, permanentRedirect } from 'next/navigation';\nimport { forwardRef } from 'react';\nimport { receiveRoutingConfig } from '../../routing/config.js';\nimport use from '../../shared/use.js';\nimport { isLocalizableHref, isPromise } from '../../shared/utils.js';\nimport BaseLink from './BaseLink.js';\nimport { validateReceivedConfig, serializeSearchParams, compileLocalizedPathname, applyPathnamePrefix, normalizeNameOrNameWithParams } from './utils.js';\nimport { jsx } from 'react/jsx-runtime';\n\n/**\n * Shared implementations for `react-server` and `react-client`\n */\nfunction createSharedNavigationFns(getLocale, routing) {\n  const config = receiveRoutingConfig(routing || {});\n  {\n    validateReceivedConfig(config);\n  }\n  const pathnames = config.pathnames;\n  function Link({\n    href,\n    locale,\n    ...rest\n  }, ref) {\n    let pathname, params;\n    if (typeof href === 'object') {\n      pathname = href.pathname;\n      // @ts-expect-error -- This is ok\n      params = href.params;\n    } else {\n      pathname = href;\n    }\n\n    // @ts-expect-error -- This is ok\n    const isLocalizable = isLocalizableHref(href);\n    const localePromiseOrValue = getLocale();\n    const curLocale = isPromise(localePromiseOrValue) ? use(localePromiseOrValue) : localePromiseOrValue;\n    const finalPathname = isLocalizable ? getPathname({\n      locale: locale || curLocale,\n      // @ts-expect-error -- This is ok\n      href: pathnames == null ? pathname : {\n        pathname,\n        params\n      },\n      // Always include a prefix when changing locales\n      forcePrefix: locale != null || undefined\n    }) : pathname;\n    return /*#__PURE__*/jsx(BaseLink, {\n      ref: ref\n      // @ts-expect-error -- This is ok\n      ,\n      href: typeof href === 'object' ? {\n        ...href,\n        pathname: finalPathname\n      } : finalPathname,\n      locale: locale,\n      localeCookie: config.localeCookie,\n      ...rest\n    });\n  }\n  const LinkWithRef = /*#__PURE__*/forwardRef(Link);\n  function getPathname(args) {\n    const {\n      forcePrefix,\n      href,\n      locale\n    } = args;\n    let pathname;\n    if (pathnames == null) {\n      if (typeof href === 'object') {\n        pathname = href.pathname;\n        if (href.query) {\n          pathname += serializeSearchParams(href.query);\n        }\n      } else {\n        pathname = href;\n      }\n    } else {\n      pathname = compileLocalizedPathname({\n        locale,\n        // @ts-expect-error -- This is ok\n        ...normalizeNameOrNameWithParams(href),\n        // @ts-expect-error -- This is ok\n        pathnames: config.pathnames\n      });\n    }\n    return applyPathnamePrefix(pathname, locale, config, forcePrefix);\n  }\n  function getRedirectFn(fn) {\n    /** @see https://next-intl.dev/docs/routing/navigation#redirect */\n    return function redirectFn(args, ...rest) {\n      return fn(getPathname(args), ...rest);\n    };\n  }\n  const redirect$1 = getRedirectFn(redirect);\n  const permanentRedirect$1 = getRedirectFn(permanentRedirect);\n  return {\n    config,\n    Link: LinkWithRef,\n    redirect: redirect$1,\n    permanentRedirect: permanentRedirect$1,\n    getPathname\n  };\n}\n\nexport { createSharedNavigationFns as default };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA;;CAEC,GACD,SAAS,0BAA0B,SAAS,EAAE,OAAO;IACnD,MAAM,SAAS,CAAA,GAAA,uLAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,CAAC;IAChD;QACE,CAAA,GAAA,mMAAA,CAAA,yBAAsB,AAAD,EAAE;IACzB;IACA,MAAM,YAAY,OAAO,SAAS;IAClC,SAAS,KAAK,EACZ,IAAI,EACJ,MAAM,EACN,GAAG,MACJ,EAAE,GAAG;QACJ,IAAI,UAAU;QACd,IAAI,OAAO,SAAS,UAAU;YAC5B,WAAW,KAAK,QAAQ;YACxB,iCAAiC;YACjC,SAAS,KAAK,MAAM;QACtB,OAAO;YACL,WAAW;QACb;QAEA,iCAAiC;QACjC,MAAM,gBAAgB,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE;QACxC,MAAM,uBAAuB;QAC7B,MAAM,YAAY,CAAA,GAAA,qLAAA,CAAA,YAAS,AAAD,EAAE,wBAAwB,CAAA,GAAA,mLAAA,CAAA,UAAG,AAAD,EAAE,wBAAwB;QAChF,MAAM,gBAAgB,gBAAgB,YAAY;YAChD,QAAQ,UAAU;YAClB,iCAAiC;YACjC,MAAM,aAAa,OAAO,WAAW;gBACnC;gBACA;YACF;YACA,gDAAgD;YAChD,aAAa,UAAU,QAAQ;QACjC,KAAK;QACL,OAAO,WAAW,GAAE,CAAA,GAAA,8LAAA,CAAA,MAAG,AAAD,EAAE,sMAAA,CAAA,UAAQ,EAAE;YAChC,KAAK;YAGL,MAAM,OAAO,SAAS,WAAW;gBAC/B,GAAG,IAAI;gBACP,UAAU;YACZ,IAAI;YACJ,QAAQ;YACR,cAAc,OAAO,YAAY;YACjC,GAAG,IAAI;QACT;IACF;IACA,MAAM,cAAc,WAAW,GAAE,CAAA,GAAA,qLAAA,CAAA,aAAU,AAAD,EAAE;IAC5C,SAAS,YAAY,IAAI;QACvB,MAAM,EACJ,WAAW,EACX,IAAI,EACJ,MAAM,EACP,GAAG;QACJ,IAAI;QACJ,IAAI,aAAa,MAAM;YACrB,IAAI,OAAO,SAAS,UAAU;gBAC5B,WAAW,KAAK,QAAQ;gBACxB,IAAI,KAAK,KAAK,EAAE;oBACd,YAAY,CAAA,GAAA,mMAAA,CAAA,wBAAqB,AAAD,EAAE,KAAK,KAAK;gBAC9C;YACF,OAAO;gBACL,WAAW;YACb;QACF,OAAO;YACL,WAAW,CAAA,GAAA,mMAAA,CAAA,2BAAwB,AAAD,EAAE;gBAClC;gBACA,iCAAiC;gBACjC,GAAG,CAAA,GAAA,mMAAA,CAAA,gCAA6B,AAAD,EAAE,KAAK;gBACtC,iCAAiC;gBACjC,WAAW,OAAO,SAAS;YAC7B;QACF;QACA,OAAO,CAAA,GAAA,mMAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,QAAQ,QAAQ;IACvD;IACA,SAAS,cAAc,EAAE;QACvB,gEAAgE,GAChE,OAAO,SAAS,WAAW,IAAI,EAAE,GAAG,IAAI;YACtC,OAAO,GAAG,YAAY,UAAU;QAClC;IACF;IACA,MAAM,aAAa,cAAc,+KAAA,CAAA,WAAQ;IACzC,MAAM,sBAAsB,cAAc,+KAAA,CAAA,oBAAiB;IAC3D,OAAO;QACL;QACA,MAAM;QACN,UAAU;QACV,mBAAmB;QACnB;IACF;AACF", "ignoreList": [0]}}, {"offset": {"line": 1197, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/server/react-server/RequestLocaleCache.js"], "sourcesContent": ["import { cache } from 'react';\n\n// See https://github.com/vercel/next.js/discussions/58862\nfunction getCacheImpl() {\n  const value = {\n    locale: undefined\n  };\n  return value;\n}\nconst getCache = cache(getCacheImpl);\nfunction getCachedRequestLocale() {\n  return getCache().locale;\n}\nfunction setCachedRequestLocale(locale) {\n  getCache().locale = locale;\n}\n\nexport { getCachedRequestLocale, setCachedRequestLocale };\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,0DAA0D;AAC1D,SAAS;IACP,MAAM,QAAQ;QACZ,QAAQ;IACV;IACA,OAAO;AACT;AACA,MAAM,WAAW,CAAA,GAAA,qLAAA,CAAA,QAAK,AAAD,EAAE;AACvB,SAAS;IACP,OAAO,WAAW,MAAM;AAC1B;AACA,SAAS,uBAAuB,MAAM;IACpC,WAAW,MAAM,GAAG;AACtB", "ignoreList": [0]}}, {"offset": {"line": 1224, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/server/react-server/RequestLocale.js"], "sourcesContent": ["import { headers } from 'next/headers';\nimport { cache } from 'react';\nimport { HEADER_LOCALE_NAME } from '../../shared/constants.js';\nimport { isPromise } from '../../shared/utils.js';\nimport { getCachedRequestLocale } from './RequestLocaleCache.js';\n\nasync function getHeadersImpl() {\n  const promiseOrValue = headers();\n\n  // Compatibility with Next.js <15\n  return isPromise(promiseOrValue) ? await promiseOrValue : promiseOrValue;\n}\nconst getHeaders = cache(getHeadersImpl);\nasync function getLocaleFromHeaderImpl() {\n  let locale;\n  try {\n    locale = (await getHeaders()).get(HEADER_LOCALE_NAME) || undefined;\n  } catch (error) {\n    if (error instanceof Error && error.digest === 'DYNAMIC_SERVER_USAGE') {\n      const wrappedError = new Error('Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering', {\n        cause: error\n      });\n      wrappedError.digest = error.digest;\n      throw wrappedError;\n    } else {\n      throw error;\n    }\n  }\n  return locale;\n}\nconst getLocaleFromHeader = cache(getLocaleFromHeaderImpl);\nasync function getRequestLocale() {\n  return getCachedRequestLocale() || (await getLocaleFromHeader());\n}\n\nexport { getRequestLocale };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,eAAe;IACb,MAAM,iBAAiB,CAAA,GAAA,2KAAA,CAAA,UAAO,AAAD;IAE7B,iCAAiC;IACjC,OAAO,CAAA,GAAA,qLAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,MAAM,iBAAiB;AAC5D;AACA,MAAM,aAAa,CAAA,GAAA,qLAAA,CAAA,QAAK,AAAD,EAAE;AACzB,eAAe;IACb,IAAI;IACJ,IAAI;QACF,SAAS,CAAC,MAAM,YAAY,EAAE,GAAG,CAAC,yLAAA,CAAA,qBAAkB,KAAK;IAC3D,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,SAAS,MAAM,MAAM,KAAK,wBAAwB;YACrE,MAAM,eAAe,IAAI,MAAM,6TAA6T;gBAC1V,OAAO;YACT;YACA,aAAa,MAAM,GAAG,MAAM,MAAM;YAClC,MAAM;QACR,OAAO;YACL,MAAM;QACR;IACF;IACA,OAAO;AACT;AACA,MAAM,sBAAsB,CAAA,GAAA,qLAAA,CAAA,QAAK,AAAD,EAAE;AAClC,eAAe;IACb,OAAO,CAAA,GAAA,qNAAA,CAAA,yBAAsB,AAAD,OAAQ,MAAM;AAC5C", "ignoreList": [0]}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/server/react-server/validateLocale.js"], "sourcesContent": ["function validateLocale(locale) {\n  try {\n    const constructed = new Intl.Locale(locale);\n    if (!constructed.language) {\n      throw new Error('Language is required');\n    }\n  } catch {\n    console.error(`An invalid locale was provided: \"${locale}\"\\nPlease ensure you're using a valid Unicode locale identifier (e.g. \"en-US\").`);\n  }\n}\n\nexport { validateLocale as default };\n"], "names": [], "mappings": ";;;AAAA,SAAS,eAAe,MAAM;IAC5B,IAAI;QACF,MAAM,cAAc,IAAI,KAAK,MAAM,CAAC;QACpC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAM;QACN,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,OAAO,+EAA+E,CAAC;IAC3I;AACF", "ignoreList": [0]}}, {"offset": {"line": 1292, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/server/react-server/getConfig.js"], "sourcesContent": ["import { cache } from 'react';\nimport { initializeConfig, _createIntlFormatters, _createCache } from 'use-intl/core';\nimport { isPromise } from '../../shared/utils.js';\nimport { getRequestLocale } from './RequestLocale.js';\nimport getRuntimeConfig from 'next-intl/config';\nimport validateLocale from './validateLocale.js';\n\n// This is automatically inherited by `NextIntlClientProvider` if\n// the component is rendered from a Server Component\nfunction getDefaultTimeZoneImpl() {\n  return Intl.DateTimeFormat().resolvedOptions().timeZone;\n}\nconst getDefaultTimeZone = cache(getDefaultTimeZoneImpl);\nasync function receiveRuntimeConfigImpl(getConfig, localeOverride) {\n  if (typeof getConfig !== 'function') {\n    throw new Error(`Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n`);\n  }\n  const params = {\n    locale: localeOverride,\n    // In case the consumer doesn't read `params.locale` and instead provides the\n    // `locale` (either in a single-language workflow or because the locale is\n    // read from the user settings), don't attempt to read the request locale.\n    get requestLocale() {\n      return localeOverride ? Promise.resolve(localeOverride) : getRequestLocale();\n    }\n  };\n  let result = getConfig(params);\n  if (isPromise(result)) {\n    result = await result;\n  }\n  if (!result.locale) {\n    throw new Error('No locale was returned from `getRequestConfig`.\\n\\nSee https://next-intl.dev/docs/usage/configuration#i18n-request');\n  }\n  {\n    validateLocale(result.locale);\n  }\n  return result;\n}\nconst receiveRuntimeConfig = cache(receiveRuntimeConfigImpl);\nconst getFormatters = cache(_createIntlFormatters);\nconst getCache = cache(_createCache);\nasync function getConfigImpl(localeOverride) {\n  const runtimeConfig = await receiveRuntimeConfig(getRuntimeConfig, localeOverride);\n  return {\n    ...initializeConfig(runtimeConfig),\n    _formatters: getFormatters(getCache()),\n    timeZone: runtimeConfig.timeZone || getDefaultTimeZone()\n  };\n}\nconst getConfig = cache(getConfigImpl);\n\nexport { getConfig as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEA,iEAAiE;AACjE,oDAAoD;AACpD,SAAS;IACP,OAAO,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;AACzD;AACA,MAAM,qBAAqB,CAAA,GAAA,qLAAA,CAAA,QAAK,AAAD,EAAE;AACjC,eAAe,yBAAyB,SAAS,EAAE,cAAc;IAC/D,IAAI,OAAO,cAAc,YAAY;QACnC,MAAM,IAAI,MAAM,CAAC;;;;;;;AAOrB,CAAC;IACC;IACA,MAAM,SAAS;QACb,QAAQ;QACR,6EAA6E;QAC7E,0EAA0E;QAC1E,0EAA0E;QAC1E,IAAI,iBAAgB;YAClB,OAAO,iBAAiB,QAAQ,OAAO,CAAC,kBAAkB,CAAA,GAAA,gNAAA,CAAA,mBAAgB,AAAD;QAC3E;IACF;IACA,IAAI,SAAS,UAAU;IACvB,IAAI,CAAA,GAAA,qLAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACrB,SAAS,MAAM;IACjB;IACA,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IACA;QACE,CAAA,GAAA,iNAAA,CAAA,UAAc,AAAD,EAAE,OAAO,MAAM;IAC9B;IACA,OAAO;AACT;AACA,MAAM,uBAAuB,CAAA,GAAA,qLAAA,CAAA,QAAK,AAAD,EAAE;AACnC,MAAM,gBAAgB,CAAA,GAAA,qLAAA,CAAA,QAAK,AAAD,EAAE,kPAAA,CAAA,wBAAqB;AACjD,MAAM,WAAW,CAAA,GAAA,qLAAA,CAAA,QAAK,AAAD,EAAE,yOAAA,CAAA,eAAY;AACnC,eAAe,cAAc,cAAc;IACzC,MAAM,gBAAgB,MAAM,qBAAqB,8HAAA,CAAA,UAAgB,EAAE;IACnE,OAAO;QACL,GAAG,CAAA,GAAA,0OAAA,CAAA,mBAAgB,AAAD,EAAE,cAAc;QAClC,aAAa,cAAc;QAC3B,UAAU,cAAc,QAAQ,IAAI;IACtC;AACF;AACA,MAAM,YAAY,CAAA,GAAA,qLAAA,CAAA,QAAK,AAAD,EAAE", "ignoreList": [0]}}, {"offset": {"line": 1366, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/navigation/react-server/getServerLocale.js"], "sourcesContent": ["import getConfig from '../../server/react-server/getConfig.js';\n\n/**\n * This is only moved to a separate module for easier mocking in\n * `../createNavigatoin.test.tsx` in order to avoid suspending.\n */\nasync function getServerLocale() {\n  const config = await getConfig();\n  return config.locale;\n}\n\nexport { getServerLocale as default };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;CAGC,GACD,eAAe;IACb,MAAM,SAAS,MAAM,CAAA,GAAA,4MAAA,CAAA,UAAS,AAAD;IAC7B,OAAO,OAAO,MAAM;AACtB", "ignoreList": [0]}}, {"offset": {"line": 1385, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/next-intl/dist/esm/development/navigation/react-server/createNavigation.js"], "sourcesContent": ["import createSharedNavigationFns from '../shared/createSharedNavigationFns.js';\nimport getServerLocale from './getServerLocale.js';\n\nfunction createNavigation(routing) {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  const {\n    config,\n    ...fns\n  } = createSharedNavigationFns(getServerLocale, routing);\n  function notSupported(hookName) {\n    return () => {\n      throw new Error(`\\`${hookName}\\` is not supported in Server Components. You can use this hook if you convert the calling component to a Client Component.`);\n    };\n  }\n  return {\n    ...fns,\n    usePathname: notSupported('usePathname'),\n    useRouter: notSupported('useRouter')\n  };\n}\n\nexport { createNavigation as default };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,iBAAiB,OAAO;IAC/B,6DAA6D;IAC7D,MAAM,EACJ,MAAM,EACN,GAAG,KACJ,GAAG,CAAA,GAAA,uNAAA,CAAA,UAAyB,AAAD,EAAE,sNAAA,CAAA,UAAe,EAAE;IAC/C,SAAS,aAAa,QAAQ;QAC5B,OAAO;YACL,MAAM,IAAI,MAAM,CAAC,EAAE,EAAE,SAAS,2HAA2H,CAAC;QAC5J;IACF;IACA,OAAO;QACL,GAAG,GAAG;QACN,aAAa,aAAa;QAC1B,WAAW,aAAa;IAC1B;AACF", "ignoreList": [0]}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/tslib/tslib.es6.mjs"], "sourcesContent": ["/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;8EAa8E,GAC9E,8DAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9D,IAAI,gBAAgB,SAAS,CAAC,EAAE,CAAC;IAC/B,gBAAgB,OAAO,cAAc,IAChC,CAAA;QAAE,WAAW,EAAE;IAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;IAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;QAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IACpG,OAAO,cAAc,GAAG;AAC1B;AAEO,SAAS,UAAU,CAAC,EAAE,CAAC;IAC5B,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;IAC7D,cAAc,GAAG;IACjB,SAAS;QAAO,IAAI,CAAC,WAAW,GAAG;IAAG;IACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;AACrF;AAEO,IAAI,WAAW;IACpB,WAAW,OAAO,MAAM,IAAI,SAAS,SAAS,CAAC;QAC3C,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAChF;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAC9E,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QACpE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB;IACJ,OAAO;AACT;AAEO,SAAS,WAAW,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI;IACtD,IAAI,IAAI,UAAU,MAAM,EAAE,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,wBAAwB,CAAC,QAAQ,OAAO,MAAM;IAC3H,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,KAAK;SACpH,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK,IAAI,IAAI,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,QAAQ,KAAK,KAAK,EAAE,QAAQ,IAAI,KAAK;IAChJ,OAAO,IAAI,KAAK,KAAK,OAAO,cAAc,CAAC,QAAQ,KAAK,IAAI;AAC9D;AAEO,SAAS,QAAQ,UAAU,EAAE,SAAS;IAC3C,OAAO,SAAU,MAAM,EAAE,GAAG;QAAI,UAAU,QAAQ,KAAK;IAAa;AACtE;AAEO,SAAS,aAAa,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,iBAAiB;IACrG,SAAS,OAAO,CAAC;QAAI,IAAI,MAAM,KAAK,KAAK,OAAO,MAAM,YAAY,MAAM,IAAI,UAAU;QAAsB,OAAO;IAAG;IACtH,IAAI,OAAO,UAAU,IAAI,EAAE,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;IACzF,IAAI,SAAS,CAAC,gBAAgB,OAAO,SAAS,CAAC,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG;IACnF,IAAI,aAAa,gBAAgB,CAAC,SAAS,OAAO,wBAAwB,CAAC,QAAQ,UAAU,IAAI,IAAI,CAAC,CAAC;IACvG,IAAI,GAAG,OAAO;IACd,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC7C,IAAI,UAAU,CAAC;QACf,IAAK,IAAI,KAAK,UAAW,OAAO,CAAC,EAAE,GAAG,MAAM,WAAW,CAAC,IAAI,SAAS,CAAC,EAAE;QACxE,IAAK,IAAI,KAAK,UAAU,MAAM,CAAE,QAAQ,MAAM,CAAC,EAAE,GAAG,UAAU,MAAM,CAAC,EAAE;QACvE,QAAQ,cAAc,GAAG,SAAU,CAAC;YAAI,IAAI,MAAM,MAAM,IAAI,UAAU;YAA2D,kBAAkB,IAAI,CAAC,OAAO,KAAK;QAAQ;QAC5K,IAAI,SAAS,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,SAAS,aAAa;YAAE,KAAK,WAAW,GAAG;YAAE,KAAK,WAAW,GAAG;QAAC,IAAI,UAAU,CAAC,IAAI,EAAE;QACtH,IAAI,SAAS,YAAY;YACrB,IAAI,WAAW,KAAK,GAAG;YACvB,IAAI,WAAW,QAAQ,OAAO,WAAW,UAAU,MAAM,IAAI,UAAU;YACvE,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,IAAI,GAAG,aAAa,OAAO,CAAC;QACtD,OACK,IAAI,IAAI,OAAO,SAAS;YACzB,IAAI,SAAS,SAAS,aAAa,OAAO,CAAC;iBACtC,UAAU,CAAC,IAAI,GAAG;QAC3B;IACJ;IACA,IAAI,QAAQ,OAAO,cAAc,CAAC,QAAQ,UAAU,IAAI,EAAE;IAC1D,OAAO;AACT;;AAEO,SAAS,kBAAkB,OAAO,EAAE,YAAY,EAAE,KAAK;IAC5D,IAAI,WAAW,UAAU,MAAM,GAAG;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC1C,QAAQ,WAAW,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,SAAS,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;IACnF;IACA,OAAO,WAAW,QAAQ,KAAK;AACjC;;AAEO,SAAS,UAAU,CAAC;IACzB,OAAO,OAAO,MAAM,WAAW,IAAI,GAAG,MAAM,CAAC;AAC/C;;AAEO,SAAS,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM;IAC/C,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,WAAW,GAAG,IAAI,MAAM,CAAC,KAAK,WAAW,EAAE,OAAO;IAC5F,OAAO,OAAO,cAAc,CAAC,GAAG,QAAQ;QAAE,cAAc;QAAM,OAAO,SAAS,GAAG,MAAM,CAAC,QAAQ,KAAK,QAAQ;IAAK;AACpH;;AAEO,SAAS,WAAW,WAAW,EAAE,aAAa;IACnD,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,OAAO,QAAQ,QAAQ,CAAC,aAAa;AAClH;AAEO,SAAS,UAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACzD,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACF;AAEO,SAAS,YAAY,OAAO,EAAE,IAAI;IACvC,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,aAAa,aAAa,WAAW,MAAM,EAAE,SAAS;IAC/L,OAAO,EAAE,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,KAAK,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;IAC1J,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,IAAI;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACF;AAEO,IAAI,kBAAkB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IAChE,IAAI,OAAO,WAAW,KAAK;IAC3B,IAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG;IAC9C,IAAI,CAAC,QAAQ,CAAC,SAAS,OAAO,CAAC,EAAE,UAAU,GAAG,KAAK,QAAQ,IAAI,KAAK,YAAY,GAAG;QAC/E,OAAO;YAAE,YAAY;YAAM,KAAK;gBAAa,OAAO,CAAC,CAAC,EAAE;YAAE;QAAE;IAChE;IACA,OAAO,cAAc,CAAC,GAAG,IAAI;AAC/B,IAAM,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IACxB,IAAI,OAAO,WAAW,KAAK;IAC3B,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;AACd;AAEO,SAAS,aAAa,CAAC,EAAE,CAAC;IAC/B,IAAK,IAAI,KAAK,EAAG,IAAI,MAAM,aAAa,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,gBAAgB,GAAG,GAAG;AAC7G;AAEO,SAAS,SAAS,CAAC;IACxB,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACtD;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO,QAAQ,CAAC;IAC1D,IAAI,CAAC,GAAG,OAAO;IACf,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,EAAE;IAC/B,IAAI;QACA,MAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAE,GAAG,IAAI,CAAC,EAAE,KAAK;IAC7E,EACA,OAAO,OAAO;QAAE,IAAI;YAAE,OAAO;QAAM;IAAG,SAC9B;QACJ,IAAI;YACA,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,IAAI,CAAC;QAClD,SACQ;YAAE,IAAI,GAAG,MAAM,EAAE,KAAK;QAAE;IACpC;IACA,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,KAAK,EAAE,EAAE,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAC3C,KAAK,GAAG,MAAM,CAAC,OAAO,SAAS,CAAC,EAAE;IACtC,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,MAAM,EAAE,IAAI,IAAI,IAAK,KAAK,SAAS,CAAC,EAAE,CAAC,MAAM;IACnF,IAAK,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IACzC,IAAK,IAAI,IAAI,SAAS,CAAC,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,KAAK,IAC1D,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnB,OAAO;AACT;AAEO,SAAS,cAAc,EAAE,EAAE,IAAI,EAAE,IAAI;IAC1C,IAAI,QAAQ,UAAU,MAAM,KAAK,GAAG,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,GAAG,IAAK;QACjF,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG;YACpB,IAAI,CAAC,IAAI,KAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;YAClD,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACnB;IACJ;IACA,OAAO,GAAG,MAAM,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACpD;AAEO,SAAS,QAAQ,CAAC;IACvB,OAAO,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAQ;AACpE;AAEO,SAAS,iBAAiB,OAAO,EAAE,UAAU,EAAE,SAAS;IAC7D,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,GAAG,GAAG,IAAI,EAAE;IAC7D,OAAO,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,kBAAkB,aAAa,gBAAgB,MAAM,EAAE,SAAS,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,cAAc,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IACtN,SAAS,YAAY,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;QAAS;IAAG;IAC9F,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,IAAI,CAAC,CAAC,EAAE,EAAE;YAAE,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;gBAAI,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;oBAAI,EAAE,IAAI,CAAC;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,IAAI,KAAK,OAAO,GAAG;gBAAI;YAAI;YAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QAAG;IAAE;IACvK,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAAK,EAAE,OAAO,GAAG;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAAI;IAAE;IACjF,SAAS,KAAK,CAAC;QAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAAI;IACvH,SAAS,QAAQ,KAAK;QAAI,OAAO,QAAQ;IAAQ;IACjD,SAAS,OAAO,KAAK;QAAI,OAAO,SAAS;IAAQ;IACjD,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAAG;AACnF;AAEO,SAAS,iBAAiB,CAAC;IAChC,IAAI,GAAG;IACP,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,SAAS,SAAU,CAAC;QAAI,MAAM;IAAG,IAAI,KAAK,WAAW,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IAC1I,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;YAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI;gBAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC;gBAAK,MAAM;YAAM,IAAI,IAAI,EAAE,KAAK;QAAG,IAAI;IAAG;AACvI;AAEO,SAAS,cAAc,CAAC;IAC7B,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,EAAE;IACjC,OAAO,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,aAAa,aAAa,SAAS,KAAK,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,WAAW,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG,CAAC;;IAC/M,SAAS,KAAK,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,SAAU,CAAC;YAAI,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,OAAO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK;YAAG;QAAI;IAAG;IAC/J,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAAI,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YAAI,QAAQ;gBAAE,OAAO;gBAAG,MAAM;YAAE;QAAI,GAAG;IAAS;AAC7H;AAEO,SAAS,qBAAqB,MAAM,EAAE,GAAG;IAC9C,IAAI,OAAO,cAAc,EAAE;QAAE,OAAO,cAAc,CAAC,QAAQ,OAAO;YAAE,OAAO;QAAI;IAAI,OAAO;QAAE,OAAO,GAAG,GAAG;IAAK;IAC9G,OAAO;AACT;;AAEA,IAAI,qBAAqB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC;IACrD,OAAO,cAAc,CAAC,GAAG,WAAW;QAAE,YAAY;QAAM,OAAO;IAAE;AACnE,IAAK,SAAS,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,UAAU,GAAG;AACjB;AAEA,IAAI,UAAU,SAAS,CAAC;IACtB,UAAU,OAAO,mBAAmB,IAAI,SAAU,CAAC;QACjD,IAAI,KAAK,EAAE;QACX,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG;QACjF,OAAO;IACT;IACA,OAAO,QAAQ;AACjB;AAEO,SAAS,aAAa,GAAG;IAC9B,IAAI,OAAO,IAAI,UAAU,EAAE,OAAO;IAClC,IAAI,SAAS,CAAC;IACd,IAAI,OAAO,MAAM;QAAA,IAAK,IAAI,IAAI,QAAQ,MAAM,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,KAAK,WAAW,gBAAgB,QAAQ,KAAK,CAAC,CAAC,EAAE;IAAC;IAChI,mBAAmB,QAAQ;IAC3B,OAAO;AACT;AAEO,SAAS,gBAAgB,GAAG;IACjC,OAAO,AAAC,OAAO,IAAI,UAAU,GAAI,MAAM;QAAE,SAAS;IAAI;AACxD;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC7D,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACtF;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACpE,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACtG;AAEO,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IACnD,IAAI,aAAa,QAAS,OAAO,aAAa,YAAY,OAAO,aAAa,YAAa,MAAM,IAAI,UAAU;IAC/G,OAAO,OAAO,UAAU,aAAa,aAAa,QAAQ,MAAM,GAAG,CAAC;AACtE;AAEO,SAAS,wBAAwB,GAAG,EAAE,KAAK,EAAE,KAAK;IACvD,IAAI,UAAU,QAAQ,UAAU,KAAK,GAAG;QACtC,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,MAAM,IAAI,UAAU;QAClF,IAAI,SAAS;QACb,IAAI,OAAO;YACT,IAAI,CAAC,OAAO,YAAY,EAAE,MAAM,IAAI,UAAU;YAC9C,UAAU,KAAK,CAAC,OAAO,YAAY,CAAC;QACtC;QACA,IAAI,YAAY,KAAK,GAAG;YACtB,IAAI,CAAC,OAAO,OAAO,EAAE,MAAM,IAAI,UAAU;YACzC,UAAU,KAAK,CAAC,OAAO,OAAO,CAAC;YAC/B,IAAI,OAAO,QAAQ;QACrB;QACA,IAAI,OAAO,YAAY,YAAY,MAAM,IAAI,UAAU;QACvD,IAAI,OAAO,UAAU;YAAa,IAAI;gBAAE,MAAM,IAAI,CAAC,IAAI;YAAG,EAAE,OAAO,GAAG;gBAAE,OAAO,QAAQ,MAAM,CAAC;YAAI;QAAE;QACpG,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;YAAO,SAAS;YAAS,OAAO;QAAM;IAChE,OACK,IAAI,OAAO;QACd,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;QAAK;IAC/B;IACA,OAAO;AACT;AAEA,IAAI,mBAAmB,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,KAAK,EAAE,UAAU,EAAE,OAAO;IACnH,IAAI,IAAI,IAAI,MAAM;IAClB,OAAO,EAAE,IAAI,GAAG,mBAAmB,EAAE,KAAK,GAAG,OAAO,EAAE,UAAU,GAAG,YAAY;AACjF;AAEO,SAAS,mBAAmB,GAAG;IACpC,SAAS,KAAK,CAAC;QACb,IAAI,KAAK,GAAG,IAAI,QAAQ,GAAG,IAAI,iBAAiB,GAAG,IAAI,KAAK,EAAE,8CAA8C;QAC5G,IAAI,QAAQ,GAAG;IACjB;IACA,IAAI,GAAG,IAAI;IACX,SAAS;QACP,MAAO,IAAI,IAAI,KAAK,CAAC,GAAG,GAAI;YAC1B,IAAI;gBACF,IAAI,CAAC,EAAE,KAAK,IAAI,MAAM,GAAG,OAAO,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,QAAQ,OAAO,GAAG,IAAI,CAAC;gBACjF,IAAI,EAAE,OAAO,EAAE;oBACb,IAAI,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK;oBACnC,IAAI,EAAE,KAAK,EAAE,OAAO,KAAK,GAAG,QAAQ,OAAO,CAAC,QAAQ,IAAI,CAAC,MAAM,SAAS,CAAC;wBAAI,KAAK;wBAAI,OAAO;oBAAQ;gBACvG,OACK,KAAK;YACZ,EACA,OAAO,GAAG;gBACR,KAAK;YACP;QACF;QACA,IAAI,MAAM,GAAG,OAAO,IAAI,QAAQ,GAAG,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI,QAAQ,OAAO;QAC9E,IAAI,IAAI,QAAQ,EAAE,MAAM,IAAI,KAAK;IACnC;IACA,OAAO;AACT;AAEO,SAAS,iCAAiC,IAAI,EAAE,WAAW;IAChE,IAAI,OAAO,SAAS,YAAY,WAAW,IAAI,CAAC,OAAO;QACnD,OAAO,KAAK,OAAO,CAAC,oDAAoD,SAAU,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE;YAChG,OAAO,MAAM,cAAc,SAAS,QAAQ,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,IAAK,IAAI,MAAM,MAAM,GAAG,WAAW,KAAK;QAC7G;IACJ;IACA,OAAO;AACT;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0]}}, {"offset": {"line": 2022, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/negotiator/lib/charset.js"], "sourcesContent": ["/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredCharsets;\nmodule.exports.preferredCharsets = preferredCharsets;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleCharsetRegExp = /^\\s*([^\\s;]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Charset header.\n * @private\n */\n\nfunction parseAcceptCharset(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var charset = parseCharset(accepts[i].trim(), i);\n\n    if (charset) {\n      accepts[j++] = charset;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a charset from the Accept-Charset header.\n * @private\n */\n\nfunction parseCharset(str, i) {\n  var match = simpleCharsetRegExp.exec(str);\n  if (!match) return null;\n\n  var charset = match[1];\n  var q = 1;\n  if (match[2]) {\n    var params = match[2].split(';')\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].trim().split('=');\n      if (p[0] === 'q') {\n        q = parseFloat(p[1]);\n        break;\n      }\n    }\n  }\n\n  return {\n    charset: charset,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of a charset.\n * @private\n */\n\nfunction getCharsetPriority(charset, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(charset, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the charset.\n * @private\n */\n\nfunction specify(charset, spec, index) {\n  var s = 0;\n  if(spec.charset.toLowerCase() === charset.toLowerCase()){\n    s |= 1;\n  } else if (spec.charset !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n}\n\n/**\n * Get the preferred charsets from an Accept-Charset header.\n * @public\n */\n\nfunction preferredCharsets(accept, provided) {\n  // RFC 2616 sec 14.2: no header = *\n  var accepts = parseAcceptCharset(accept === undefined ? '*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all charsets\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullCharset);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getCharsetPriority(type, accepts, index);\n  });\n\n  // sorted list of accepted charsets\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getCharset(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full charset string.\n * @private\n */\n\nfunction getFullCharset(spec) {\n  return spec.charset;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;AAEA;;;CAGC,GAED,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,iBAAiB,GAAG;AAEnC;;;CAGC,GAED,IAAI,sBAAsB;AAE1B;;;CAGC,GAED,SAAS,mBAAmB,MAAM;IAChC,IAAI,UAAU,OAAO,KAAK,CAAC;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QAC9C,IAAI,UAAU,aAAa,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI;QAE9C,IAAI,SAAS;YACX,OAAO,CAAC,IAAI,GAAG;QACjB;IACF;IAEA,eAAe;IACf,QAAQ,MAAM,GAAG;IAEjB,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,aAAa,GAAG,EAAE,CAAC;IAC1B,IAAI,QAAQ,oBAAoB,IAAI,CAAC;IACrC,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,UAAU,KAAK,CAAC,EAAE;IACtB,IAAI,IAAI;IACR,IAAI,KAAK,CAAC,EAAE,EAAE;QACZ,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK;gBAChB,IAAI,WAAW,CAAC,CAAC,EAAE;gBACnB;YACF;QACF;IACF;IAEA,OAAO;QACL,SAAS;QACT,GAAG;QACH,GAAG;IACL;AACF;AAEA;;;CAGC,GAED,SAAS,mBAAmB,OAAO,EAAE,QAAQ,EAAE,KAAK;IAClD,IAAI,WAAW;QAAC,GAAG,CAAC;QAAG,GAAG;QAAG,GAAG;IAAC;IAEjC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,IAAI,OAAO,QAAQ,SAAS,QAAQ,CAAC,EAAE,EAAE;QAEzC,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG;YACnF,WAAW;QACb;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,QAAQ,OAAO,EAAE,IAAI,EAAE,KAAK;IACnC,IAAI,IAAI;IACR,IAAG,KAAK,OAAO,CAAC,WAAW,OAAO,QAAQ,WAAW,IAAG;QACtD,KAAK;IACP,OAAO,IAAI,KAAK,OAAO,KAAK,KAAM;QAChC,OAAO;IACT;IAEA,OAAO;QACL,GAAG;QACH,GAAG,KAAK,CAAC;QACT,GAAG,KAAK,CAAC;QACT,GAAG;IACL;AACF;AAEA;;;CAGC,GAED,SAAS,kBAAkB,MAAM,EAAE,QAAQ;IACzC,mCAAmC;IACnC,IAAI,UAAU,mBAAmB,WAAW,YAAY,MAAM,UAAU;IAExE,IAAI,CAAC,UAAU;QACb,8BAA8B;QAC9B,OAAO,QACJ,MAAM,CAAC,WACP,IAAI,CAAC,cACL,GAAG,CAAC;IACT;IAEA,IAAI,aAAa,SAAS,GAAG,CAAC,SAAS,YAAY,IAAI,EAAE,KAAK;QAC5D,OAAO,mBAAmB,MAAM,SAAS;IAC3C;IAEA,mCAAmC;IACnC,OAAO,WAAW,MAAM,CAAC,WAAW,IAAI,CAAC,cAAc,GAAG,CAAC,SAAS,WAAW,QAAQ;QACrF,OAAO,QAAQ,CAAC,WAAW,OAAO,CAAC,UAAU;IAC/C;AACF;AAEA;;;CAGC,GAED,SAAS,aAAa,CAAC,EAAE,CAAC;IACxB,OAAO,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAK;AACrE;AAEA;;;CAGC,GAED,SAAS,eAAe,IAAI;IAC1B,OAAO,KAAK,OAAO;AACrB;AAEA;;;CAGC,GAED,SAAS,UAAU,IAAI;IACrB,OAAO,KAAK,CAAC,GAAG;AAClB", "ignoreList": [0]}}, {"offset": {"line": 2152, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/negotiator/lib/encoding.js"], "sourcesContent": ["/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredEncodings;\nmodule.exports.preferredEncodings = preferredEncodings;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleEncodingRegExp = /^\\s*([^\\s;]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Encoding header.\n * @private\n */\n\nfunction parseAcceptEncoding(accept) {\n  var accepts = accept.split(',');\n  var hasIdentity = false;\n  var minQuality = 1;\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var encoding = parseEncoding(accepts[i].trim(), i);\n\n    if (encoding) {\n      accepts[j++] = encoding;\n      hasIdentity = hasIdentity || specify('identity', encoding);\n      minQuality = Math.min(minQuality, encoding.q || 1);\n    }\n  }\n\n  if (!hasIdentity) {\n    /*\n     * If identity doesn't explicitly appear in the accept-encoding header,\n     * it's added to the list of acceptable encoding with the lowest q\n     */\n    accepts[j++] = {\n      encoding: 'identity',\n      q: minQuality,\n      i: i\n    };\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse an encoding from the Accept-Encoding header.\n * @private\n */\n\nfunction parseEncoding(str, i) {\n  var match = simpleEncodingRegExp.exec(str);\n  if (!match) return null;\n\n  var encoding = match[1];\n  var q = 1;\n  if (match[2]) {\n    var params = match[2].split(';');\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].trim().split('=');\n      if (p[0] === 'q') {\n        q = parseFloat(p[1]);\n        break;\n      }\n    }\n  }\n\n  return {\n    encoding: encoding,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of an encoding.\n * @private\n */\n\nfunction getEncodingPriority(encoding, accepted, index) {\n  var priority = {encoding: encoding, o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(encoding, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the encoding.\n * @private\n */\n\nfunction specify(encoding, spec, index) {\n  var s = 0;\n  if(spec.encoding.toLowerCase() === encoding.toLowerCase()){\n    s |= 1;\n  } else if (spec.encoding !== '*' ) {\n    return null\n  }\n\n  return {\n    encoding: encoding,\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n};\n\n/**\n * Get the preferred encodings from an Accept-Encoding header.\n * @public\n */\n\nfunction preferredEncodings(accept, provided, preferred) {\n  var accepts = parseAcceptEncoding(accept || '');\n\n  var comparator = preferred ? function comparator (a, b) {\n    if (a.q !== b.q) {\n      return b.q - a.q // higher quality first\n    }\n\n    var aPreferred = preferred.indexOf(a.encoding)\n    var bPreferred = preferred.indexOf(b.encoding)\n\n    if (aPreferred === -1 && bPreferred === -1) {\n      // consider the original specifity/order\n      return (b.s - a.s) || (a.o - b.o) || (a.i - b.i)\n    }\n\n    if (aPreferred !== -1 && bPreferred !== -1) {\n      return aPreferred - bPreferred // consider the preferred order\n    }\n\n    return aPreferred === -1 ? 1 : -1 // preferred first\n  } : compareSpecs;\n\n  if (!provided) {\n    // sorted list of all encodings\n    return accepts\n      .filter(isQuality)\n      .sort(comparator)\n      .map(getFullEncoding);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getEncodingPriority(type, accepts, index);\n  });\n\n  // sorted list of accepted encodings\n  return priorities.filter(isQuality).sort(comparator).map(function getEncoding(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i);\n}\n\n/**\n * Get full encoding string.\n * @private\n */\n\nfunction getFullEncoding(spec) {\n  return spec.encoding;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;AAEA;;;CAGC,GAED,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,kBAAkB,GAAG;AAEpC;;;CAGC,GAED,IAAI,uBAAuB;AAE3B;;;CAGC,GAED,SAAS,oBAAoB,MAAM;IACjC,IAAI,UAAU,OAAO,KAAK,CAAC;IAC3B,IAAI,cAAc;IAClB,IAAI,aAAa;IAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QAC9C,IAAI,WAAW,cAAc,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI;QAEhD,IAAI,UAAU;YACZ,OAAO,CAAC,IAAI,GAAG;YACf,cAAc,eAAe,QAAQ,YAAY;YACjD,aAAa,KAAK,GAAG,CAAC,YAAY,SAAS,CAAC,IAAI;QAClD;IACF;IAEA,IAAI,CAAC,aAAa;QAChB;;;KAGC,GACD,OAAO,CAAC,IAAI,GAAG;YACb,UAAU;YACV,GAAG;YACH,GAAG;QACL;IACF;IAEA,eAAe;IACf,QAAQ,MAAM,GAAG;IAEjB,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,cAAc,GAAG,EAAE,CAAC;IAC3B,IAAI,QAAQ,qBAAqB,IAAI,CAAC;IACtC,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,WAAW,KAAK,CAAC,EAAE;IACvB,IAAI,IAAI;IACR,IAAI,KAAK,CAAC,EAAE,EAAE;QACZ,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK;gBAChB,IAAI,WAAW,CAAC,CAAC,EAAE;gBACnB;YACF;QACF;IACF;IAEA,OAAO;QACL,UAAU;QACV,GAAG;QACH,GAAG;IACL;AACF;AAEA;;;CAGC,GAED,SAAS,oBAAoB,QAAQ,EAAE,QAAQ,EAAE,KAAK;IACpD,IAAI,WAAW;QAAC,UAAU;QAAU,GAAG,CAAC;QAAG,GAAG;QAAG,GAAG;IAAC;IAErD,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,IAAI,OAAO,QAAQ,UAAU,QAAQ,CAAC,EAAE,EAAE;QAE1C,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG;YACnF,WAAW;QACb;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,QAAQ,QAAQ,EAAE,IAAI,EAAE,KAAK;IACpC,IAAI,IAAI;IACR,IAAG,KAAK,QAAQ,CAAC,WAAW,OAAO,SAAS,WAAW,IAAG;QACxD,KAAK;IACP,OAAO,IAAI,KAAK,QAAQ,KAAK,KAAM;QACjC,OAAO;IACT;IAEA,OAAO;QACL,UAAU;QACV,GAAG;QACH,GAAG,KAAK,CAAC;QACT,GAAG,KAAK,CAAC;QACT,GAAG;IACL;AACF;;AAEA;;;CAGC,GAED,SAAS,mBAAmB,MAAM,EAAE,QAAQ,EAAE,SAAS;IACrD,IAAI,UAAU,oBAAoB,UAAU;IAE5C,IAAI,aAAa,YAAY,SAAS,WAAY,CAAC,EAAE,CAAC;QACpD,IAAI,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE;YACf,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,uBAAuB;;QAC1C;QAEA,IAAI,aAAa,UAAU,OAAO,CAAC,EAAE,QAAQ;QAC7C,IAAI,aAAa,UAAU,OAAO,CAAC,EAAE,QAAQ;QAE7C,IAAI,eAAe,CAAC,KAAK,eAAe,CAAC,GAAG;YAC1C,wCAAwC;YACxC,OAAO,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC;QACjD;QAEA,IAAI,eAAe,CAAC,KAAK,eAAe,CAAC,GAAG;YAC1C,OAAO,aAAa,WAAW,+BAA+B;;QAChE;QAEA,OAAO,eAAe,CAAC,IAAI,IAAI,CAAC,EAAE,kBAAkB;;IACtD,IAAI;IAEJ,IAAI,CAAC,UAAU;QACb,+BAA+B;QAC/B,OAAO,QACJ,MAAM,CAAC,WACP,IAAI,CAAC,YACL,GAAG,CAAC;IACT;IAEA,IAAI,aAAa,SAAS,GAAG,CAAC,SAAS,YAAY,IAAI,EAAE,KAAK;QAC5D,OAAO,oBAAoB,MAAM,SAAS;IAC5C;IAEA,oCAAoC;IACpC,OAAO,WAAW,MAAM,CAAC,WAAW,IAAI,CAAC,YAAY,GAAG,CAAC,SAAS,YAAY,QAAQ;QACpF,OAAO,QAAQ,CAAC,WAAW,OAAO,CAAC,UAAU;IAC/C;AACF;AAEA;;;CAGC,GAED,SAAS,aAAa,CAAC,EAAE,CAAC;IACxB,OAAO,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC;AAChE;AAEA;;;CAGC,GAED,SAAS,gBAAgB,IAAI;IAC3B,OAAO,KAAK,QAAQ;AACtB;AAEA;;;CAGC,GAED,SAAS,UAAU,IAAI;IACrB,OAAO,KAAK,CAAC,GAAG;AAClB", "ignoreList": [0]}}, {"offset": {"line": 2316, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/negotiator/lib/language.js"], "sourcesContent": ["/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredLanguages;\nmodule.exports.preferredLanguages = preferredLanguages;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleLanguageRegExp = /^\\s*([^\\s\\-;]+)(?:-([^\\s;]+))?\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Language header.\n * @private\n */\n\nfunction parseAcceptLanguage(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var language = parseLanguage(accepts[i].trim(), i);\n\n    if (language) {\n      accepts[j++] = language;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a language from the Accept-Language header.\n * @private\n */\n\nfunction parseLanguage(str, i) {\n  var match = simpleLanguageRegExp.exec(str);\n  if (!match) return null;\n\n  var prefix = match[1]\n  var suffix = match[2]\n  var full = prefix\n\n  if (suffix) full += \"-\" + suffix;\n\n  var q = 1;\n  if (match[3]) {\n    var params = match[3].split(';')\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].split('=');\n      if (p[0] === 'q') q = parseFloat(p[1]);\n    }\n  }\n\n  return {\n    prefix: prefix,\n    suffix: suffix,\n    q: q,\n    i: i,\n    full: full\n  };\n}\n\n/**\n * Get the priority of a language.\n * @private\n */\n\nfunction getLanguagePriority(language, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(language, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the language.\n * @private\n */\n\nfunction specify(language, spec, index) {\n  var p = parseLanguage(language)\n  if (!p) return null;\n  var s = 0;\n  if(spec.full.toLowerCase() === p.full.toLowerCase()){\n    s |= 4;\n  } else if (spec.prefix.toLowerCase() === p.full.toLowerCase()) {\n    s |= 2;\n  } else if (spec.full.toLowerCase() === p.prefix.toLowerCase()) {\n    s |= 1;\n  } else if (spec.full !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n};\n\n/**\n * Get the preferred languages from an Accept-Language header.\n * @public\n */\n\nfunction preferredLanguages(accept, provided) {\n  // RFC 2616 sec 14.4: no header = *\n  var accepts = parseAcceptLanguage(accept === undefined ? '*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all languages\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullLanguage);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getLanguagePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted languages\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getLanguage(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full language string.\n * @private\n */\n\nfunction getFullLanguage(spec) {\n  return spec.full;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;AAEA;;;CAGC,GAED,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,kBAAkB,GAAG;AAEpC;;;CAGC,GAED,IAAI,uBAAuB;AAE3B;;;CAGC,GAED,SAAS,oBAAoB,MAAM;IACjC,IAAI,UAAU,OAAO,KAAK,CAAC;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QAC9C,IAAI,WAAW,cAAc,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI;QAEhD,IAAI,UAAU;YACZ,OAAO,CAAC,IAAI,GAAG;QACjB;IACF;IAEA,eAAe;IACf,QAAQ,MAAM,GAAG;IAEjB,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,cAAc,GAAG,EAAE,CAAC;IAC3B,IAAI,QAAQ,qBAAqB,IAAI,CAAC;IACtC,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,SAAS,KAAK,CAAC,EAAE;IACrB,IAAI,SAAS,KAAK,CAAC,EAAE;IACrB,IAAI,OAAO;IAEX,IAAI,QAAQ,QAAQ,MAAM;IAE1B,IAAI,IAAI;IACR,IAAI,KAAK,CAAC,EAAE,EAAE;QACZ,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACtC,IAAI,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;YACxB,IAAI,CAAC,CAAC,EAAE,KAAK,KAAK,IAAI,WAAW,CAAC,CAAC,EAAE;QACvC;IACF;IAEA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,GAAG;QACH,GAAG;QACH,MAAM;IACR;AACF;AAEA;;;CAGC,GAED,SAAS,oBAAoB,QAAQ,EAAE,QAAQ,EAAE,KAAK;IACpD,IAAI,WAAW;QAAC,GAAG,CAAC;QAAG,GAAG;QAAG,GAAG;IAAC;IAEjC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,IAAI,OAAO,QAAQ,UAAU,QAAQ,CAAC,EAAE,EAAE;QAE1C,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG;YACnF,WAAW;QACb;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,QAAQ,QAAQ,EAAE,IAAI,EAAE,KAAK;IACpC,IAAI,IAAI,cAAc;IACtB,IAAI,CAAC,GAAG,OAAO;IACf,IAAI,IAAI;IACR,IAAG,KAAK,IAAI,CAAC,WAAW,OAAO,EAAE,IAAI,CAAC,WAAW,IAAG;QAClD,KAAK;IACP,OAAO,IAAI,KAAK,MAAM,CAAC,WAAW,OAAO,EAAE,IAAI,CAAC,WAAW,IAAI;QAC7D,KAAK;IACP,OAAO,IAAI,KAAK,IAAI,CAAC,WAAW,OAAO,EAAE,MAAM,CAAC,WAAW,IAAI;QAC7D,KAAK;IACP,OAAO,IAAI,KAAK,IAAI,KAAK,KAAM;QAC7B,OAAO;IACT;IAEA,OAAO;QACL,GAAG;QACH,GAAG,KAAK,CAAC;QACT,GAAG,KAAK,CAAC;QACT,GAAG;IACL;AACF;;AAEA;;;CAGC,GAED,SAAS,mBAAmB,MAAM,EAAE,QAAQ;IAC1C,mCAAmC;IACnC,IAAI,UAAU,oBAAoB,WAAW,YAAY,MAAM,UAAU;IAEzE,IAAI,CAAC,UAAU;QACb,+BAA+B;QAC/B,OAAO,QACJ,MAAM,CAAC,WACP,IAAI,CAAC,cACL,GAAG,CAAC;IACT;IAEA,IAAI,aAAa,SAAS,GAAG,CAAC,SAAS,YAAY,IAAI,EAAE,KAAK;QAC5D,OAAO,oBAAoB,MAAM,SAAS;IAC5C;IAEA,oCAAoC;IACpC,OAAO,WAAW,MAAM,CAAC,WAAW,IAAI,CAAC,cAAc,GAAG,CAAC,SAAS,YAAY,QAAQ;QACtF,OAAO,QAAQ,CAAC,WAAW,OAAO,CAAC,UAAU;IAC/C;AACF;AAEA;;;CAGC,GAED,SAAS,aAAa,CAAC,EAAE,CAAC;IACxB,OAAO,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAK;AACrE;AAEA;;;CAGC,GAED,SAAS,gBAAgB,IAAI;IAC3B,OAAO,KAAK,IAAI;AAClB;AAEA;;;CAGC,GAED,SAAS,UAAU,IAAI;IACrB,OAAO,KAAK,CAAC,GAAG;AAClB", "ignoreList": [0]}}, {"offset": {"line": 2455, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/negotiator/lib/mediaType.js"], "sourcesContent": ["/**\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2014 <PERSON>\n * Copyright(c) 2014-2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredMediaTypes;\nmodule.exports.preferredMediaTypes = preferredMediaTypes;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleMediaTypeRegExp = /^\\s*([^\\s\\/;]+)\\/([^;\\s]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept header.\n * @private\n */\n\nfunction parseAccept(accept) {\n  var accepts = splitMediaTypes(accept);\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var mediaType = parseMediaType(accepts[i].trim(), i);\n\n    if (mediaType) {\n      accepts[j++] = mediaType;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a media type from the Accept header.\n * @private\n */\n\nfunction parseMediaType(str, i) {\n  var match = simpleMediaTypeRegExp.exec(str);\n  if (!match) return null;\n\n  var params = Object.create(null);\n  var q = 1;\n  var subtype = match[2];\n  var type = match[1];\n\n  if (match[3]) {\n    var kvps = splitParameters(match[3]).map(splitKeyValuePair);\n\n    for (var j = 0; j < kvps.length; j++) {\n      var pair = kvps[j];\n      var key = pair[0].toLowerCase();\n      var val = pair[1];\n\n      // get the value, unwrapping quotes\n      var value = val && val[0] === '\"' && val[val.length - 1] === '\"'\n        ? val.slice(1, -1)\n        : val;\n\n      if (key === 'q') {\n        q = parseFloat(value);\n        break;\n      }\n\n      // store parameter\n      params[key] = value;\n    }\n  }\n\n  return {\n    type: type,\n    subtype: subtype,\n    params: params,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of a media type.\n * @private\n */\n\nfunction getMediaTypePriority(type, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(type, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the media type.\n * @private\n */\n\nfunction specify(type, spec, index) {\n  var p = parseMediaType(type);\n  var s = 0;\n\n  if (!p) {\n    return null;\n  }\n\n  if(spec.type.toLowerCase() == p.type.toLowerCase()) {\n    s |= 4\n  } else if(spec.type != '*') {\n    return null;\n  }\n\n  if(spec.subtype.toLowerCase() == p.subtype.toLowerCase()) {\n    s |= 2\n  } else if(spec.subtype != '*') {\n    return null;\n  }\n\n  var keys = Object.keys(spec.params);\n  if (keys.length > 0) {\n    if (keys.every(function (k) {\n      return spec.params[k] == '*' || (spec.params[k] || '').toLowerCase() == (p.params[k] || '').toLowerCase();\n    })) {\n      s |= 1\n    } else {\n      return null\n    }\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s,\n  }\n}\n\n/**\n * Get the preferred media types from an Accept header.\n * @public\n */\n\nfunction preferredMediaTypes(accept, provided) {\n  // RFC 2616 sec 14.2: no header = */*\n  var accepts = parseAccept(accept === undefined ? '*/*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all types\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullType);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getMediaTypePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted types\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getType(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full type string.\n * @private\n */\n\nfunction getFullType(spec) {\n  return spec.type + '/' + spec.subtype;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n\n/**\n * Count the number of quotes in a string.\n * @private\n */\n\nfunction quoteCount(string) {\n  var count = 0;\n  var index = 0;\n\n  while ((index = string.indexOf('\"', index)) !== -1) {\n    count++;\n    index++;\n  }\n\n  return count;\n}\n\n/**\n * Split a key value pair.\n * @private\n */\n\nfunction splitKeyValuePair(str) {\n  var index = str.indexOf('=');\n  var key;\n  var val;\n\n  if (index === -1) {\n    key = str;\n  } else {\n    key = str.slice(0, index);\n    val = str.slice(index + 1);\n  }\n\n  return [key, val];\n}\n\n/**\n * Split an Accept header into media types.\n * @private\n */\n\nfunction splitMediaTypes(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 1, j = 0; i < accepts.length; i++) {\n    if (quoteCount(accepts[j]) % 2 == 0) {\n      accepts[++j] = accepts[i];\n    } else {\n      accepts[j] += ',' + accepts[i];\n    }\n  }\n\n  // trim accepts\n  accepts.length = j + 1;\n\n  return accepts;\n}\n\n/**\n * Split a string of parameters.\n * @private\n */\n\nfunction splitParameters(str) {\n  var parameters = str.split(';');\n\n  for (var i = 1, j = 0; i < parameters.length; i++) {\n    if (quoteCount(parameters[j]) % 2 == 0) {\n      parameters[++j] = parameters[i];\n    } else {\n      parameters[j] += ';' + parameters[i];\n    }\n  }\n\n  // trim parameters\n  parameters.length = j + 1;\n\n  for (var i = 0; i < parameters.length; i++) {\n    parameters[i] = parameters[i].trim();\n  }\n\n  return parameters;\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;AAEA;;;CAGC,GAED,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,mBAAmB,GAAG;AAErC;;;CAGC,GAED,IAAI,wBAAwB;AAE5B;;;CAGC,GAED,SAAS,YAAY,MAAM;IACzB,IAAI,UAAU,gBAAgB;IAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QAC9C,IAAI,YAAY,eAAe,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI;QAElD,IAAI,WAAW;YACb,OAAO,CAAC,IAAI,GAAG;QACjB;IACF;IAEA,eAAe;IACf,QAAQ,MAAM,GAAG;IAEjB,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,eAAe,GAAG,EAAE,CAAC;IAC5B,IAAI,QAAQ,sBAAsB,IAAI,CAAC;IACvC,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI,SAAS,OAAO,MAAM,CAAC;IAC3B,IAAI,IAAI;IACR,IAAI,UAAU,KAAK,CAAC,EAAE;IACtB,IAAI,OAAO,KAAK,CAAC,EAAE;IAEnB,IAAI,KAAK,CAAC,EAAE,EAAE;QACZ,IAAI,OAAO,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC;QAEzC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YACpC,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC,WAAW;YAC7B,IAAI,MAAM,IAAI,CAAC,EAAE;YAEjB,mCAAmC;YACnC,IAAI,QAAQ,OAAO,GAAG,CAAC,EAAE,KAAK,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,KAAK,MACzD,IAAI,KAAK,CAAC,GAAG,CAAC,KACd;YAEJ,IAAI,QAAQ,KAAK;gBACf,IAAI,WAAW;gBACf;YACF;YAEA,kBAAkB;YAClB,MAAM,CAAC,IAAI,GAAG;QAChB;IACF;IAEA,OAAO;QACL,MAAM;QACN,SAAS;QACT,QAAQ;QACR,GAAG;QACH,GAAG;IACL;AACF;AAEA;;;CAGC,GAED,SAAS,qBAAqB,IAAI,EAAE,QAAQ,EAAE,KAAK;IACjD,IAAI,WAAW;QAAC,GAAG,CAAC;QAAG,GAAG;QAAG,GAAG;IAAC;IAEjC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,IAAI,OAAO,QAAQ,MAAM,QAAQ,CAAC,EAAE,EAAE;QAEtC,IAAI,QAAQ,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,SAAS,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG;YACnF,WAAW;QACb;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,KAAK;IAChC,IAAI,IAAI,eAAe;IACvB,IAAI,IAAI;IAER,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IAEA,IAAG,KAAK,IAAI,CAAC,WAAW,MAAM,EAAE,IAAI,CAAC,WAAW,IAAI;QAClD,KAAK;IACP,OAAO,IAAG,KAAK,IAAI,IAAI,KAAK;QAC1B,OAAO;IACT;IAEA,IAAG,KAAK,OAAO,CAAC,WAAW,MAAM,EAAE,OAAO,CAAC,WAAW,IAAI;QACxD,KAAK;IACP,OAAO,IAAG,KAAK,OAAO,IAAI,KAAK;QAC7B,OAAO;IACT;IAEA,IAAI,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM;IAClC,IAAI,KAAK,MAAM,GAAG,GAAG;QACnB,IAAI,KAAK,KAAK,CAAC,SAAU,CAAC;YACxB,OAAO,KAAK,MAAM,CAAC,EAAE,IAAI,OAAO,CAAC,KAAK,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,WAAW,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,WAAW;QACzG,IAAI;YACF,KAAK;QACP,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO;QACL,GAAG;QACH,GAAG,KAAK,CAAC;QACT,GAAG,KAAK,CAAC;QACT,GAAG;IACL;AACF;AAEA;;;CAGC,GAED,SAAS,oBAAoB,MAAM,EAAE,QAAQ;IAC3C,qCAAqC;IACrC,IAAI,UAAU,YAAY,WAAW,YAAY,QAAQ,UAAU;IAEnE,IAAI,CAAC,UAAU;QACb,2BAA2B;QAC3B,OAAO,QACJ,MAAM,CAAC,WACP,IAAI,CAAC,cACL,GAAG,CAAC;IACT;IAEA,IAAI,aAAa,SAAS,GAAG,CAAC,SAAS,YAAY,IAAI,EAAE,KAAK;QAC5D,OAAO,qBAAqB,MAAM,SAAS;IAC7C;IAEA,gCAAgC;IAChC,OAAO,WAAW,MAAM,CAAC,WAAW,IAAI,CAAC,cAAc,GAAG,CAAC,SAAS,QAAQ,QAAQ;QAClF,OAAO,QAAQ,CAAC,WAAW,OAAO,CAAC,UAAU;IAC/C;AACF;AAEA;;;CAGC,GAED,SAAS,aAAa,CAAC,EAAE,CAAC;IACxB,OAAO,AAAC,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAM,EAAE,CAAC,GAAG,EAAE,CAAC,IAAK;AACrE;AAEA;;;CAGC,GAED,SAAS,YAAY,IAAI;IACvB,OAAO,KAAK,IAAI,GAAG,MAAM,KAAK,OAAO;AACvC;AAEA;;;CAGC,GAED,SAAS,UAAU,IAAI;IACrB,OAAO,KAAK,CAAC,GAAG;AAClB;AAEA;;;CAGC,GAED,SAAS,WAAW,MAAM;IACxB,IAAI,QAAQ;IACZ,IAAI,QAAQ;IAEZ,MAAO,CAAC,QAAQ,OAAO,OAAO,CAAC,KAAK,MAAM,MAAM,CAAC,EAAG;QAClD;QACA;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,kBAAkB,GAAG;IAC5B,IAAI,QAAQ,IAAI,OAAO,CAAC;IACxB,IAAI;IACJ,IAAI;IAEJ,IAAI,UAAU,CAAC,GAAG;QAChB,MAAM;IACR,OAAO;QACL,MAAM,IAAI,KAAK,CAAC,GAAG;QACnB,MAAM,IAAI,KAAK,CAAC,QAAQ;IAC1B;IAEA,OAAO;QAAC;QAAK;KAAI;AACnB;AAEA;;;CAGC,GAED,SAAS,gBAAgB,MAAM;IAC7B,IAAI,UAAU,OAAO,KAAK,CAAC;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QAC9C,IAAI,WAAW,OAAO,CAAC,EAAE,IAAI,KAAK,GAAG;YACnC,OAAO,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,EAAE;QAC3B,OAAO;YACL,OAAO,CAAC,EAAE,IAAI,MAAM,OAAO,CAAC,EAAE;QAChC;IACF;IAEA,eAAe;IACf,QAAQ,MAAM,GAAG,IAAI;IAErB,OAAO;AACT;AAEA;;;CAGC,GAED,SAAS,gBAAgB,GAAG;IAC1B,IAAI,aAAa,IAAI,KAAK,CAAC;IAE3B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QACjD,IAAI,WAAW,UAAU,CAAC,EAAE,IAAI,KAAK,GAAG;YACtC,UAAU,CAAC,EAAE,EAAE,GAAG,UAAU,CAAC,EAAE;QACjC,OAAO;YACL,UAAU,CAAC,EAAE,IAAI,MAAM,UAAU,CAAC,EAAE;QACtC;IACF;IAEA,kBAAkB;IAClB,WAAW,MAAM,GAAG,IAAI;IAExB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,IAAI;IACpC;IAEA,OAAO;AACT", "ignoreList": [0]}}, {"offset": {"line": 2679, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/negotiator/index.js"], "sourcesContent": ["/*!\n * negotiator\n * Copyright(c) 2012 <PERSON>\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\nvar preferredCharsets = require('./lib/charset')\nvar preferredEncodings = require('./lib/encoding')\nvar preferredLanguages = require('./lib/language')\nvar preferredMediaTypes = require('./lib/mediaType')\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = Negotiator;\nmodule.exports.Negotiator = Negotiator;\n\n/**\n * Create a Negotiator instance from a request.\n * @param {object} request\n * @public\n */\n\nfunction Negotiator(request) {\n  if (!(this instanceof Negotiator)) {\n    return new Negotiator(request);\n  }\n\n  this.request = request;\n}\n\nNegotiator.prototype.charset = function charset(available) {\n  var set = this.charsets(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.charsets = function charsets(available) {\n  return preferredCharsets(this.request.headers['accept-charset'], available);\n};\n\nNegotiator.prototype.encoding = function encoding(available, opts) {\n  var set = this.encodings(available, opts);\n  return set && set[0];\n};\n\nNegotiator.prototype.encodings = function encodings(available, options) {\n  var opts = options || {};\n  return preferredEncodings(this.request.headers['accept-encoding'], available, opts.preferred);\n};\n\nNegotiator.prototype.language = function language(available) {\n  var set = this.languages(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.languages = function languages(available) {\n  return preferredLanguages(this.request.headers['accept-language'], available);\n};\n\nNegotiator.prototype.mediaType = function mediaType(available) {\n  var set = this.mediaTypes(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.mediaTypes = function mediaTypes(available) {\n  return preferredMediaTypes(this.request.headers.accept, available);\n};\n\n// Backwards compatibility\nNegotiator.prototype.preferredCharset = Negotiator.prototype.charset;\nNegotiator.prototype.preferredCharsets = Negotiator.prototype.charsets;\nNegotiator.prototype.preferredEncoding = Negotiator.prototype.encoding;\nNegotiator.prototype.preferredEncodings = Negotiator.prototype.encodings;\nNegotiator.prototype.preferredLanguage = Negotiator.prototype.language;\nNegotiator.prototype.preferredLanguages = Negotiator.prototype.languages;\nNegotiator.prototype.preferredMediaType = Negotiator.prototype.mediaType;\nNegotiator.prototype.preferredMediaTypes = Negotiator.prototype.mediaTypes;\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GAED;AAEA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ;;;CAGC,GAED,OAAO,OAAO,GAAG;AACjB,OAAO,OAAO,CAAC,UAAU,GAAG;AAE5B;;;;CAIC,GAED,SAAS,WAAW,OAAO;IACzB,IAAI,CAAC,CAAC,IAAI,YAAY,UAAU,GAAG;QACjC,OAAO,IAAI,WAAW;IACxB;IAEA,IAAI,CAAC,OAAO,GAAG;AACjB;AAEA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAS,QAAQ,SAAS;IACvD,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC;IACxB,OAAO,OAAO,GAAG,CAAC,EAAE;AACtB;AAEA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,SAAS;IACzD,OAAO,kBAAkB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,EAAE;AACnE;AAEA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,SAAS,EAAE,IAAI;IAC/D,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW;IACpC,OAAO,OAAO,GAAG,CAAC,EAAE;AACtB;AAEA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,SAAS,EAAE,OAAO;IACpE,IAAI,OAAO,WAAW,CAAC;IACvB,OAAO,mBAAmB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,WAAW,KAAK,SAAS;AAC9F;AAEA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAS,SAAS,SAAS;IACzD,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC;IACzB,OAAO,OAAO,GAAG,CAAC,EAAE;AACtB;AAEA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,SAAS;IAC3D,OAAO,mBAAmB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE;AACrE;AAEA,WAAW,SAAS,CAAC,SAAS,GAAG,SAAS,UAAU,SAAS;IAC3D,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC;IAC1B,OAAO,OAAO,GAAG,CAAC,EAAE;AACtB;AAEA,WAAW,SAAS,CAAC,UAAU,GAAG,SAAS,WAAW,SAAS;IAC7D,OAAO,oBAAoB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE;AAC1D;AAEA,0BAA0B;AAC1B,WAAW,SAAS,CAAC,gBAAgB,GAAG,WAAW,SAAS,CAAC,OAAO;AACpE,WAAW,SAAS,CAAC,iBAAiB,GAAG,WAAW,SAAS,CAAC,QAAQ;AACtE,WAAW,SAAS,CAAC,iBAAiB,GAAG,WAAW,SAAS,CAAC,QAAQ;AACtE,WAAW,SAAS,CAAC,kBAAkB,GAAG,WAAW,SAAS,CAAC,SAAS;AACxE,WAAW,SAAS,CAAC,iBAAiB,GAAG,WAAW,SAAS,CAAC,QAAQ;AACtE,WAAW,SAAS,CAAC,kBAAkB,GAAG,WAAW,SAAS,CAAC,SAAS;AACxE,WAAW,SAAS,CAAC,kBAAkB,GAAG,WAAW,SAAS,CAAC,SAAS;AACxE,WAAW,SAAS,CAAC,mBAAmB,GAAG,WAAW,SAAS,CAAC,UAAU", "ignoreList": [0]}}, {"offset": {"line": 2749, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/fast-memoize/lib/index.js"], "sourcesContent": ["//\n// Main\n//\nexport function memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nexport var strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n"], "names": [], "mappings": "AAAA,EAAE;AACF,OAAO;AACP,EAAE;;;;;AACK,SAAS,QAAQ,EAAE,EAAE,OAAO;IAC/B,IAAI,QAAQ,WAAW,QAAQ,KAAK,GAAG,QAAQ,KAAK,GAAG;IACvD,IAAI,aAAa,WAAW,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG;IACtE,IAAI,WAAW,WAAW,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,GAAG;IAChE,OAAO,SAAS,IAAI;QAChB,OAAO;QACP,YAAY;IAChB;AACJ;AACA,EAAE;AACF,WAAW;AACX,EAAE;AACF,SAAS,YAAY,KAAK;IACtB,OAAQ,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,UAAU,WAAY,gEAAgE;AACvJ;AACA,SAAS,QAAQ,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IACvC,IAAI,WAAW,YAAY,OAAO,MAAM,WAAW;IACnD,IAAI,gBAAgB,MAAM,GAAG,CAAC;IAC9B,IAAI,OAAO,kBAAkB,aAAa;QACtC,gBAAgB,GAAG,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,GAAG,CAAC,UAAU;IACxB;IACA,OAAO;AACX;AACA,SAAS,SAAS,EAAE,EAAE,KAAK,EAAE,UAAU;IACnC,IAAI,OAAO,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;IACjD,IAAI,WAAW,WAAW;IAC1B,IAAI,gBAAgB,MAAM,GAAG,CAAC;IAC9B,IAAI,OAAO,kBAAkB,aAAa;QACtC,gBAAgB,GAAG,KAAK,CAAC,IAAI,EAAE;QAC/B,MAAM,GAAG,CAAC,UAAU;IACxB;IACA,OAAO;AACX;AACA,SAAS,SAAS,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS;IACrD,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,OAAO;AAC7C;AACA,SAAS,gBAAgB,EAAE,EAAE,OAAO;IAChC,IAAI,WAAW,GAAG,MAAM,KAAK,IAAI,UAAU;IAC3C,OAAO,SAAS,IAAI,IAAI,EAAE,UAAU,QAAQ,KAAK,CAAC,MAAM,IAAI,QAAQ,UAAU;AAClF;AACA,SAAS,iBAAiB,EAAE,EAAE,OAAO;IACjC,OAAO,SAAS,IAAI,IAAI,EAAE,UAAU,QAAQ,KAAK,CAAC,MAAM,IAAI,QAAQ,UAAU;AAClF;AACA,SAAS,gBAAgB,EAAE,EAAE,OAAO;IAChC,OAAO,SAAS,IAAI,IAAI,EAAE,SAAS,QAAQ,KAAK,CAAC,MAAM,IAAI,QAAQ,UAAU;AACjF;AACA,EAAE;AACF,aAAa;AACb,EAAE;AACF,IAAI,oBAAoB;IACpB,OAAO,KAAK,SAAS,CAAC;AAC1B;AACA,EAAE;AACF,QAAQ;AACR,EAAE;AACF,IAAI,8BAA6C;IAC7C,SAAS;QACL,IAAI,CAAC,KAAK,GAAG,OAAO,MAAM,CAAC;IAC/B;IACA,4BAA4B,SAAS,CAAC,GAAG,GAAG,SAAU,GAAG;QACrD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;IAC1B;IACA,4BAA4B,SAAS,CAAC,GAAG,GAAG,SAAU,GAAG,EAAE,KAAK;QAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG;IACtB;IACA,OAAO;AACX;AACA,IAAI,eAAe;IACf,QAAQ,SAAS;QACb,OAAO,IAAI;IACf;AACJ;AACO,IAAI,aAAa;IACpB,UAAU;IACV,SAAS;AACb", "ignoreList": [0]}}, {"offset": {"line": 2839, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-messageformat-parser/lib/error.js"], "sourcesContent": ["export var ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n"], "names": [], "mappings": ";;;AAAO,IAAI;AACX,CAAC,SAAU,SAAS;IAChB,qCAAqC,GACrC,SAAS,CAAC,SAAS,CAAC,gCAAgC,GAAG,EAAE,GAAG;IAC5D,mCAAmC,GACnC,SAAS,CAAC,SAAS,CAAC,iBAAiB,GAAG,EAAE,GAAG;IAC7C,2CAA2C,GAC3C,SAAS,CAAC,SAAS,CAAC,qBAAqB,GAAG,EAAE,GAAG;IACjD,4CAA4C,GAC5C,SAAS,CAAC,SAAS,CAAC,uBAAuB,GAAG,EAAE,GAAG;IACnD,iDAAiD,GACjD,SAAS,CAAC,SAAS,CAAC,wBAAwB,GAAG,EAAE,GAAG;IACpD,sDAAsD,GACtD,SAAS,CAAC,SAAS,CAAC,wBAAwB,GAAG,EAAE,GAAG;IACpD,oCAAoC,GACpC,SAAS,CAAC,SAAS,CAAC,0BAA0B,GAAG,EAAE,GAAG;IACtD,uCAAuC,GACvC,SAAS,CAAC,SAAS,CAAC,6BAA6B,GAAG,EAAE,GAAG;IACzD,2EAA2E,GAC3E,SAAS,CAAC,SAAS,CAAC,yBAAyB,GAAG,EAAE,GAAG;IACrD,4EAA4E,GAC5E,SAAS,CAAC,SAAS,CAAC,4BAA4B,GAAG,GAAG,GAAG;IACzD,6EAA6E,GAC7E,SAAS,CAAC,SAAS,CAAC,mCAAmC,GAAG,GAAG,GAAG;IAChE,2DAA2D,GAC3D,SAAS,CAAC,SAAS,CAAC,iCAAiC,GAAG,GAAG,GAAG;IAC9D,oGAAoG,GACpG,SAAS,CAAC,SAAS,CAAC,sCAAsC,GAAG,GAAG,GAAG;IACnE,6FAA6F,GAC7F,SAAS,CAAC,SAAS,CAAC,uCAAuC,GAAG,GAAG,GAAG;IACpE,oEAAoE,GACpE,SAAS,CAAC,SAAS,CAAC,kCAAkC,GAAG,GAAG,GAAG;IAC/D,uFAAuF,GACvF,SAAS,CAAC,SAAS,CAAC,kCAAkC,GAAG,GAAG,GAAG;IAC/D,2FAA2F,GAC3F,SAAS,CAAC,SAAS,CAAC,2CAA2C,GAAG,GAAG,GAAG;IACxE;;;KAGC,GACD,SAAS,CAAC,SAAS,CAAC,2CAA2C,GAAG,GAAG,GAAG;IACxE,wFAAwF,GACxF,SAAS,CAAC,SAAS,CAAC,mCAAmC,GAAG,GAAG,GAAG;IAChE;;;KAGC,GACD,SAAS,CAAC,SAAS,CAAC,qCAAqC,GAAG,GAAG,GAAG;IAClE;;KAEC,GACD,SAAS,CAAC,SAAS,CAAC,qCAAqC,GAAG,GAAG,GAAG;IAClE,+DAA+D,GAC/D,SAAS,CAAC,SAAS,CAAC,uBAAuB,GAAG,GAAG,GAAG;IACpD,qDAAqD,GACrD,SAAS,CAAC,SAAS,CAAC,cAAc,GAAG,GAAG,GAAG;IAC3C,qDAAqD,GACrD,SAAS,CAAC,SAAS,CAAC,mBAAmB,GAAG,GAAG,GAAG;IAChD,gFAAgF,GAChF,SAAS,CAAC,SAAS,CAAC,wBAAwB,GAAG,GAAG,GAAG;IACrD,kEAAkE,GAClE,SAAS,CAAC,SAAS,CAAC,eAAe,GAAG,GAAG,GAAG;AAChD,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC", "ignoreList": [0]}}, {"offset": {"line": 2885, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-messageformat-parser/lib/types.js"], "sourcesContent": ["export var TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nexport var SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nexport function isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nexport function isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nexport function isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nexport function isDateElement(el) {\n    return el.type === TYPE.date;\n}\nexport function isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nexport function isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nexport function isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nexport function isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nexport function isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nexport function isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nexport function isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nexport function createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nexport function createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAO,IAAI;AACX,CAAC,SAAU,IAAI;IACX;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,GAAG;IAC5B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,GAAG;IAC7B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG;IAC3B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG;IACzB;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG;IACzB;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG;IAC3B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,EAAE,GAAG;IAC3B;;;KAGC,GACD,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC1B;;KAEC,GACD,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG;AAC5B,CAAC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACd,IAAI;AACX,CAAC,SAAU,aAAa;IACpB,aAAa,CAAC,aAAa,CAAC,SAAS,GAAG,EAAE,GAAG;IAC7C,aAAa,CAAC,aAAa,CAAC,WAAW,GAAG,EAAE,GAAG;AACnD,CAAC,EAAE,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;AAIhC,SAAS,iBAAiB,EAAE;IAC/B,OAAO,GAAG,IAAI,KAAK,KAAK,OAAO;AACnC;AACO,SAAS,kBAAkB,EAAE;IAChC,OAAO,GAAG,IAAI,KAAK,KAAK,QAAQ;AACpC;AACO,SAAS,gBAAgB,EAAE;IAC9B,OAAO,GAAG,IAAI,KAAK,KAAK,MAAM;AAClC;AACO,SAAS,cAAc,EAAE;IAC5B,OAAO,GAAG,IAAI,KAAK,KAAK,IAAI;AAChC;AACO,SAAS,cAAc,EAAE;IAC5B,OAAO,GAAG,IAAI,KAAK,KAAK,IAAI;AAChC;AACO,SAAS,gBAAgB,EAAE;IAC9B,OAAO,GAAG,IAAI,KAAK,KAAK,MAAM;AAClC;AACO,SAAS,gBAAgB,EAAE;IAC9B,OAAO,GAAG,IAAI,KAAK,KAAK,MAAM;AAClC;AACO,SAAS,eAAe,EAAE;IAC7B,OAAO,GAAG,IAAI,KAAK,KAAK,KAAK;AACjC;AACO,SAAS,aAAa,EAAE;IAC3B,OAAO,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B;AACO,SAAS,iBAAiB,EAAE;IAC/B,OAAO,CAAC,CAAC,CAAC,MAAM,OAAO,OAAO,YAAY,GAAG,IAAI,KAAK,cAAc,MAAM;AAC9E;AACO,SAAS,mBAAmB,EAAE;IACjC,OAAO,CAAC,CAAC,CAAC,MAAM,OAAO,OAAO,YAAY,GAAG,IAAI,KAAK,cAAc,QAAQ;AAChF;AACO,SAAS,qBAAqB,KAAK;IACtC,OAAO;QACH,MAAM,KAAK,OAAO;QAClB,OAAO;IACX;AACJ;AACO,SAAS,oBAAoB,KAAK,EAAE,KAAK;IAC5C,OAAO;QACH,MAAM,KAAK,MAAM;QACjB,OAAO;QACP,OAAO;IACX;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2990, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js"], "sourcesContent": ["// @generated from regex-gen.ts\nexport var SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;;AACxB,IAAI,wBAAwB;AAC5B,IAAI,oBAAoB", "ignoreList": [0]}}, {"offset": {"line": 3003, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js"], "sourcesContent": ["// @generated from time-data-gen.ts\n// prettier-ignore  \nexport var timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n"], "names": [], "mappings": "AAAA,mCAAmC;AACnC,oBAAoB;;;;AACb,IAAI,WAAW;IAClB,OAAO;QACH;QACA;KACH;IACD,OAAO;QACH;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;QACA;QACA;KACH;IACD,MAAM;QACF;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,UAAU;QACN;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,UAAU;QACN;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;KACH;IACD,SAAS;QACL;QACA;QACA;QACA;KACH;AACL", "ignoreList": [0]}}, {"offset": {"line": 4429, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js"], "sourcesContent": ["import { timeData } from './time-data.generated';\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nexport function getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = timeData[regionTag || ''] ||\n        timeData[languageTag || ''] ||\n        timeData[\"\".concat(languageTag, \"-001\")] ||\n        timeData['001'];\n    return hourCycles[0];\n}\n"], "names": [], "mappings": ";;;AAAA;;AAQO,SAAS,eAAe,QAAQ,EAAE,MAAM;IAC3C,IAAI,eAAe;IACnB,IAAK,IAAI,aAAa,GAAG,aAAa,SAAS,MAAM,EAAE,aAAc;QACjE,IAAI,cAAc,SAAS,MAAM,CAAC;QAClC,IAAI,gBAAgB,KAAK;YACrB,IAAI,cAAc;YAClB,MAAO,aAAa,IAAI,SAAS,MAAM,IACnC,SAAS,MAAM,CAAC,aAAa,OAAO,YAAa;gBACjD;gBACA;YACJ;YACA,IAAI,UAAU,IAAI,CAAC,cAAc,CAAC;YAClC,IAAI,eAAe,cAAc,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC;YAC9D,IAAI,gBAAgB;YACpB,IAAI,WAAW,+BAA+B;YAC9C,IAAI,YAAY,OAAO,YAAY,KAAK;gBACpC,eAAe;YACnB;YACA,MAAO,iBAAiB,EAAG;gBACvB,gBAAgB;YACpB;YACA,MAAO,YAAY,EAAG;gBAClB,eAAe,WAAW;YAC9B;QACJ,OACK,IAAI,gBAAgB,KAAK;YAC1B,gBAAgB;QACpB,OACK;YACD,gBAAgB;QACpB;IACJ;IACA,OAAO;AACX;AACA;;;;CAIC,GACD,SAAS,+BAA+B,MAAM;IAC1C,IAAI,YAAY,OAAO,SAAS;IAChC,IAAI,cAAc,aACd,gDAAgD;IAChD,OAAO,UAAU,IACjB,aAAa;IACb,OAAO,UAAU,CAAC,MAAM,EAAE;QAC1B,aAAa;QACb,YAAY,OAAO,UAAU,CAAC,EAAE;IACpC;IACA,IAAI,WAAW;QACX,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,MAAM,IAAI,MAAM;QACxB;IACJ;IACA,oFAAoF;IACpF,IAAI,cAAc,OAAO,QAAQ;IACjC,IAAI;IACJ,IAAI,gBAAgB,QAAQ;QACxB,YAAY,OAAO,QAAQ,GAAG,MAAM;IACxC;IACA,IAAI,aAAa,wMAAA,CAAA,WAAQ,CAAC,aAAa,GAAG,IACtC,wMAAA,CAAA,WAAQ,CAAC,eAAe,GAAG,IAC3B,wMAAA,CAAA,WAAQ,CAAC,GAAG,MAAM,CAAC,aAAa,QAAQ,IACxC,wMAAA,CAAA,WAAQ,CAAC,MAAM;IACnB,OAAO,UAAU,CAAC,EAAE;AACxB", "ignoreList": [0]}}, {"offset": {"line": 4506, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js"], "sourcesContent": ["var _a;\nimport { __assign } from \"tslib\";\nimport { <PERSON>rrorKind } from './error';\nimport { SKELETON_TYPE, TYPE, } from './types';\nimport { SPACE_SEPARATOR_REGEX } from './regex.generated';\nimport { parseNumberSkeleton, parseNumberSkeletonFromString, parseDateTimeSkeleton, } from '@formatjs/icu-skeleton-parser';\nimport { getBestPattern } from './date-time-pattern-generator';\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = getBestPattern(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? parseDateTimeSkeleton(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? TYPE.date : TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? TYPE.number\n                            : argType === 'date'\n                                ? TYPE.date\n                                : TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, __assign({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = parseNumberSkeletonFromString(skeleton);\n        }\n        catch (e) {\n            return this.error(ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? parseNumberSkeleton(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\nexport { Parser };\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AANA,IAAI;;;;;;;AAOJ,IAAI,8BAA8B,IAAI,OAAO,IAAI,MAAM,CAAC,iMAAA,CAAA,wBAAqB,CAAC,MAAM,EAAE;AACtF,IAAI,4BAA4B,IAAI,OAAO,GAAG,MAAM,CAAC,iMAAA,CAAA,wBAAqB,CAAC,MAAM,EAAE;AACnF,SAAS,eAAe,KAAK,EAAE,GAAG;IAC9B,OAAO;QAAE,OAAO;QAAO,KAAK;IAAI;AACpC;AACA,oBAAoB;AACpB,0EAA0E;AAC1E,IAAI,sBAAsB,CAAC,CAAC,OAAO,SAAS,CAAC,UAAU,IAAI,KAAK,UAAU,CAAC,KAAK;AAChF,IAAI,yBAAyB,CAAC,CAAC,OAAO,aAAa;AACnD,IAAI,uBAAuB,CAAC,CAAC,OAAO,WAAW;AAC/C,IAAI,uBAAuB,CAAC,CAAC,OAAO,SAAS,CAAC,WAAW;AACzD,IAAI,eAAe,CAAC,CAAC,OAAO,SAAS,CAAC,SAAS;AAC/C,IAAI,aAAa,CAAC,CAAC,OAAO,SAAS,CAAC,OAAO;AAC3C,IAAI,yBAAyB,CAAC,CAAC,OAAO,aAAa;AACnD,IAAI,gBAAgB,yBACd,OAAO,aAAa,GACpB,SAAU,CAAC;IACT,OAAQ,OAAO,MAAM,YACjB,SAAS,MACT,KAAK,KAAK,CAAC,OAAO,KAClB,KAAK,GAAG,CAAC,MAAM;AACvB;AACJ,iCAAiC;AACjC,IAAI,yBAAyB;AAC7B,IAAI;IACA,IAAI,KAAK,GAAG,6CAA6C;IACzD;;;;;KAKC,GACD,yBAAyB,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,EAAE,MAAM;AAClG,EACA,OAAO,GAAG;IACN,yBAAyB;AAC7B;AACA,IAAI,aAAa,sBAET,SAAS,WAAW,CAAC,EAAE,MAAM,EAAE,QAAQ;IACnC,OAAO,EAAE,UAAU,CAAC,QAAQ;AAChC,IAEA,SAAS,WAAW,CAAC,EAAE,MAAM,EAAE,QAAQ;IACnC,OAAO,EAAE,KAAK,CAAC,UAAU,WAAW,OAAO,MAAM,MAAM;AAC3D;AACR,IAAI,gBAAgB,yBACd,OAAO,aAAa,GAElB,SAAS;IACL,IAAI,aAAa,EAAE;IACnB,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;QAC1C,UAAU,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;IAClC;IACA,IAAI,WAAW;IACf,IAAI,SAAS,WAAW,MAAM;IAC9B,IAAI,IAAI;IACR,IAAI;IACJ,MAAO,SAAS,EAAG;QACf,OAAO,UAAU,CAAC,IAAI;QACtB,IAAI,OAAO,UACP,MAAM,WAAW,OAAO;QAC5B,YACI,OAAO,UACD,OAAO,YAAY,CAAC,QACpB,OAAO,YAAY,CAAC,CAAC,CAAC,QAAQ,OAAO,KAAK,EAAE,IAAI,QAAQ,AAAC,OAAO,QAAS;IACvF;IACA,OAAO;AACX;AACR,IAAI,cACJ,SAAS;AACT,uBACM,OAAO,WAAW,GAEhB,SAAS,YAAY,OAAO;IACxB,IAAI,MAAM,CAAC;IACX,IAAK,IAAI,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,MAAM,EAAE,KAAM;QAC/D,IAAI,KAAK,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;QAC5C,GAAG,CAAC,EAAE,GAAG;IACb;IACA,OAAO;AACX;AACR,IAAI,cAAc,uBAEV,SAAS,YAAY,CAAC,EAAE,KAAK;IACzB,OAAO,EAAE,WAAW,CAAC;AACzB,IAEA,SAAS,YAAY,CAAC,EAAE,KAAK;IACzB,IAAI,OAAO,EAAE,MAAM;IACnB,IAAI,QAAQ,KAAK,SAAS,MAAM;QAC5B,OAAO;IACX;IACA,IAAI,QAAQ,EAAE,UAAU,CAAC;IACzB,IAAI;IACJ,OAAO,QAAQ,UACX,QAAQ,UACR,QAAQ,MAAM,QACd,CAAC,SAAS,EAAE,UAAU,CAAC,QAAQ,EAAE,IAAI,UACrC,SAAS,SACP,QACA,CAAC,AAAC,QAAQ,UAAW,EAAE,IAAI,CAAC,SAAS,MAAM,IAAI;AACzD;AACR,IAAI,YAAY,eAER,SAAS,UAAU,CAAC;IAChB,OAAO,EAAE,SAAS;AACtB,IAEA,SAAS,UAAU,CAAC;IAChB,OAAO,EAAE,OAAO,CAAC,6BAA6B;AAClD;AACR,IAAI,UAAU,aAEN,SAAS,QAAQ,CAAC;IACd,OAAO,EAAE,OAAO;AACpB,IAEA,SAAS,QAAQ,CAAC;IACd,OAAO,EAAE,OAAO,CAAC,2BAA2B;AAChD;AACR,kGAAkG;AAClG,SAAS,GAAG,CAAC,EAAE,IAAI;IACf,OAAO,IAAI,OAAO,GAAG;AACzB;AACA,aAAa;AACb,IAAI;AACJ,IAAI,wBAAwB;IACxB,SAAS;IACT,IAAI,yBAAyB,GAAG,6CAA6C;IAC7E,yBAAyB,SAAS,uBAAuB,CAAC,EAAE,KAAK;QAC7D,IAAI;QACJ,uBAAuB,SAAS,GAAG;QACnC,IAAI,QAAQ,uBAAuB,IAAI,CAAC;QACxC,OAAO,CAAC,KAAK,KAAK,CAAC,EAAE,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;IAC5D;AACJ,OACK;IACD,OAAO;IACP,yBAAyB,SAAS,uBAAuB,CAAC,EAAE,KAAK;QAC7D,IAAI,QAAQ,EAAE;QACd,MAAO,KAAM;YACT,IAAI,IAAI,YAAY,GAAG;YACvB,IAAI,MAAM,aAAa,cAAc,MAAM,iBAAiB,IAAI;gBAC5D;YACJ;YACA,MAAM,IAAI,CAAC;YACX,SAAS,KAAK,UAAU,IAAI;QAChC;QACA,OAAO,cAAc,KAAK,CAAC,KAAK,GAAG;IACvC;AACJ;AACA,IAAI,SAAwB;IACxB,SAAS,OAAO,OAAO,EAAE,OAAO;QAC5B,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU,CAAC;QAAG;QACxC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;YAAE,QAAQ;YAAG,MAAM;YAAG,QAAQ;QAAE;QAChD,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,QAAQ,SAAS;QACpC,IAAI,CAAC,MAAM,GAAG,QAAQ,MAAM;QAC5B,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,QAAQ,mBAAmB;QACxD,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,QAAQ,oBAAoB;IAC9D;IACA,OAAO,SAAS,CAAC,KAAK,GAAG;QACrB,IAAI,IAAI,CAAC,MAAM,OAAO,GAAG;YACrB,MAAM,MAAM;QAChB;QACA,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,IAAI;IACpC;IACA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAU,YAAY,EAAE,aAAa,EAAE,iBAAiB;QACpF,IAAI,WAAW,EAAE;QACjB,MAAO,CAAC,IAAI,CAAC,KAAK,GAAI;YAClB,IAAI,OAAO,IAAI,CAAC,IAAI;YACpB,IAAI,SAAS,IAAI,OAAO,KAAI;gBACxB,IAAI,SAAS,IAAI,CAAC,aAAa,CAAC,cAAc;gBAC9C,IAAI,OAAO,GAAG,EAAE;oBACZ,OAAO;gBACX;gBACA,SAAS,IAAI,CAAC,OAAO,GAAG;YAC5B,OACK,IAAI,SAAS,IAAI,OAAO,OAAM,eAAe,GAAG;gBACjD;YACJ,OACK,IAAI,SAAS,GAAG,OAAO,OACxB,CAAC,kBAAkB,YAAY,kBAAkB,eAAe,GAAG;gBACnE,IAAI,WAAW,IAAI,CAAC,aAAa;gBACjC,IAAI,CAAC,IAAI;gBACT,SAAS,IAAI,CAAC;oBACV,MAAM,oLAAA,CAAA,OAAI,CAAC,KAAK;oBAChB,UAAU,eAAe,UAAU,IAAI,CAAC,aAAa;gBACzD;YACJ,OACK,IAAI,SAAS,GAAG,OAAO,OACxB,CAAC,IAAI,CAAC,SAAS,IACf,IAAI,CAAC,IAAI,OAAO,GAAG,oBAAoB;cACzC;gBACE,IAAI,mBAAmB;oBACnB;gBACJ,OACK;oBACD,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,qBAAqB,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;gBAC9G;YACJ,OACK,IAAI,SAAS,GAAG,OAAO,OACxB,CAAC,IAAI,CAAC,SAAS,IACf,SAAS,IAAI,CAAC,IAAI,MAAM,IAAI;gBAC5B,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,cAAc;gBACzC,IAAI,OAAO,GAAG,EAAE;oBACZ,OAAO;gBACX;gBACA,SAAS,IAAI,CAAC,OAAO,GAAG;YAC5B,OACK;gBACD,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,cAAc;gBAC7C,IAAI,OAAO,GAAG,EAAE;oBACZ,OAAO;gBACX;gBACA,SAAS,IAAI,CAAC,OAAO,GAAG;YAC5B;QACJ;QACA,OAAO;YAAE,KAAK;YAAU,KAAK;QAAK;IACtC;IACA;;;;;;;;;;;;;;;;;KAiBC,GACD,OAAO,SAAS,CAAC,QAAQ,GAAG,SAAU,YAAY,EAAE,aAAa;QAC7D,IAAI,gBAAgB,IAAI,CAAC,aAAa;QACtC,IAAI,CAAC,IAAI,IAAI,MAAM;QACnB,IAAI,UAAU,IAAI,CAAC,YAAY;QAC/B,IAAI,CAAC,SAAS;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO;YACnB,mBAAmB;YACnB,OAAO;gBACH,KAAK;oBACD,MAAM,oLAAA,CAAA,OAAI,CAAC,OAAO;oBAClB,OAAO,IAAI,MAAM,CAAC,SAAS;oBAC3B,UAAU,eAAe,eAAe,IAAI,CAAC,aAAa;gBAC9D;gBACA,KAAK;YACT;QACJ,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM;YACvB,IAAI,iBAAiB,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,eAAe;YACxE,IAAI,eAAe,GAAG,EAAE;gBACpB,OAAO;YACX;YACA,IAAI,WAAW,eAAe,GAAG;YACjC,wBAAwB;YACxB,IAAI,sBAAsB,IAAI,CAAC,aAAa;YAC5C,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO;gBACnB,IAAI,IAAI,CAAC,KAAK,MAAM,CAAC,SAAS,IAAI,CAAC,IAAI,KAAK;oBACxC,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,WAAW,EAAE,eAAe,qBAAqB,IAAI,CAAC,aAAa;gBACnG;gBACA,IAAI,8BAA8B,IAAI,CAAC,aAAa;gBACpD,IAAI,iBAAiB,IAAI,CAAC,YAAY;gBACtC,IAAI,YAAY,gBAAgB;oBAC5B,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,qBAAqB,EAAE,eAAe,6BAA6B,IAAI,CAAC,aAAa;gBACrH;gBACA,IAAI,CAAC,SAAS;gBACd,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;oBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,WAAW,EAAE,eAAe,qBAAqB,IAAI,CAAC,aAAa;gBACnG;gBACA,OAAO;oBACH,KAAK;wBACD,MAAM,oLAAA,CAAA,OAAI,CAAC,GAAG;wBACd,OAAO;wBACP,UAAU;wBACV,UAAU,eAAe,eAAe,IAAI,CAAC,aAAa;oBAC9D;oBACA,KAAK;gBACT;YACJ,OACK;gBACD,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,YAAY,EAAE,eAAe,eAAe,IAAI,CAAC,aAAa;YAC9F;QACJ,OACK;YACD,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,WAAW,EAAE,eAAe,eAAe,IAAI,CAAC,aAAa;QAC7F;IACJ;IACA;;KAEC,GACD,OAAO,SAAS,CAAC,YAAY,GAAG;QAC5B,IAAI,cAAc,IAAI,CAAC,MAAM;QAC7B,IAAI,CAAC,IAAI,IAAI,+BAA+B;QAC5C,MAAO,CAAC,IAAI,CAAC,KAAK,MAAM,4BAA4B,IAAI,CAAC,IAAI,IAAK;YAC9D,IAAI,CAAC,IAAI;QACb;QACA,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,MAAM;IACtD;IACA,OAAO,SAAS,CAAC,YAAY,GAAG,SAAU,YAAY,EAAE,aAAa;QACjE,IAAI,QAAQ,IAAI,CAAC,aAAa;QAC9B,IAAI,QAAQ;QACZ,MAAO,KAAM;YACT,IAAI,mBAAmB,IAAI,CAAC,aAAa,CAAC;YAC1C,IAAI,kBAAkB;gBAClB,SAAS;gBACT;YACJ;YACA,IAAI,sBAAsB,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAC9D,IAAI,qBAAqB;gBACrB,SAAS;gBACT;YACJ;YACA,IAAI,uBAAuB,IAAI,CAAC,wBAAwB;YACxD,IAAI,sBAAsB;gBACtB,SAAS;gBACT;YACJ;YACA;QACJ;QACA,IAAI,WAAW,eAAe,OAAO,IAAI,CAAC,aAAa;QACvD,OAAO;YACH,KAAK;gBAAE,MAAM,oLAAA,CAAA,OAAI,CAAC,OAAO;gBAAE,OAAO;gBAAO,UAAU;YAAS;YAC5D,KAAK;QACT;IACJ;IACA,OAAO,SAAS,CAAC,wBAAwB,GAAG;QACxC,IAAI,CAAC,IAAI,CAAC,KAAK,MACX,IAAI,CAAC,IAAI,OAAO,GAAG,OAAO,OAC1B,CAAC,IAAI,CAAC,SAAS,IACX,uDAAuD;QACvD,CAAC,gBAAgB,IAAI,CAAC,IAAI,MAAM,EAAE,GAAG;YACzC,IAAI,CAAC,IAAI,IAAI,MAAM;YACnB,OAAO;QACX;QACA,OAAO;IACX;IACA;;;;KAIC,GACD,OAAO,SAAS,CAAC,aAAa,GAAG,SAAU,aAAa;QACpD,IAAI,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,IAAI,OAAO,GAAG,OAAO,KAAI;YAC9C,OAAO;QACX;QACA,4FAA4F;QAC5F,sCAAsC;QACtC,OAAQ,IAAI,CAAC,IAAI;YACb,KAAK,GAAG,OAAO;gBACX,iDAAiD;gBACjD,IAAI,CAAC,IAAI;gBACT,IAAI,CAAC,IAAI;gBACT,OAAO;YACX,qBAAqB;YACrB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD;YACJ,KAAK;gBACD,IAAI,kBAAkB,YAAY,kBAAkB,iBAAiB;oBACjE;gBACJ;gBACA,OAAO;YACX;gBACI,OAAO;QACf;QACA,IAAI,CAAC,IAAI,IAAI,aAAa;QAC1B,IAAI,aAAa;YAAC,IAAI,CAAC,IAAI;SAAG,EAAE,eAAe;QAC/C,IAAI,CAAC,IAAI;QACT,4DAA4D;QAC5D,MAAO,CAAC,IAAI,CAAC,KAAK,GAAI;YAClB,IAAI,KAAK,IAAI,CAAC,IAAI;YAClB,IAAI,OAAO,GAAG,OAAO,KAAI;gBACrB,IAAI,IAAI,CAAC,IAAI,OAAO,GAAG,OAAO,KAAI;oBAC9B,WAAW,IAAI,CAAC;oBAChB,2DAA2D;oBAC3D,IAAI,CAAC,IAAI;gBACb,OACK;oBACD,+BAA+B;oBAC/B,IAAI,CAAC,IAAI;oBACT;gBACJ;YACJ,OACK;gBACD,WAAW,IAAI,CAAC;YACpB;YACA,IAAI,CAAC,IAAI;QACb;QACA,OAAO,cAAc,KAAK,CAAC,KAAK,GAAG;IACvC;IACA,OAAO,SAAS,CAAC,gBAAgB,GAAG,SAAU,YAAY,EAAE,aAAa;QACrE,IAAI,IAAI,CAAC,KAAK,IAAI;YACd,OAAO;QACX;QACA,IAAI,KAAK,IAAI,CAAC,IAAI;QAClB,IAAI,OAAO,GAAG,OAAO,OACjB,OAAO,IAAI,OAAO,OACjB,OAAO,GAAG,OAAO,OACd,CAAC,kBAAkB,YAAY,kBAAkB,eAAe,KACnE,OAAO,IAAI,OAAO,OAAM,eAAe,GAAI;YAC5C,OAAO;QACX,OACK;YACD,IAAI,CAAC,IAAI;YACT,OAAO,cAAc;QACzB;IACJ;IACA,OAAO,SAAS,CAAC,aAAa,GAAG,SAAU,YAAY,EAAE,iBAAiB;QACtE,IAAI,uBAAuB,IAAI,CAAC,aAAa;QAC7C,IAAI,CAAC,IAAI,IAAI,MAAM;QACnB,IAAI,CAAC,SAAS;QACd,IAAI,IAAI,CAAC,KAAK,IAAI;YACd,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,6BAA6B,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QACtH;QACA,IAAI,IAAI,CAAC,IAAI,OAAO,IAAI,OAAO,KAAI;YAC/B,IAAI,CAAC,IAAI;YACT,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,cAAc,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QACvG;QACA,gBAAgB;QAChB,IAAI,QAAQ,IAAI,CAAC,yBAAyB,GAAG,KAAK;QAClD,IAAI,CAAC,OAAO;YACR,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,kBAAkB,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QAC3G;QACA,IAAI,CAAC,SAAS;QACd,IAAI,IAAI,CAAC,KAAK,IAAI;YACd,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,6BAA6B,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QACtH;QACA,OAAQ,IAAI,CAAC,IAAI;YACb,4BAA4B;YAC5B,KAAK,IAAI,OAAO;gBAAI;oBAChB,IAAI,CAAC,IAAI,IAAI,MAAM;oBACnB,OAAO;wBACH,KAAK;4BACD,MAAM,oLAAA,CAAA,OAAI,CAAC,QAAQ;4BACnB,yDAAyD;4BACzD,OAAO;4BACP,UAAU,eAAe,sBAAsB,IAAI,CAAC,aAAa;wBACrE;wBACA,KAAK;oBACT;gBACJ;YACA,+CAA+C;YAC/C,KAAK,GAAG,OAAO;gBAAI;oBACf,IAAI,CAAC,IAAI,IAAI,MAAM;oBACnB,IAAI,CAAC,SAAS;oBACd,IAAI,IAAI,CAAC,KAAK,IAAI;wBACd,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,6BAA6B,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;oBACtH;oBACA,OAAO,IAAI,CAAC,oBAAoB,CAAC,cAAc,mBAAmB,OAAO;gBAC7E;YACA;gBACI,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,kBAAkB,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QAC/G;IACJ;IACA;;;KAGC,GACD,OAAO,SAAS,CAAC,yBAAyB,GAAG;QACzC,IAAI,mBAAmB,IAAI,CAAC,aAAa;QACzC,IAAI,cAAc,IAAI,CAAC,MAAM;QAC7B,IAAI,QAAQ,uBAAuB,IAAI,CAAC,OAAO,EAAE;QACjD,IAAI,YAAY,cAAc,MAAM,MAAM;QAC1C,IAAI,CAAC,MAAM,CAAC;QACZ,IAAI,cAAc,IAAI,CAAC,aAAa;QACpC,IAAI,WAAW,eAAe,kBAAkB;QAChD,OAAO;YAAE,OAAO;YAAO,UAAU;QAAS;IAC9C;IACA,OAAO,SAAS,CAAC,oBAAoB,GAAG,SAAU,YAAY,EAAE,iBAAiB,EAAE,KAAK,EAAE,oBAAoB;QAC1G,IAAI;QACJ,oBAAoB;QACpB,sBAAsB;QACtB,eAAe;QACf,IAAI,oBAAoB,IAAI,CAAC,aAAa;QAC1C,IAAI,UAAU,IAAI,CAAC,yBAAyB,GAAG,KAAK;QACpD,IAAI,kBAAkB,IAAI,CAAC,aAAa;QACxC,OAAQ;YACJ,KAAK;gBACD,iFAAiF;gBACjF,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,oBAAoB,EAAE,eAAe,mBAAmB;YACxF,KAAK;YACL,KAAK;YACL,KAAK;gBAAQ;oBACT,oBAAoB;oBACpB,wBAAwB;oBACxB,yBAAyB;oBACzB,IAAI,CAAC,SAAS;oBACd,IAAI,mBAAmB;oBACvB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM;wBAClB,IAAI,CAAC,SAAS;wBACd,IAAI,qBAAqB,IAAI,CAAC,aAAa;wBAC3C,IAAI,SAAS,IAAI,CAAC,6BAA6B;wBAC/C,IAAI,OAAO,GAAG,EAAE;4BACZ,OAAO;wBACX;wBACA,IAAI,QAAQ,QAAQ,OAAO,GAAG;wBAC9B,IAAI,MAAM,MAAM,KAAK,GAAG;4BACpB,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,qBAAqB,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;wBAC9G;wBACA,IAAI,gBAAgB,eAAe,oBAAoB,IAAI,CAAC,aAAa;wBACzE,mBAAmB;4BAAE,OAAO;4BAAO,eAAe;wBAAc;oBACpE;oBACA,IAAI,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;oBAChD,IAAI,eAAe,GAAG,EAAE;wBACpB,OAAO;oBACX;oBACA,IAAI,aAAa,eAAe,sBAAsB,IAAI,CAAC,aAAa;oBACxE,4BAA4B;oBAC5B,IAAI,oBAAoB,WAAW,qBAAqB,QAAQ,qBAAqB,KAAK,IAAI,KAAK,IAAI,iBAAiB,KAAK,EAAE,MAAM,IAAI;wBACrI,6BAA6B;wBAC7B,IAAI,WAAW,UAAU,iBAAiB,KAAK,CAAC,KAAK,CAAC;wBACtD,IAAI,YAAY,UAAU;4BACtB,IAAI,SAAS,IAAI,CAAC,6BAA6B,CAAC,UAAU,iBAAiB,aAAa;4BACxF,IAAI,OAAO,GAAG,EAAE;gCACZ,OAAO;4BACX;4BACA,OAAO;gCACH,KAAK;oCAAE,MAAM,oLAAA,CAAA,OAAI,CAAC,MAAM;oCAAE,OAAO;oCAAO,UAAU;oCAAY,OAAO,OAAO,GAAG;gCAAC;gCAChF,KAAK;4BACT;wBACJ,OACK;4BACD,IAAI,SAAS,MAAM,KAAK,GAAG;gCACvB,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,yBAAyB,EAAE;4BAC3D;4BACA,IAAI,kBAAkB;4BACtB,oEAAoE;4BACpE,iEAAiE;4BACjE,4BAA4B;4BAC5B,IAAI,IAAI,CAAC,MAAM,EAAE;gCACb,kBAAkB,CAAA,GAAA,mNAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,IAAI,CAAC,MAAM;4BAC1D;4BACA,IAAI,QAAQ;gCACR,MAAM,oLAAA,CAAA,gBAAa,CAAC,QAAQ;gCAC5B,SAAS;gCACT,UAAU,iBAAiB,aAAa;gCACxC,eAAe,IAAI,CAAC,oBAAoB,GAClC,CAAA,GAAA,sLAAA,CAAA,wBAAqB,AAAD,EAAE,mBACtB,CAAC;4BACX;4BACA,IAAI,OAAO,YAAY,SAAS,oLAAA,CAAA,OAAI,CAAC,IAAI,GAAG,oLAAA,CAAA,OAAI,CAAC,IAAI;4BACrD,OAAO;gCACH,KAAK;oCAAE,MAAM;oCAAM,OAAO;oCAAO,UAAU;oCAAY,OAAO;gCAAM;gCACpE,KAAK;4BACT;wBACJ;oBACJ;oBACA,6BAA6B;oBAC7B,OAAO;wBACH,KAAK;4BACD,MAAM,YAAY,WACZ,oLAAA,CAAA,OAAI,CAAC,MAAM,GACX,YAAY,SACR,oLAAA,CAAA,OAAI,CAAC,IAAI,GACT,oLAAA,CAAA,OAAI,CAAC,IAAI;4BACnB,OAAO;4BACP,UAAU;4BACV,OAAO,CAAC,KAAK,qBAAqB,QAAQ,qBAAqB,KAAK,IAAI,KAAK,IAAI,iBAAiB,KAAK,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;wBAC9I;wBACA,KAAK;oBACT;gBACJ;YACA,KAAK;YACL,KAAK;YACL,KAAK;gBAAU;oBACX,oBAAoB;oBACpB,0BAA0B;oBAC1B,2BAA2B;oBAC3B,IAAI,oBAAoB,IAAI,CAAC,aAAa;oBAC1C,IAAI,CAAC,SAAS;oBACd,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;wBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,8BAA8B,EAAE,eAAe,mBAAmB,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG;oBAC/G;oBACA,IAAI,CAAC,SAAS;oBACd,gBAAgB;oBAChB,oCAAoC;oBACpC,yBAAyB;oBACzB,EAAE;oBACF,uBAAuB;oBACvB,EAAE;oBACF,wCAAwC;oBACxC,sBAAsB;oBACtB,IAAI,wBAAwB,IAAI,CAAC,yBAAyB;oBAC1D,IAAI,eAAe;oBACnB,IAAI,YAAY,YAAY,sBAAsB,KAAK,KAAK,UAAU;wBAClE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;4BACnB,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,mCAAmC,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;wBAC5H;wBACA,IAAI,CAAC,SAAS;wBACd,IAAI,SAAS,IAAI,CAAC,sBAAsB,CAAC,oLAAA,CAAA,YAAS,CAAC,mCAAmC,EAAE,oLAAA,CAAA,YAAS,CAAC,oCAAoC;wBACtI,IAAI,OAAO,GAAG,EAAE;4BACZ,OAAO;wBACX;wBACA,8CAA8C;wBAC9C,IAAI,CAAC,SAAS;wBACd,wBAAwB,IAAI,CAAC,yBAAyB;wBACtD,eAAe,OAAO,GAAG;oBAC7B;oBACA,IAAI,gBAAgB,IAAI,CAAC,6BAA6B,CAAC,cAAc,SAAS,mBAAmB;oBACjG,IAAI,cAAc,GAAG,EAAE;wBACnB,OAAO;oBACX;oBACA,IAAI,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;oBAChD,IAAI,eAAe,GAAG,EAAE;wBACpB,OAAO;oBACX;oBACA,IAAI,aAAa,eAAe,sBAAsB,IAAI,CAAC,aAAa;oBACxE,IAAI,YAAY,UAAU;wBACtB,OAAO;4BACH,KAAK;gCACD,MAAM,oLAAA,CAAA,OAAI,CAAC,MAAM;gCACjB,OAAO;gCACP,SAAS,YAAY,cAAc,GAAG;gCACtC,UAAU;4BACd;4BACA,KAAK;wBACT;oBACJ,OACK;wBACD,OAAO;4BACH,KAAK;gCACD,MAAM,oLAAA,CAAA,OAAI,CAAC,MAAM;gCACjB,OAAO;gCACP,SAAS,YAAY,cAAc,GAAG;gCACtC,QAAQ;gCACR,YAAY,YAAY,WAAW,aAAa;gCAChD,UAAU;4BACd;4BACA,KAAK;wBACT;oBACJ;gBACJ;YACA;gBACI,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,qBAAqB,EAAE,eAAe,mBAAmB;QAC7F;IACJ;IACA,OAAO,SAAS,CAAC,qBAAqB,GAAG,SAAU,oBAAoB;QACnE,0CAA0C;QAC1C,EAAE;QACF,IAAI,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,IAAI,OAAO,IAAI,OAAO,KAAI;YAC/C,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,6BAA6B,EAAE,eAAe,sBAAsB,IAAI,CAAC,aAAa;QACtH;QACA,IAAI,CAAC,IAAI,IAAI,MAAM;QACnB,OAAO;YAAE,KAAK;YAAM,KAAK;QAAK;IAClC;IACA;;KAEC,GACD,OAAO,SAAS,CAAC,6BAA6B,GAAG;QAC7C,IAAI,eAAe;QACnB,IAAI,gBAAgB,IAAI,CAAC,aAAa;QACtC,MAAO,CAAC,IAAI,CAAC,KAAK,GAAI;YAClB,IAAI,KAAK,IAAI,CAAC,IAAI;YAClB,OAAQ;gBACJ,KAAK,GAAG,OAAO;oBAAI;wBACf,gEAAgE;wBAChE,2CAA2C;wBAC3C,IAAI,CAAC,IAAI;wBACT,IAAI,qBAAqB,IAAI,CAAC,aAAa;wBAC3C,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM;4BACtB,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,gCAAgC,EAAE,eAAe,oBAAoB,IAAI,CAAC,aAAa;wBACvH;wBACA,IAAI,CAAC,IAAI;wBACT;oBACJ;gBACA,KAAK,IAAI,OAAO;oBAAI;wBAChB,gBAAgB;wBAChB,IAAI,CAAC,IAAI;wBACT;oBACJ;gBACA,KAAK,IAAI,OAAO;oBAAI;wBAChB,IAAI,eAAe,GAAG;4BAClB,gBAAgB;wBACpB,OACK;4BACD,OAAO;gCACH,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,MAAM,EAAE,IAAI,CAAC,MAAM;gCACzD,KAAK;4BACT;wBACJ;wBACA;oBACJ;gBACA;oBACI,IAAI,CAAC,IAAI;oBACT;YACR;QACJ;QACA,OAAO;YACH,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,MAAM,EAAE,IAAI,CAAC,MAAM;YACzD,KAAK;QACT;IACJ;IACA,OAAO,SAAS,CAAC,6BAA6B,GAAG,SAAU,QAAQ,EAAE,QAAQ;QACzE,IAAI,SAAS,EAAE;QACf,IAAI;YACA,SAAS,CAAA,GAAA,gLAAA,CAAA,gCAA6B,AAAD,EAAE;QAC3C,EACA,OAAO,GAAG;YACN,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,uBAAuB,EAAE;QACzD;QACA,OAAO;YACH,KAAK;gBACD,MAAM,oLAAA,CAAA,gBAAa,CAAC,MAAM;gBAC1B,QAAQ;gBACR,UAAU;gBACV,eAAe,IAAI,CAAC,oBAAoB,GAClC,CAAA,GAAA,gLAAA,CAAA,sBAAmB,AAAD,EAAE,UACpB,CAAC;YACX;YACA,KAAK;QACT;IACJ;IACA;;;;;;;;;KASC,GACD,OAAO,SAAS,CAAC,6BAA6B,GAAG,SAAU,YAAY,EAAE,aAAa,EAAE,cAAc,EAAE,qBAAqB;QACzH,IAAI;QACJ,IAAI,iBAAiB;QACrB,IAAI,UAAU,EAAE;QAChB,IAAI,kBAAkB,IAAI;QAC1B,IAAI,WAAW,sBAAsB,KAAK,EAAE,mBAAmB,sBAAsB,QAAQ;QAC7F,SAAS;QACT,kBAAkB;QAClB,OAAO;QACP,MAAO,KAAM;YACT,IAAI,SAAS,MAAM,KAAK,GAAG;gBACvB,IAAI,gBAAgB,IAAI,CAAC,aAAa;gBACtC,IAAI,kBAAkB,YAAY,IAAI,CAAC,MAAM,CAAC,MAAM;oBAChD,iCAAiC;oBACjC,IAAI,SAAS,IAAI,CAAC,sBAAsB,CAAC,oLAAA,CAAA,YAAS,CAAC,+BAA+B,EAAE,oLAAA,CAAA,YAAS,CAAC,gCAAgC;oBAC9H,IAAI,OAAO,GAAG,EAAE;wBACZ,OAAO;oBACX;oBACA,mBAAmB,eAAe,eAAe,IAAI,CAAC,aAAa;oBACnE,WAAW,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,cAAc,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnE,OACK;oBACD;gBACJ;YACJ;YACA,6BAA6B;YAC7B,IAAI,gBAAgB,GAAG,CAAC,WAAW;gBAC/B,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,WAC9B,oLAAA,CAAA,YAAS,CAAC,kCAAkC,GAC5C,oLAAA,CAAA,YAAS,CAAC,kCAAkC,EAAE;YACxD;YACA,IAAI,aAAa,SAAS;gBACtB,iBAAiB;YACrB;YACA,SAAS;YACT,kBAAkB;YAClB,mBAAmB;YACnB,IAAI,CAAC,SAAS;YACd,IAAI,uBAAuB,IAAI,CAAC,aAAa;YAC7C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;gBACnB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,WAC9B,oLAAA,CAAA,YAAS,CAAC,wCAAwC,GAClD,oLAAA,CAAA,YAAS,CAAC,wCAAwC,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;YACrH;YACA,IAAI,iBAAiB,IAAI,CAAC,YAAY,CAAC,eAAe,GAAG,eAAe;YACxE,IAAI,eAAe,GAAG,EAAE;gBACpB,OAAO;YACX;YACA,IAAI,iBAAiB,IAAI,CAAC,qBAAqB,CAAC;YAChD,IAAI,eAAe,GAAG,EAAE;gBACpB,OAAO;YACX;YACA,QAAQ,IAAI,CAAC;gBACT;gBACA;oBACI,OAAO,eAAe,GAAG;oBACzB,UAAU,eAAe,sBAAsB,IAAI,CAAC,aAAa;gBACrE;aACH;YACD,uCAAuC;YACvC,gBAAgB,GAAG,CAAC;YACpB,6BAA6B;YAC7B,IAAI,CAAC,SAAS;YACb,KAAK,IAAI,CAAC,yBAAyB,IAAI,WAAW,GAAG,KAAK,EAAE,mBAAmB,GAAG,QAAQ;QAC/F;QACA,IAAI,QAAQ,MAAM,KAAK,GAAG;YACtB,OAAO,IAAI,CAAC,KAAK,CAAC,kBAAkB,WAC9B,oLAAA,CAAA,YAAS,CAAC,+BAA+B,GACzC,oLAAA,CAAA,YAAS,CAAC,+BAA+B,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;QAC5G;QACA,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,gBAAgB;YAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,oLAAA,CAAA,YAAS,CAAC,oBAAoB,EAAE,eAAe,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa;QAC7G;QACA,OAAO;YAAE,KAAK;YAAS,KAAK;QAAK;IACrC;IACA,OAAO,SAAS,CAAC,sBAAsB,GAAG,SAAU,iBAAiB,EAAE,kBAAkB;QACrF,IAAI,OAAO;QACX,IAAI,mBAAmB,IAAI,CAAC,aAAa;QACzC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CACtB,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM;YACvB,OAAO,CAAC;QACZ;QACA,IAAI,YAAY;QAChB,IAAI,UAAU;QACd,MAAO,CAAC,IAAI,CAAC,KAAK,GAAI;YAClB,IAAI,KAAK,IAAI,CAAC,IAAI;YAClB,IAAI,MAAM,GAAG,OAAO,OAAM,MAAM,GAAG,OAAO,KAAI;gBAC1C,YAAY;gBACZ,UAAU,UAAU,KAAK,CAAC,KAAK,EAAE;gBACjC,IAAI,CAAC,IAAI;YACb,OACK;gBACD;YACJ;QACJ;QACA,IAAI,WAAW,eAAe,kBAAkB,IAAI,CAAC,aAAa;QAClE,IAAI,CAAC,WAAW;YACZ,OAAO,IAAI,CAAC,KAAK,CAAC,mBAAmB;QACzC;QACA,WAAW;QACX,IAAI,CAAC,cAAc,UAAU;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,oBAAoB;QAC1C;QACA,OAAO;YAAE,KAAK;YAAS,KAAK;QAAK;IACrC;IACA,OAAO,SAAS,CAAC,MAAM,GAAG;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM;IAC/B;IACA,OAAO,SAAS,CAAC,KAAK,GAAG;QACrB,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;IAChD;IACA,OAAO,SAAS,CAAC,aAAa,GAAG;QAC7B,sDAAsD;QACtD,OAAO;YACH,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM;YAC5B,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI;YACxB,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM;QAChC;IACJ;IACA;;;KAGC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG;QACpB,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM;QACjC,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YAC/B,MAAM,MAAM;QAChB;QACA,IAAI,OAAO,YAAY,IAAI,CAAC,OAAO,EAAE;QACrC,IAAI,SAAS,WAAW;YACpB,MAAM,MAAM,UAAU,MAAM,CAAC,QAAQ;QACzC;QACA,OAAO;IACX;IACA,OAAO,SAAS,CAAC,KAAK,GAAG,SAAU,IAAI,EAAE,QAAQ;QAC7C,OAAO;YACH,KAAK;YACL,KAAK;gBACD,MAAM;gBACN,SAAS,IAAI,CAAC,OAAO;gBACrB,UAAU;YACd;QACJ;IACJ;IACA,kDAAkD,GAClD,OAAO,SAAS,CAAC,IAAI,GAAG;QACpB,IAAI,IAAI,CAAC,KAAK,IAAI;YACd;QACJ;QACA,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,SAAS,GAAG,QAAQ,KAAI;YACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI;YACtB,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;YACvB,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI;QAC5B,OACK;YACD,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI;YACxB,iEAAiE;YACjE,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,UAAU,IAAI;QACjD;IACJ;IACA;;;;;KAKC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAU,MAAM;QACtC,IAAI,WAAW,IAAI,CAAC,OAAO,EAAE,QAAQ,IAAI,CAAC,MAAM,KAAK;YACjD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACpC,IAAI,CAAC,IAAI;YACb;YACA,OAAO;QACX;QACA,OAAO;IACX;IACA;;;KAGC,GACD,OAAO,SAAS,CAAC,SAAS,GAAG,SAAU,OAAO;QAC1C,IAAI,gBAAgB,IAAI,CAAC,MAAM;QAC/B,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS;QAC1C,IAAI,SAAS,GAAG;YACZ,IAAI,CAAC,MAAM,CAAC;YACZ,OAAO;QACX,OACK;YACD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM;YAC/B,OAAO;QACX;IACJ;IACA;;;KAGC,GACD,OAAO,SAAS,CAAC,MAAM,GAAG,SAAU,YAAY;QAC5C,IAAI,IAAI,CAAC,MAAM,KAAK,cAAc;YAC9B,MAAM,MAAM,gBAAgB,MAAM,CAAC,cAAc,yDAAyD,MAAM,CAAC,IAAI,CAAC,MAAM;QAChI;QACA,eAAe,KAAK,GAAG,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,MAAM;QACzD,MAAO,KAAM;YACT,IAAI,SAAS,IAAI,CAAC,MAAM;YACxB,IAAI,WAAW,cAAc;gBACzB;YACJ;YACA,IAAI,SAAS,cAAc;gBACvB,MAAM,MAAM,gBAAgB,MAAM,CAAC,cAAc;YACrD;YACA,IAAI,CAAC,IAAI;YACT,IAAI,IAAI,CAAC,KAAK,IAAI;gBACd;YACJ;QACJ;IACJ;IACA,oFAAoF,GACpF,OAAO,SAAS,CAAC,SAAS,GAAG;QACzB,MAAO,CAAC,IAAI,CAAC,KAAK,MAAM,cAAc,IAAI,CAAC,IAAI,IAAK;YAChD,IAAI,CAAC,IAAI;QACb;IACJ;IACA;;;KAGC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG;QACpB,IAAI,IAAI,CAAC,KAAK,IAAI;YACd,OAAO;QACX;QACA,IAAI,OAAO,IAAI,CAAC,IAAI;QACpB,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,WAAW,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,QAAQ,UAAU,IAAI,CAAC;QACxE,OAAO,aAAa,QAAQ,aAAa,KAAK,IAAI,WAAW;IACjE;IACA,OAAO;AACX;;AAEA;;;;CAIC,GACD,SAAS,SAAS,SAAS;IACvB,OAAQ,AAAC,aAAa,MAAM,aAAa,OACpC,aAAa,MAAM,aAAa;AACzC;AACA,SAAS,gBAAgB,SAAS;IAC9B,OAAO,SAAS,cAAc,cAAc,IAAI,OAAO;AAC3D;AACA,kCAAkC,GAClC,SAAS,4BAA4B,CAAC;IAClC,OAAQ,MAAM,GAAG,OAAO,OACpB,MAAM,GAAG,OAAO,OACf,KAAK,MAAM,KAAK,MACjB,MAAM,GAAG,OAAO,OACf,KAAK,MAAM,KAAK,OAChB,KAAK,MAAM,KAAK,MACjB,KAAK,QACJ,KAAK,QAAQ,KAAK,QAClB,KAAK,QAAQ,KAAK,QAClB,KAAK,QAAQ,KAAK,SAClB,KAAK,SAAS,KAAK,UACnB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,WAAW,KAAK;AAC9B;AACA;;;CAGC,GACD,SAAS,cAAc,CAAC;IACpB,OAAQ,AAAC,KAAK,UAAU,KAAK,UACzB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM;AACd;AACA;;;CAGC,GACD,SAAS,iBAAiB,CAAC;IACvB,OAAQ,AAAC,KAAK,UAAU,KAAK,UACzB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACL,KAAK,UAAU,KAAK,UACpB,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK,UACrB,MAAM,UACN,MAAM,UACN,MAAM,UACN,MAAM,UACL,KAAK,UAAU,KAAK;AAC7B", "ignoreList": [0]}}, {"offset": {"line": 5472, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js"], "sourcesContent": ["import { __spreadArray } from \"tslib\";\nimport { isArgumentElement, isDateElement, isNumberElement, isPluralElement, isSelectElement, isTagElement, isTimeElement, TYPE, } from './types';\nfunction cloneDeep(obj) {\n    if (Array.isArray(obj)) {\n        // @ts-expect-error meh\n        return __spreadArray([], obj.map(cloneDeep), true);\n    }\n    if (obj !== null && typeof obj === 'object') {\n        // @ts-expect-error meh\n        return Object.keys(obj).reduce(function (cloned, k) {\n            // @ts-expect-error meh\n            cloned[k] = cloneDeep(obj[k]);\n            return cloned;\n        }, {});\n    }\n    return obj;\n}\nfunction hoistPluralOrSelectElement(ast, el, positionToInject) {\n    // pull this out of the ast and move it to the top\n    var cloned = cloneDeep(el);\n    var options = cloned.options;\n    cloned.options = Object.keys(options).reduce(function (all, k) {\n        var newValue = hoistSelectors(__spreadArray(__spreadArray(__spreadArray([], ast.slice(0, positionToInject), true), options[k].value, true), ast.slice(positionToInject + 1), true));\n        all[k] = {\n            value: newValue,\n        };\n        return all;\n    }, {});\n    return cloned;\n}\nfunction isPluralOrSelectElement(el) {\n    return isPluralElement(el) || isSelectElement(el);\n}\nfunction findPluralOrSelectElement(ast) {\n    return !!ast.find(function (el) {\n        if (isPluralOrSelectElement(el)) {\n            return true;\n        }\n        if (isTagElement(el)) {\n            return findPluralOrSelectElement(el.children);\n        }\n        return false;\n    });\n}\n/**\n * Hoist all selectors to the beginning of the AST & flatten the\n * resulting options. E.g:\n * \"I have {count, plural, one{a dog} other{many dogs}}\"\n * becomes \"{count, plural, one{I have a dog} other{I have many dogs}}\".\n * If there are multiple selectors, the order of which one is hoisted 1st\n * is non-deterministic.\n * The goal is to provide as many full sentences as possible since fragmented\n * sentences are not translator-friendly\n * @param ast AST\n */\nexport function hoistSelectors(ast) {\n    for (var i = 0; i < ast.length; i++) {\n        var el = ast[i];\n        if (isPluralOrSelectElement(el)) {\n            return [hoistPluralOrSelectElement(ast, el, i)];\n        }\n        if (isTagElement(el) && findPluralOrSelectElement([el])) {\n            throw new Error('Cannot hoist plural/select within a tag element. Please put the tag element inside each plural/select option');\n        }\n    }\n    return ast;\n}\n/**\n * Collect all variables in an AST to Record<string, TYPE>\n * @param ast AST to collect variables from\n * @param vars Record of variable name to variable type\n */\nfunction collectVariables(ast, vars) {\n    if (vars === void 0) { vars = new Map(); }\n    ast.forEach(function (el) {\n        if (isArgumentElement(el) ||\n            isDateElement(el) ||\n            isTimeElement(el) ||\n            isNumberElement(el)) {\n            if (el.value in vars && vars.get(el.value) !== el.type) {\n                throw new Error(\"Variable \".concat(el.value, \" has conflicting types\"));\n            }\n            vars.set(el.value, el.type);\n        }\n        if (isPluralElement(el) || isSelectElement(el)) {\n            vars.set(el.value, el.type);\n            Object.keys(el.options).forEach(function (k) {\n                collectVariables(el.options[k].value, vars);\n            });\n        }\n        if (isTagElement(el)) {\n            vars.set(el.value, el.type);\n            collectVariables(el.children, vars);\n        }\n    });\n}\n/**\n * Check if 2 ASTs are structurally the same. This primarily means that\n * they have the same variables with the same type\n * @param a\n * @param b\n * @returns\n */\nexport function isStructurallySame(a, b) {\n    var aVars = new Map();\n    var bVars = new Map();\n    collectVariables(a, aVars);\n    collectVariables(b, bVars);\n    if (aVars.size !== bVars.size) {\n        return {\n            success: false,\n            error: new Error(\"Different number of variables: [\".concat(Array.from(aVars.keys()).join(', '), \"] vs [\").concat(Array.from(bVars.keys()).join(', '), \"]\")),\n        };\n    }\n    return Array.from(aVars.entries()).reduce(function (result, _a) {\n        var key = _a[0], type = _a[1];\n        if (!result.success) {\n            return result;\n        }\n        var bType = bVars.get(key);\n        if (bType == null) {\n            return {\n                success: false,\n                error: new Error(\"Missing variable \".concat(key, \" in message\")),\n            };\n        }\n        if (bType !== type) {\n            return {\n                success: false,\n                error: new Error(\"Variable \".concat(key, \" has conflicting types: \").concat(TYPE[type], \" vs \").concat(TYPE[bType])),\n            };\n        }\n        return result;\n    }, { success: true });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACA,SAAS,UAAU,GAAG;IAClB,IAAI,MAAM,OAAO,CAAC,MAAM;QACpB,uBAAuB;QACvB,OAAO,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,EAAE,IAAI,GAAG,CAAC,YAAY;IACjD;IACA,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;QACzC,uBAAuB;QACvB,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,SAAU,MAAM,EAAE,CAAC;YAC9C,uBAAuB;YACvB,MAAM,CAAC,EAAE,GAAG,UAAU,GAAG,CAAC,EAAE;YAC5B,OAAO;QACX,GAAG,CAAC;IACR;IACA,OAAO;AACX;AACA,SAAS,2BAA2B,GAAG,EAAE,EAAE,EAAE,gBAAgB;IACzD,kDAAkD;IAClD,IAAI,SAAS,UAAU;IACvB,IAAI,UAAU,OAAO,OAAO;IAC5B,OAAO,OAAO,GAAG,OAAO,IAAI,CAAC,SAAS,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC;QACzD,IAAI,WAAW,eAAe,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE,EAAE,EAAE,IAAI,KAAK,CAAC,GAAG,mBAAmB,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,mBAAmB,IAAI;QAC7K,GAAG,CAAC,EAAE,GAAG;YACL,OAAO;QACX;QACA,OAAO;IACX,GAAG,CAAC;IACJ,OAAO;AACX;AACA,SAAS,wBAAwB,EAAE;IAC/B,OAAO,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE;AAClD;AACA,SAAS,0BAA0B,GAAG;IAClC,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC,SAAU,EAAE;QAC1B,IAAI,wBAAwB,KAAK;YAC7B,OAAO;QACX;QACA,IAAI,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,KAAK;YAClB,OAAO,0BAA0B,GAAG,QAAQ;QAChD;QACA,OAAO;IACX;AACJ;AAYO,SAAS,eAAe,GAAG;IAC9B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACjC,IAAI,KAAK,GAAG,CAAC,EAAE;QACf,IAAI,wBAAwB,KAAK;YAC7B,OAAO;gBAAC,2BAA2B,KAAK,IAAI;aAAG;QACnD;QACA,IAAI,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,OAAO,0BAA0B;YAAC;SAAG,GAAG;YACrD,MAAM,IAAI,MAAM;QACpB;IACJ;IACA,OAAO;AACX;AACA;;;;CAIC,GACD,SAAS,iBAAiB,GAAG,EAAE,IAAI;IAC/B,IAAI,SAAS,KAAK,GAAG;QAAE,OAAO,IAAI;IAAO;IACzC,IAAI,OAAO,CAAC,SAAU,EAAE;QACpB,IAAI,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,OAClB,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,OACd,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,OACd,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YACrB,IAAI,GAAG,KAAK,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,MAAM,GAAG,IAAI,EAAE;gBACpD,MAAM,IAAI,MAAM,YAAY,MAAM,CAAC,GAAG,KAAK,EAAE;YACjD;YACA,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,IAAI;QAC9B;QACA,IAAI,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YAC5C,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,IAAI;YAC1B,OAAO,IAAI,CAAC,GAAG,OAAO,EAAE,OAAO,CAAC,SAAU,CAAC;gBACvC,iBAAiB,GAAG,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE;YAC1C;QACJ;QACA,IAAI,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,KAAK;YAClB,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,IAAI;YAC1B,iBAAiB,GAAG,QAAQ,EAAE;QAClC;IACJ;AACJ;AAQO,SAAS,mBAAmB,CAAC,EAAE,CAAC;IACnC,IAAI,QAAQ,IAAI;IAChB,IAAI,QAAQ,IAAI;IAChB,iBAAiB,GAAG;IACpB,iBAAiB,GAAG;IACpB,IAAI,MAAM,IAAI,KAAK,MAAM,IAAI,EAAE;QAC3B,OAAO;YACH,SAAS;YACT,OAAO,IAAI,MAAM,mCAAmC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,UAAU,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO;QAC1J;IACJ;IACA,OAAO,MAAM,IAAI,CAAC,MAAM,OAAO,IAAI,MAAM,CAAC,SAAU,MAAM,EAAE,EAAE;QAC1D,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;QAC7B,IAAI,CAAC,OAAO,OAAO,EAAE;YACjB,OAAO;QACX;QACA,IAAI,QAAQ,MAAM,GAAG,CAAC;QACtB,IAAI,SAAS,MAAM;YACf,OAAO;gBACH,SAAS;gBACT,OAAO,IAAI,MAAM,oBAAoB,MAAM,CAAC,KAAK;YACrD;QACJ;QACA,IAAI,UAAU,MAAM;YAChB,OAAO;gBACH,SAAS;gBACT,OAAO,IAAI,MAAM,YAAY,MAAM,CAAC,KAAK,4BAA4B,MAAM,CAAC,oLAAA,CAAA,OAAI,CAAC,KAAK,EAAE,QAAQ,MAAM,CAAC,oLAAA,CAAA,OAAI,CAAC,MAAM;YACtH;QACJ;QACA,OAAO;IACX,GAAG;QAAE,SAAS;IAAK;AACvB", "ignoreList": [0]}}, {"offset": {"line": 5605, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-messageformat-parser/lib/index.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { ErrorKind } from './error';\nimport { Parser } from './parser';\nimport { isDateElement, isDateTimeSkeleton, isNumberElement, isNumberSkeleton, isPluralElement, isSelectElement, isTagElement, isTimeElement, } from './types';\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if (isSelectElement(el) || isPluralElement(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if (isNumberElement(el) && isNumberSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if ((isDateElement(el) || isTimeElement(el)) &&\n            isDateTimeSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if (isTagElement(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nexport function parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = __assign({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\nexport * from './types';\n// only for testing\nexport var _Parser = Parser;\nexport { isStructurallySame } from './manipulator';\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AA0CA;;;;;AAzCA,SAAS,cAAc,GAAG;IACtB,IAAI,OAAO,CAAC,SAAU,EAAE;QACpB,OAAO,GAAG,QAAQ;QAClB,IAAI,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YAC5C,IAAK,IAAI,KAAK,GAAG,OAAO,CAAE;gBACtB,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,QAAQ;gBAC7B,cAAc,GAAG,OAAO,CAAC,EAAE,CAAC,KAAK;YACrC;QACJ,OACK,IAAI,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,KAAK,GAAG;YACxD,OAAO,GAAG,KAAK,CAAC,QAAQ;QAC5B,OACK,IAAI,CAAC,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,GAAG,KAC5C,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE,GAAG,KAAK,GAAG;YAC9B,OAAO,GAAG,KAAK,CAAC,QAAQ;QAC5B,OACK,IAAI,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,KAAK;YACvB,cAAc,GAAG,QAAQ;QAC7B;IACJ;AACJ;AACO,SAAS,MAAM,OAAO,EAAE,IAAI;IAC/B,IAAI,SAAS,KAAK,GAAG;QAAE,OAAO,CAAC;IAAG;IAClC,OAAO,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,sBAAsB;QAAM,qBAAqB;IAAK,GAAG;IAC3E,IAAI,SAAS,IAAI,qLAAA,CAAA,SAAM,CAAC,SAAS,MAAM,KAAK;IAC5C,IAAI,OAAO,GAAG,EAAE;QACZ,IAAI,QAAQ,YAAY,oLAAA,CAAA,YAAS,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC;QAClD,0CAA0C;QAC1C,MAAM,QAAQ,GAAG,OAAO,GAAG,CAAC,QAAQ;QACpC,0CAA0C;QAC1C,MAAM,eAAe,GAAG,OAAO,GAAG,CAAC,OAAO;QAC1C,MAAM;IACV;IACA,IAAI,CAAC,CAAC,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,eAAe,GAAG;QACrE,cAAc,OAAO,GAAG;IAC5B;IACA,OAAO,OAAO,GAAG;AACrB;;AAGO,IAAI,UAAU,qLAAA,CAAA,SAAM", "ignoreList": [0]}}, {"offset": {"line": 5678, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js"], "sourcesContent": ["/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nexport function parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;AACD,IAAI,kBAAkB;AAOf,SAAS,sBAAsB,QAAQ;IAC1C,IAAI,SAAS,CAAC;IACd,SAAS,OAAO,CAAC,iBAAiB,SAAU,KAAK;QAC7C,IAAI,MAAM,MAAM,MAAM;QACtB,OAAQ,KAAK,CAAC,EAAE;YACZ,MAAM;YACN,KAAK;gBACD,OAAO,GAAG,GAAG,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;gBACzD;YACJ,OAAO;YACP,KAAK;gBACD,OAAO,IAAI,GAAG,QAAQ,IAAI,YAAY;gBACtC;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,UAAU;YACV,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,QAAQ;YACR,KAAK;YACL,KAAK;gBACD,OAAO,KAAK,GAAG;oBAAC;oBAAW;oBAAW;oBAAS;oBAAQ;iBAAS,CAAC,MAAM,EAAE;gBACzE;YACJ,OAAO;YACP,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,KAAK;gBACD,OAAO,GAAG,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC5C;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,UAAU;YACV,KAAK;gBACD,OAAO,OAAO,GAAG,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;gBAC7D;YACJ,KAAK;gBACD,IAAI,MAAM,GAAG;oBACT,MAAM,IAAI,WAAW;gBACzB;gBACA,OAAO,OAAO,GAAG;oBAAC;oBAAS;oBAAQ;oBAAU;iBAAQ,CAAC,MAAM,EAAE;gBAC9D;YACJ,KAAK;gBACD,IAAI,MAAM,GAAG;oBACT,MAAM,IAAI,WAAW;gBACzB;gBACA,OAAO,OAAO,GAAG;oBAAC;oBAAS;oBAAQ;oBAAU;iBAAQ,CAAC,MAAM,EAAE;gBAC9D;YACJ,SAAS;YACT,KAAK;gBACD,OAAO,MAAM,GAAG;gBAChB;YACJ,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,OAAO;YACP,KAAK;gBACD,OAAO,SAAS,GAAG;gBACnB,OAAO,IAAI,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC7C;YACJ,KAAK;gBACD,OAAO,SAAS,GAAG;gBACnB,OAAO,IAAI,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC7C;YACJ,KAAK;gBACD,OAAO,SAAS,GAAG;gBACnB,OAAO,IAAI,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC7C;YACJ,KAAK;gBACD,OAAO,SAAS,GAAG;gBACnB,OAAO,IAAI,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC7C;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,SAAS;YACT,KAAK;gBACD,OAAO,MAAM,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC/C;YACJ,SAAS;YACT,KAAK;gBACD,OAAO,MAAM,GAAG;oBAAC;oBAAW;iBAAU,CAAC,MAAM,EAAE;gBAC/C;YACJ,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;YACzB,OAAO;YACP,KAAK;gBACD,OAAO,YAAY,GAAG,MAAM,IAAI,UAAU;gBAC1C;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,MAAM,IAAI,WAAW;QAC7B;QACA,OAAO;IACX;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 5838, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js"], "sourcesContent": ["// @generated from regex-gen.ts\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n"], "names": [], "mappings": "AAAA,+BAA+B;;;;AACxB,IAAI,oBAAoB", "ignoreList": [0]}}, {"offset": {"line": 5849, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-skeleton-parser/lib/number.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { WHITE_SPACE_REGEX } from './regex.generated';\nexport function parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nexport function parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = __assign(__assign(__assign({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = __assign(__assign(__assign({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = __assign(__assign({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = __assign(__assign({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = __assign(__assign({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = __assign(__assign({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = __assign(__assign({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,SAAS,8BAA8B,QAAQ;IAClD,IAAI,SAAS,MAAM,KAAK,GAAG;QACvB,MAAM,IAAI,MAAM;IACpB;IACA,qBAAqB;IACrB,IAAI,eAAe,SACd,KAAK,CAAC,4LAAA,CAAA,oBAAiB,EACvB,MAAM,CAAC,SAAU,CAAC;QAAI,OAAO,EAAE,MAAM,GAAG;IAAG;IAChD,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,KAAK,GAAG,iBAAiB,cAAc,KAAK,eAAe,MAAM,EAAE,KAAM;QAC9E,IAAI,cAAc,cAAc,CAAC,GAAG;QACpC,IAAI,iBAAiB,YAAY,KAAK,CAAC;QACvC,IAAI,eAAe,MAAM,KAAK,GAAG;YAC7B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,OAAO,cAAc,CAAC,EAAE,EAAE,UAAU,eAAe,KAAK,CAAC;QAC7D,IAAK,IAAI,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,MAAM,EAAE,KAAM;YAC/D,IAAI,SAAS,SAAS,CAAC,GAAG;YAC1B,IAAI,OAAO,MAAM,KAAK,GAAG;gBACrB,MAAM,IAAI,MAAM;YACpB;QACJ;QACA,OAAO,IAAI,CAAC;YAAE,MAAM;YAAM,SAAS;QAAQ;IAC/C;IACA,OAAO;AACX;AACA,SAAS,cAAc,IAAI;IACvB,OAAO,KAAK,OAAO,CAAC,WAAW;AACnC;AACA,IAAI,2BAA2B;AAC/B,IAAI,8BAA8B;AAClC,IAAI,sBAAsB;AAC1B,IAAI,8BAA8B;AAClC,SAAS,0BAA0B,GAAG;IAClC,IAAI,SAAS,CAAC;IACd,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,KAAK,KAAK;QAC7B,OAAO,gBAAgB,GAAG;IAC9B,OACK,IAAI,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,KAAK,KAAK;QAClC,OAAO,gBAAgB,GAAG;IAC9B;IACA,IAAI,OAAO,CAAC,6BAA6B,SAAU,CAAC,EAAE,EAAE,EAAE,EAAE;QACxD,WAAW;QACX,IAAI,OAAO,OAAO,UAAU;YACxB,OAAO,wBAAwB,GAAG,GAAG,MAAM;YAC3C,OAAO,wBAAwB,GAAG,GAAG,MAAM;QAC/C,OAEK,IAAI,OAAO,KAAK;YACjB,OAAO,wBAAwB,GAAG,GAAG,MAAM;QAC/C,OAEK,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK;YACpB,OAAO,wBAAwB,GAAG,GAAG,MAAM;QAC/C,OAEK;YACD,OAAO,wBAAwB,GAAG,GAAG,MAAM;YAC3C,OAAO,wBAAwB,GAC3B,GAAG,MAAM,GAAG,CAAC,OAAO,OAAO,WAAW,GAAG,MAAM,GAAG,CAAC;QAC3D;QACA,OAAO;IACX;IACA,OAAO;AACX;AACA,SAAS,UAAU,GAAG;IAClB,OAAQ;QACJ,KAAK;YACD,OAAO;gBACH,aAAa;YACjB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,cAAc;YAClB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,aAAa;YACjB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,aAAa;gBACb,cAAc;YAClB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,aAAa;YACjB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,aAAa;gBACb,cAAc;YAClB;QACJ,KAAK;QACL,KAAK;YACD,OAAO;gBACH,aAAa;YACjB;IACR;AACJ;AACA,SAAS,yCAAyC,IAAI;IAClD,cAAc;IACd,IAAI;IACJ,IAAI,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,CAAC,EAAE,KAAK,KAAK;QACpC,SAAS;YACL,UAAU;QACd;QACA,OAAO,KAAK,KAAK,CAAC;IACtB,OACK,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK;QACtB,SAAS;YACL,UAAU;QACd;QACA,OAAO,KAAK,KAAK,CAAC;IACtB;IACA,IAAI,QAAQ;QACR,IAAI,cAAc,KAAK,KAAK,CAAC,GAAG;QAChC,IAAI,gBAAgB,MAAM;YACtB,OAAO,WAAW,GAAG;YACrB,OAAO,KAAK,KAAK,CAAC;QACtB,OACK,IAAI,gBAAgB,MAAM;YAC3B,OAAO,WAAW,GAAG;YACrB,OAAO,KAAK,KAAK,CAAC;QACtB;QACA,IAAI,CAAC,4BAA4B,IAAI,CAAC,OAAO;YACzC,MAAM,IAAI,MAAM;QACpB;QACA,OAAO,oBAAoB,GAAG,KAAK,MAAM;IAC7C;IACA,OAAO;AACX;AACA,SAAS,qBAAqB,GAAG;IAC7B,IAAI,SAAS,CAAC;IACd,IAAI,WAAW,UAAU;IACzB,IAAI,UAAU;QACV,OAAO;IACX;IACA,OAAO;AACX;AAIO,SAAS,oBAAoB,MAAM;IACtC,IAAI,SAAS,CAAC;IACd,IAAK,IAAI,KAAK,GAAG,WAAW,QAAQ,KAAK,SAAS,MAAM,EAAE,KAAM;QAC5D,IAAI,QAAQ,QAAQ,CAAC,GAAG;QACxB,OAAQ,MAAM,IAAI;YACd,KAAK;YACL,KAAK;gBACD,OAAO,KAAK,GAAG;gBACf;YACJ,KAAK;gBACD,OAAO,KAAK,GAAG;gBACf,OAAO,KAAK,GAAG;gBACf;YACJ,KAAK;gBACD,OAAO,KAAK,GAAG;gBACf,OAAO,QAAQ,GAAG,MAAM,OAAO,CAAC,EAAE;gBAClC;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,WAAW,GAAG;gBACrB;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,qBAAqB,GAAG;gBAC/B;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,KAAK,GAAG;gBACf,OAAO,IAAI,GAAG,cAAc,MAAM,OAAO,CAAC,EAAE;gBAC5C;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,QAAQ,GAAG;gBAClB,OAAO,cAAc,GAAG;gBACxB;YACJ,KAAK;YACL,KAAK;gBACD,OAAO,QAAQ,GAAG;gBAClB,OAAO,cAAc,GAAG;gBACxB;YACJ,KAAK;gBACD,SAAS,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS;oBAAE,UAAU;gBAAa,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,SAAU,GAAG,EAAE,GAAG;oBAAI,OAAQ,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,qBAAqB;gBAAQ,GAAG,CAAC;gBAC9L;YACJ,KAAK;gBACD,SAAS,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS;oBAAE,UAAU;gBAAc,IAAI,MAAM,OAAO,CAAC,MAAM,CAAC,SAAU,GAAG,EAAE,GAAG;oBAAI,OAAQ,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,MAAM,qBAAqB;gBAAQ,GAAG,CAAC;gBAC/L;YACJ,KAAK;gBACD,OAAO,QAAQ,GAAG;gBAClB;YACJ,8FAA8F;YAC9F,KAAK;gBACD,OAAO,eAAe,GAAG;gBACzB,OAAO,WAAW,GAAG;gBACrB;YACJ,KAAK;gBACD,OAAO,eAAe,GAAG;gBACzB,OAAO,WAAW,GAAG;gBACrB;YACJ,KAAK;gBACD,OAAO,eAAe,GAAG;gBACzB,OAAO,WAAW,GAAG;gBACrB;YACJ,KAAK;gBACD,OAAO,eAAe,GAAG;gBACzB;YACJ,KAAK;gBACD,OAAO,KAAK,GAAG,WAAW,MAAM,OAAO,CAAC,EAAE;gBAC1C;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,KAAK;gBACD,OAAO,YAAY,GAAG;gBACtB;YACJ,gGAAgG;YAChG,KAAK;gBACD,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG;oBAC1B,MAAM,IAAI,WAAW;gBACzB;gBACA,MAAM,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,qBAAqB,SAAU,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;oBACzE,IAAI,IAAI;wBACJ,OAAO,oBAAoB,GAAG,GAAG,MAAM;oBAC3C,OACK,IAAI,MAAM,IAAI;wBACf,MAAM,IAAI,MAAM;oBACpB,OACK,IAAI,IAAI;wBACT,MAAM,IAAI,MAAM;oBACpB;oBACA,OAAO;gBACX;gBACA;QACR;QACA,gGAAgG;QAChG,IAAI,4BAA4B,IAAI,CAAC,MAAM,IAAI,GAAG;YAC9C,OAAO,oBAAoB,GAAG,MAAM,IAAI,CAAC,MAAM;YAC/C;QACJ;QACA,IAAI,yBAAyB,IAAI,CAAC,MAAM,IAAI,GAAG;YAC3C,YAAY;YACZ,qGAAqG;YACrG,yBAAyB;YACzB,IAAI,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG;gBAC1B,MAAM,IAAI,WAAW;YACzB;YACA,MAAM,IAAI,CAAC,OAAO,CAAC,0BAA0B,SAAU,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBACxE,yCAAyC;gBACzC,IAAI,OAAO,KAAK;oBACZ,OAAO,qBAAqB,GAAG,GAAG,MAAM;gBAC5C,OAEK,IAAI,MAAM,EAAE,CAAC,EAAE,KAAK,KAAK;oBAC1B,OAAO,qBAAqB,GAAG,GAAG,MAAM;gBAC5C,OAEK,IAAI,MAAM,IAAI;oBACf,OAAO,qBAAqB,GAAG,GAAG,MAAM;oBACxC,OAAO,qBAAqB,GAAG,GAAG,MAAM,GAAG,GAAG,MAAM;gBACxD,OACK;oBACD,OAAO,qBAAqB,GAAG,GAAG,MAAM;oBACxC,OAAO,qBAAqB,GAAG,GAAG,MAAM;gBAC5C;gBACA,OAAO;YACX;YACA,IAAI,MAAM,MAAM,OAAO,CAAC,EAAE;YAC1B,wGAAwG;YACxG,IAAI,QAAQ,KAAK;gBACb,SAAS,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS;oBAAE,qBAAqB;gBAAiB;YACpF,OACK,IAAI,KAAK;gBACV,SAAS,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS,0BAA0B;YACtE;YACA;QACJ;QACA,+GAA+G;QAC/G,IAAI,4BAA4B,IAAI,CAAC,MAAM,IAAI,GAAG;YAC9C,SAAS,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS,0BAA0B,MAAM,IAAI;YAC5E;QACJ;QACA,IAAI,WAAW,UAAU,MAAM,IAAI;QACnC,IAAI,UAAU;YACV,SAAS,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS;QAC5C;QACA,IAAI,sCAAsC,yCAAyC,MAAM,IAAI;QAC7F,IAAI,qCAAqC;YACrC,SAAS,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,SAAS;QAC5C;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 6169, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/@formatjs/icu-skeleton-parser/lib/index.js"], "sourcesContent": ["export * from './date-time';\nexport * from './number';\n"], "names": [], "mappings": ";AAAA;AACA", "ignoreList": [0]}}, {"offset": {"line": 6190, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/intl-messageformat/lib/src/error.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nexport var ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    __extends(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\nexport { FormatError };\nvar InvalidValueError = /** @class */ (function (_super) {\n    __extends(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\nexport { InvalidValueError };\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    __extends(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\nexport { InvalidValueTypeError };\nvar MissingValueError = /** @class */ (function (_super) {\n    __extends(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\nexport { MissingValueError };\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,IAAI;AACX,CAAC,SAAU,SAAS;IAChB,oDAAoD;IACpD,SAAS,CAAC,gBAAgB,GAAG;IAC7B,iCAAiC;IACjC,SAAS,CAAC,gBAAgB,GAAG;IAC7B,wDAAwD;IACxD,SAAS,CAAC,mBAAmB,GAAG;AACpC,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,IAAI,cAA6B,SAAU,MAAM;IAC7C,CAAA,GAAA,8IAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IACvB,SAAS,YAAY,GAAG,EAAE,IAAI,EAAE,eAAe;QAC3C,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,QAAQ,IAAI;QAC1C,MAAM,IAAI,GAAG;QACb,MAAM,eAAe,GAAG;QACxB,OAAO;IACX;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC7B,OAAO,oBAAoB,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,MAAM,CAAC,IAAI,CAAC,OAAO;IAC1E;IACA,OAAO;AACX,EAAE;;AAEF,IAAI,oBAAmC,SAAU,MAAM;IACnD,CAAA,GAAA,8IAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB;IAC7B,SAAS,kBAAkB,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe;QAClE,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,wBAAwB,MAAM,CAAC,YAAY,UAAU,MAAM,CAAC,OAAO,sBAAsB,MAAM,CAAC,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,OAAO,UAAU,aAAa,EAAE,oBAAoB,IAAI;IACxN;IACA,OAAO;AACX,EAAE;;AAEF,IAAI,wBAAuC,SAAU,MAAM;IACvD,CAAA,GAAA,8IAAA,CAAA,YAAS,AAAD,EAAE,uBAAuB;IACjC,SAAS,sBAAsB,KAAK,EAAE,IAAI,EAAE,eAAe;QACvD,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,eAAe,MAAM,CAAC,OAAO,uBAAuB,MAAM,CAAC,OAAO,UAAU,aAAa,EAAE,oBAAoB,IAAI;IAChJ;IACA,OAAO;AACX,EAAE;;AAEF,IAAI,oBAAmC,SAAU,MAAM;IACnD,CAAA,GAAA,8IAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB;IAC7B,SAAS,kBAAkB,UAAU,EAAE,eAAe;QAClD,OAAO,OAAO,IAAI,CAAC,IAAI,EAAE,sCAAsC,MAAM,CAAC,YAAY,wCAAwC,MAAM,CAAC,iBAAiB,OAAO,UAAU,aAAa,EAAE,oBAAoB,IAAI;IAC9M;IACA,OAAO;AACX,EAAE", "ignoreList": [0]}}, {"offset": {"line": 6252, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/intl-messageformat/lib/src/formatters.js"], "sourcesContent": ["import { isArgumentElement, isDateElement, isDateTimeSkeleton, isLiteralElement, isNumberElement, isNumberSkeleton, isPluralElement, isPoundElement, isSelectElement, isTagElement, isTimeElement, } from '@formatjs/icu-messageformat-parser';\nimport { ErrorCode, FormatError, InvalidValueError, InvalidValueTypeError, MissingValueError, } from './error';\nexport var PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nexport function isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nexport function formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && isLiteralElement(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if (isLiteralElement(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if (isPoundElement(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if (isArgumentElement(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if (isDateElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTimeElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isNumberElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : isNumberSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTagElement(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if (isSelectElement(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if (isPluralElement(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;;;AACO,IAAI;AACX,CAAC,SAAU,SAAS;IAChB,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,GAAG;IACtC,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,GAAG;AACzC,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,SAAS,aAAa,KAAK;IACvB,IAAI,MAAM,MAAM,GAAG,GAAG;QAClB,OAAO;IACX;IACA,OAAO,MAAM,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;QACnC,IAAI,WAAW,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE;QAClC,IAAI,CAAC,YACD,SAAS,IAAI,KAAK,UAAU,OAAO,IACnC,KAAK,IAAI,KAAK,UAAU,OAAO,EAAE;YACjC,IAAI,IAAI,CAAC;QACb,OACK;YACD,SAAS,KAAK,IAAI,KAAK,KAAK;QAChC;QACA,OAAO;IACX,GAAG,EAAE;AACT;AACO,SAAS,qBAAqB,EAAE;IACnC,OAAO,OAAO,OAAO;AACzB;AAEO,SAAS,cAAc,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,kBAAkB,EAC3F,gBAAgB;AAChB,eAAe;IACX,gDAAgD;IAChD,IAAI,IAAI,MAAM,KAAK,KAAK,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,CAAC,EAAE,GAAG;QAC9C,OAAO;YACH;gBACI,MAAM,UAAU,OAAO;gBACvB,OAAO,GAAG,CAAC,EAAE,CAAC,KAAK;YACvB;SACH;IACL;IACA,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,KAAK,GAAG,QAAQ,KAAK,KAAK,MAAM,MAAM,EAAE,KAAM;QACnD,IAAI,KAAK,KAAK,CAAC,GAAG;QAClB,+BAA+B;QAC/B,IAAI,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK;YACtB,OAAO,IAAI,CAAC;gBACR,MAAM,UAAU,OAAO;gBACvB,OAAO,GAAG,KAAK;YACnB;YACA;QACJ;QACA,0CAA0C;QAC1C,6DAA6D;QAC7D,IAAI,CAAA,GAAA,oLAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;YACpB,IAAI,OAAO,uBAAuB,UAAU;gBACxC,OAAO,IAAI,CAAC;oBACR,MAAM,UAAU,OAAO;oBACvB,OAAO,WAAW,eAAe,CAAC,SAAS,MAAM,CAAC;gBACtD;YACJ;YACA;QACJ;QACA,IAAI,UAAU,GAAG,KAAK;QACtB,+DAA+D;QAC/D,IAAI,CAAC,CAAC,UAAU,WAAW,MAAM,GAAG;YAChC,MAAM,IAAI,oKAAA,CAAA,oBAAiB,CAAC,SAAS;QACzC;QACA,IAAI,QAAQ,MAAM,CAAC,QAAQ;QAC3B,IAAI,CAAA,GAAA,oLAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK;YACvB,IAAI,CAAC,SAAS,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;gBAClE,QACI,OAAO,UAAU,YAAY,OAAO,UAAU,WACxC,OAAO,SACP;YACd;YACA,OAAO,IAAI,CAAC;gBACR,MAAM,OAAO,UAAU,WAAW,UAAU,OAAO,GAAG,UAAU,MAAM;gBACtE,OAAO;YACX;YACA;QACJ;QACA,sEAAsE;QACtE,iEAAiE;QACjE,yDAAyD;QACzD,IAAI,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;YACnB,IAAI,QAAQ,OAAO,GAAG,KAAK,KAAK,WAC1B,QAAQ,IAAI,CAAC,GAAG,KAAK,CAAC,GACtB,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE,GAAG,KAAK,IACvB,GAAG,KAAK,CAAC,aAAa,GACtB;YACV,OAAO,IAAI,CAAC;gBACR,MAAM,UAAU,OAAO;gBACvB,OAAO,WACF,iBAAiB,CAAC,SAAS,OAC3B,MAAM,CAAC;YAChB;YACA;QACJ;QACA,IAAI,CAAA,GAAA,oLAAA,CAAA,gBAAa,AAAD,EAAE,KAAK;YACnB,IAAI,QAAQ,OAAO,GAAG,KAAK,KAAK,WAC1B,QAAQ,IAAI,CAAC,GAAG,KAAK,CAAC,GACtB,CAAA,GAAA,oLAAA,CAAA,qBAAkB,AAAD,EAAE,GAAG,KAAK,IACvB,GAAG,KAAK,CAAC,aAAa,GACtB,QAAQ,IAAI,CAAC,MAAM;YAC7B,OAAO,IAAI,CAAC;gBACR,MAAM,UAAU,OAAO;gBACvB,OAAO,WACF,iBAAiB,CAAC,SAAS,OAC3B,MAAM,CAAC;YAChB;YACA;QACJ;QACA,IAAI,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YACrB,IAAI,QAAQ,OAAO,GAAG,KAAK,KAAK,WAC1B,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC,GACxB,CAAA,GAAA,oLAAA,CAAA,mBAAgB,AAAD,EAAE,GAAG,KAAK,IACrB,GAAG,KAAK,CAAC,aAAa,GACtB;YACV,IAAI,SAAS,MAAM,KAAK,EAAE;gBACtB,QACI,QACI,CAAC,MAAM,KAAK,IAAI,CAAC;YAC7B;YACA,OAAO,IAAI,CAAC;gBACR,MAAM,UAAU,OAAO;gBACvB,OAAO,WACF,eAAe,CAAC,SAAS,OACzB,MAAM,CAAC;YAChB;YACA;QACJ;QACA,IAAI,CAAA,GAAA,oLAAA,CAAA,eAAY,AAAD,EAAE,KAAK;YAClB,IAAI,WAAW,GAAG,QAAQ,EAAE,UAAU,GAAG,KAAK;YAC9C,IAAI,WAAW,MAAM,CAAC,QAAQ;YAC9B,IAAI,CAAC,qBAAqB,WAAW;gBACjC,MAAM,IAAI,oKAAA,CAAA,wBAAqB,CAAC,SAAS,YAAY;YACzD;YACA,IAAI,QAAQ,cAAc,UAAU,SAAS,YAAY,SAAS,QAAQ;YAC1E,IAAI,SAAS,SAAS,MAAM,GAAG,CAAC,SAAU,CAAC;gBAAI,OAAO,EAAE,KAAK;YAAE;YAC/D,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS;gBACxB,SAAS;oBAAC;iBAAO;YACrB;YACA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,OAAO,GAAG,CAAC,SAAU,CAAC;gBAC5C,OAAO;oBACH,MAAM,OAAO,MAAM,WAAW,UAAU,OAAO,GAAG,UAAU,MAAM;oBAClE,OAAO;gBACX;YACJ;QACJ;QACA,IAAI,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YACrB,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,GAAG,OAAO,CAAC,KAAK;YAC/C,IAAI,CAAC,KAAK;gBACN,MAAM,IAAI,oKAAA,CAAA,oBAAiB,CAAC,GAAG,KAAK,EAAE,OAAO,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG;YAC1E;YACA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,cAAc,IAAI,KAAK,EAAE,SAAS,YAAY,SAAS;YACjF;QACJ;QACA,IAAI,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK;YACrB,IAAI,MAAM,GAAG,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO;YACvC,IAAI,CAAC,KAAK;gBACN,IAAI,CAAC,KAAK,WAAW,EAAE;oBACnB,MAAM,IAAI,oKAAA,CAAA,cAAW,CAAC,qHAAqH,oKAAA,CAAA,YAAS,CAAC,gBAAgB,EAAE;gBAC3K;gBACA,IAAI,OAAO,WACN,cAAc,CAAC,SAAS;oBAAE,MAAM,GAAG,UAAU;gBAAC,GAC9C,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;gBACnC,MAAM,GAAG,OAAO,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,KAAK;YAC9C;YACA,IAAI,CAAC,KAAK;gBACN,MAAM,IAAI,oKAAA,CAAA,oBAAiB,CAAC,GAAG,KAAK,EAAE,OAAO,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG;YAC1E;YACA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,cAAc,IAAI,KAAK,EAAE,SAAS,YAAY,SAAS,QAAQ,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;YAChH;QACJ;IACJ;IACA,OAAO,aAAa;AACxB", "ignoreList": [0]}}, {"offset": {"line": 6419, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/intl-messageformat/lib/src/core.js"], "sourcesContent": ["/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nimport { __assign, __rest, __spreadArray } from \"tslib\";\nimport { memoize, strategies } from '@formatjs/fast-memoize';\nimport { parse, } from '@formatjs/icu-messageformat-parser';\nimport { formatToParts, PART_TYPE, } from './formatters';\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return __assign(__assign(__assign({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = __assign(__assign({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, __assign({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: strategies.variadic,\n        }),\n        getDateTimeFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: strategies.variadic,\n        }),\n        getPluralRules: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return formatToParts(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = __rest(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, __assign(__assign({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\nexport { IntlMessageFormat };\n"], "names": [], "mappings": "AAAA;;;;AAIA;;;AACA;AACA;AACA;AAAA;AACA;;;;;AACA,4EAA4E;AAC5E,SAAS,YAAY,EAAE,EAAE,EAAE;IACvB,IAAI,CAAC,IAAI;QACL,OAAO;IACX;IACA,OAAO,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAI,MAAM,CAAC,IAAM,MAAM,CAAC,IAAK,OAAO,IAAI,CAAC,IAAI,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC;QACnG,GAAG,CAAC,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAI,EAAE,CAAC,EAAE,IAAI,CAAC;QAClD,OAAO;IACX,GAAG,CAAC;AACR;AACA,SAAS,aAAa,aAAa,EAAE,OAAO;IACxC,IAAI,CAAC,SAAS;QACV,OAAO;IACX;IACA,OAAO,OAAO,IAAI,CAAC,eAAe,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC;QACrD,GAAG,CAAC,EAAE,GAAG,YAAY,aAAa,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;QACjD,OAAO;IACX,GAAG,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG;AACpB;AACA,SAAS,uBAAuB,KAAK;IACjC,OAAO;QACH,QAAQ;YACJ,OAAO;gBACH,KAAK,SAAU,GAAG;oBACd,OAAO,KAAK,CAAC,IAAI;gBACrB;gBACA,KAAK,SAAU,GAAG,EAAE,KAAK;oBACrB,KAAK,CAAC,IAAI,GAAG;gBACjB;YACJ;QACJ;IACJ;AACJ;AACA,SAAS,wBAAwB,KAAK;IAClC,IAAI,UAAU,KAAK,GAAG;QAAE,QAAQ;YAC5B,QAAQ,CAAC;YACT,UAAU,CAAC;YACX,aAAa,CAAC;QAClB;IAAG;IACH,OAAO;QACH,iBAAiB,CAAA,GAAA,qKAAA,CAAA,UAAO,AAAD,EAAE;YACrB,IAAI;YACJ,IAAI,OAAO,EAAE;YACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;gBAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;YAC5B;YACA,OAAO,IAAI,CAAC,CAAC,KAAK,KAAK,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE;gBAAC,KAAK;aAAE,EAAE,MAAM,OAAO;QAC7F,GAAG;YACC,OAAO,uBAAuB,MAAM,MAAM;YAC1C,UAAU,qKAAA,CAAA,aAAU,CAAC,QAAQ;QACjC;QACA,mBAAmB,CAAA,GAAA,qKAAA,CAAA,UAAO,AAAD,EAAE;YACvB,IAAI;YACJ,IAAI,OAAO,EAAE;YACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;gBAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;YAC5B;YACA,OAAO,IAAI,CAAC,CAAC,KAAK,KAAK,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE;gBAAC,KAAK;aAAE,EAAE,MAAM,OAAO;QAC/F,GAAG;YACC,OAAO,uBAAuB,MAAM,QAAQ;YAC5C,UAAU,qKAAA,CAAA,aAAU,CAAC,QAAQ;QACjC;QACA,gBAAgB,CAAA,GAAA,qKAAA,CAAA,UAAO,AAAD,EAAE;YACpB,IAAI;YACJ,IAAI,OAAO,EAAE;YACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;gBAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;YAC5B;YACA,OAAO,IAAI,CAAC,CAAC,KAAK,KAAK,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAA,GAAA,8IAAA,CAAA,gBAAa,AAAD,EAAE;gBAAC,KAAK;aAAE,EAAE,MAAM,OAAO;QAC5F,GAAG;YACC,OAAO,uBAAuB,MAAM,WAAW;YAC/C,UAAU,qKAAA,CAAA,aAAU,CAAC,QAAQ;QACjC;IACJ;AACJ;AACA,IAAI,oBAAmC;IACnC,SAAS,kBAAkB,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,IAAI;QAC9D,IAAI,YAAY,KAAK,GAAG;YAAE,UAAU,kBAAkB,aAAa;QAAE;QACrE,IAAI,QAAQ,IAAI;QAChB,IAAI,CAAC,cAAc,GAAG;YAClB,QAAQ,CAAC;YACT,UAAU,CAAC;YACX,aAAa,CAAC;QAClB;QACA,IAAI,CAAC,MAAM,GAAG,SAAU,MAAM;YAC1B,IAAI,QAAQ,MAAM,aAAa,CAAC;YAChC,gDAAgD;YAChD,IAAI,MAAM,MAAM,KAAK,GAAG;gBACpB,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK;YACzB;YACA,IAAI,SAAS,MAAM,MAAM,CAAC,SAAU,GAAG,EAAE,IAAI;gBACzC,IAAI,CAAC,IAAI,MAAM,IACX,KAAK,IAAI,KAAK,yKAAA,CAAA,YAAS,CAAC,OAAO,IAC/B,OAAO,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,KAAK,UAAU;oBACzC,IAAI,IAAI,CAAC,KAAK,KAAK;gBACvB,OACK;oBACD,GAAG,CAAC,IAAI,MAAM,GAAG,EAAE,IAAI,KAAK,KAAK;gBACrC;gBACA,OAAO;YACX,GAAG,EAAE;YACL,IAAI,OAAO,MAAM,IAAI,GAAG;gBACpB,OAAO,MAAM,CAAC,EAAE,IAAI;YACxB;YACA,OAAO;QACX;QACA,IAAI,CAAC,aAAa,GAAG,SAAU,MAAM;YACjC,OAAO,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,GAAG,EAAE,MAAM,OAAO,EAAE,MAAM,UAAU,EAAE,MAAM,OAAO,EAAE,QAAQ,WAAW,MAAM,OAAO;QACpH;QACA,IAAI,CAAC,eAAe,GAAG;YACnB,IAAI;YACJ,OAAQ;gBACJ,QAAQ,CAAC,CAAC,KAAK,MAAM,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,QAAQ,EAAE,KACnF,KAAK,YAAY,CAAC,kBAAkB,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE;YAC9D;QACJ;QACA,IAAI,CAAC,MAAM,GAAG;YAAc,OAAO,MAAM,GAAG;QAAE;QAC9C,+DAA+D;QAC/D,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,cAAc,GAAG,kBAAkB,aAAa,CAAC;QACtD,IAAI,OAAO,YAAY,UAAU;YAC7B,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,kBAAkB,OAAO,EAAE;gBAC5B,MAAM,IAAI,UAAU;YACxB;YACA,IAAI,KAAK,QAAQ,CAAC,GAAG,aAAa,GAAG,UAAU,EAAE,YAAY,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,IAAI;gBAAC;aAAa;YACtF,qCAAqC;YACrC,IAAI,CAAC,GAAG,GAAG,kBAAkB,OAAO,CAAC,SAAS,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,YAAY;gBAAE,QAAQ,IAAI,CAAC,cAAc;YAAC;QAClH,OACK;YACD,IAAI,CAAC,GAAG,GAAG;QACf;QACA,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG;YAC1B,MAAM,IAAI,UAAU;QACxB;QACA,4EAA4E;QAC5E,WAAW;QACX,IAAI,CAAC,OAAO,GAAG,aAAa,kBAAkB,OAAO,EAAE;QACvD,IAAI,CAAC,UAAU,GACX,AAAC,QAAQ,KAAK,UAAU,IAAK,wBAAwB,IAAI,CAAC,cAAc;IAChF;IACA,OAAO,cAAc,CAAC,mBAAmB,iBAAiB;QACtD,KAAK;YACD,IAAI,CAAC,kBAAkB,qBAAqB,EAAE;gBAC1C,kBAAkB,qBAAqB,GACnC,IAAI,KAAK,YAAY,GAAG,eAAe,GAAG,MAAM;YACxD;YACA,OAAO,kBAAkB,qBAAqB;QAClD;QACA,YAAY;QACZ,cAAc;IAClB;IACA,kBAAkB,qBAAqB,GAAG;IAC1C,kBAAkB,aAAa,GAAG,SAAU,OAAO;QAC/C,IAAI,OAAO,KAAK,MAAM,KAAK,aAAa;YACpC;QACJ;QACA,IAAI,mBAAmB,KAAK,YAAY,CAAC,kBAAkB,CAAC;QAC5D,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC7B,OAAO,IAAI,KAAK,MAAM,CAAC,gBAAgB,CAAC,EAAE;QAC9C;QACA,OAAO,IAAI,KAAK,MAAM,CAAC,OAAO,YAAY,WAAW,UAAU,OAAO,CAAC,EAAE;IAC7E;IACA,kBAAkB,OAAO,GAAG,oMAAA,CAAA,QAAK;IACjC,gFAAgF;IAChF,+EAA+E;IAC/E,qCAAqC;IACrC,kBAAkB,OAAO,GAAG;QACxB,QAAQ;YACJ,SAAS;gBACL,uBAAuB;YAC3B;YACA,UAAU;gBACN,OAAO;YACX;YACA,SAAS;gBACL,OAAO;YACX;QACJ;QACA,MAAM;YACF,OAAO;gBACH,OAAO;gBACP,KAAK;gBACL,MAAM;YACV;YACA,QAAQ;gBACJ,OAAO;gBACP,KAAK;gBACL,MAAM;YACV;YACA,MAAM;gBACF,OAAO;gBACP,KAAK;gBACL,MAAM;YACV;YACA,MAAM;gBACF,SAAS;gBACT,OAAO;gBACP,KAAK;gBACL,MAAM;YACV;QACJ;QACA,MAAM;YACF,OAAO;gBACH,MAAM;gBACN,QAAQ;YACZ;YACA,QAAQ;gBACJ,MAAM;gBACN,QAAQ;gBACR,QAAQ;YACZ;YACA,MAAM;gBACF,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,cAAc;YAClB;YACA,MAAM;gBACF,MAAM;gBACN,QAAQ;gBACR,QAAQ;gBACR,cAAc;YAClB;QACJ;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 6679, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js"], "sourcesContent": ["import { IntlMessageFormat } from 'intl-messageformat';\nimport { isValidElement, cloneElement } from 'react';\nimport { memoize, strategies } from '@formatjs/fast-memoize';\n\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nvar IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}(IntlErrorCode || {});\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(globalFormats, inlineFormats, timeZone) {\n  const mfDateDefaults = IntlMessageFormat.formats.date;\n  const mfTimeDefaults = IntlMessageFormat.formats.time;\n  const dateTimeFormats = {\n    ...globalFormats?.dateTime,\n    ...inlineFormats?.dateTime\n  };\n  const allFormats = {\n    date: {\n      ...mfDateDefaults,\n      ...dateTimeFormats\n    },\n    time: {\n      ...mfTimeDefaults,\n      ...dateTimeFormats\n    },\n    number: {\n      ...globalFormats?.number,\n      ...inlineFormats?.number\n    }\n    // (list is not supported in ICU messages)\n  };\n  if (timeZone) {\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    ['date', 'time'].forEach(property => {\n      const formats = allFormats[property];\n      for (const [key, value] of Object.entries(formats)) {\n        formats[key] = {\n          timeZone,\n          ...value\n        };\n      }\n    });\n  }\n  return allFormats;\n}\n\nfunction joinPath(...parts) {\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return memoize(fn, {\n    cache: createMemoCache(cache),\n    strategy: strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn((...args) => new ConstructorFn(...args), cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = memoFn((...args) => new IntlMessageFormat(args[0], args[1], args[2], {\n    formatters: intlFormatters,\n    ...args[3]\n  }), cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(`No messages available at \\`${namespace}\\`.` );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(`Could not resolve \\`${fullKey}\\` in messages for locale \\`${locale}\\`.` );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/isValidElement(result) ? /*#__PURE__*/cloneElement(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace, onError = defaultOnError) {\n  try {\n    if (!messages) {\n      throw new Error(`No messages were configured.` );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(`No messages for namespace \\`${namespace}\\` found.` );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new IntlError(IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  {\n    // Keep fast path in development\n    if (values) return undefined;\n\n    // Despite potentially no values being available, there can still be\n    // placeholders in the message if the user has forgotten to provide\n    // values. In this case we compile the message to receive an error.\n    const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n      return unescapedMessage;\n    }\n  }\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl({\n  cache,\n  formats: globalFormats,\n  formatters,\n  getMessageFallback = defaultGetMessageFallback,\n  locale,\n  messagesOrError,\n  namespace,\n  onError,\n  timeZone\n}) {\n  const hasMessagesError = messagesOrError instanceof IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages`;\n        }\n      } else {\n        code = IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an object, but only strings are supported. Use a \\`.\\` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages`;\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat(globalFormats, formats, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? ` (${thrownError.originalMessage})` : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      values ? prepareTranslationValues(values) : values);\n      if (formattedMessage == null) {\n        throw new Error(`Unable to format \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'}` );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/isValidElement(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, `The message \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'} didn't resolve to a string. If you want to format rich text, use \\`t.rich\\` instead.` );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n    if (typeof result !== 'string') {\n      const error = new IntlError(IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(props) {\n  const {\n    _cache: cache = createCache(),\n    _formatters: formatters = createIntlFormatters(cache),\n    formats,\n    locale,\n    onError = defaultOnError,\n    timeZone: globalTimeZone\n  } = props;\n  function applyTimeZone(options) {\n    if (!options?.timeZone) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`timeZone\\` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone` ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions, overrides) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats?.[formatName];\n      if (!options) {\n        const error = new IntlError(IntlErrorCode.MISSING_FORMAT, `Format \\`${formatName}\\` is not available.` );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    if (overrides) {\n      options = {\n        ...options,\n        ...overrides\n      };\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, overrides, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions, overrides);\n    } catch {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(start, end, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    // Only read when necessary to avoid triggering a `dynamicIO` error\n    // unnecessarily (`now` is only needed for `format.relativeTime`)\n    if (props.now) {\n      return props.now;\n    } else {\n      onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`now\\` parameter wasn't provided to \\`relativeTime\\` and there is no global default configured, therefore the current time will be used as a fallback. See https://next-intl.dev/docs/usage/dates-times#relative-times-usenow` ));\n      return new Date();\n    }\n  }\n  function relativeTime(date, nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions, overrides) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, overrides, formats?.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(([key, messageOrMessages]) => {\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += ` (at ${parentPath})`;\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, `Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\n\nInvalid ${invalidKeyLabels.length === 1 ? 'key' : 'keys'}: ${invalidKeyLabels.join(', ')}\n\nIf you're migrating from a flat structure, you can convert your messages as follows:\n\nimport {set} from \"lodash\";\n\nconst input = {\n  \"one.one\": \"1.1\",\n  \"one.two\": \"1.2\",\n  \"two.one.one\": \"2.1.1\"\n};\n\nconst output = Object.entries(input).reduce(\n  (acc, [key, value]) => set(acc, key, value),\n  {}\n);\n\n// Output:\n//\n// {\n//   \"one\": {\n//     \"one\": \"1.1\",\n//     \"two\": \"1.2\"\n//   },\n//   \"two\": {\n//     \"one\": {\n//       \"one\": \"2.1.1\"\n//     }\n//   }\n// }\n` ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig({\n  formats,\n  getMessageFallback,\n  messages,\n  onError,\n  ...rest\n}) {\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    formats: formats || undefined,\n    messages: messages || undefined,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\nexport { IntlError as I, IntlErrorCode as a, createIntlFormatters as b, createFormatter as c, createCache as d, createBaseTranslator as e, defaultGetMessageFallback as f, defaultOnError as g, initializeConfig as i, resolveNamespace as r };\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,kBAAkB;IACtB,YAAY,IAAI,EAAE,eAAe,CAAE;QACjC,IAAI,UAAU;QACd,IAAI,iBAAiB;YACnB,WAAW,OAAO;QACpB;QACA,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,iBAAiB;YACnB,IAAI,CAAC,eAAe,GAAG;QACzB;IACF;AACF;AAEA,IAAI,gBAAgB,WAAW,GAAE,SAAU,aAAa;IACtD,aAAa,CAAC,kBAAkB,GAAG;IACnC,aAAa,CAAC,iBAAiB,GAAG;IAClC,aAAa,CAAC,uBAAuB,GAAG;IACxC,aAAa,CAAC,oBAAoB,GAAG;IACrC,aAAa,CAAC,kBAAkB,GAAG;IACnC,aAAa,CAAC,cAAc,GAAG;IAC/B,aAAa,CAAC,mBAAmB,GAAG;IACpC,OAAO;AACT,EAAE,iBAAiB,CAAC;AAEpB;;;;;;CAMC,GACD,SAAS,kCAAkC,aAAa,EAAE,aAAa,EAAE,QAAQ;IAC/E,MAAM,iBAAiB,mKAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,IAAI;IACrD,MAAM,iBAAiB,mKAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC,IAAI;IACrD,MAAM,kBAAkB;QACtB,GAAG,eAAe,QAAQ;QAC1B,GAAG,eAAe,QAAQ;IAC5B;IACA,MAAM,aAAa;QACjB,MAAM;YACJ,GAAG,cAAc;YACjB,GAAG,eAAe;QACpB;QACA,MAAM;YACJ,GAAG,cAAc;YACjB,GAAG,eAAe;QACpB;QACA,QAAQ;YACN,GAAG,eAAe,MAAM;YACxB,GAAG,eAAe,MAAM;QAC1B;IAEF;IACA,IAAI,UAAU;QACZ,4FAA4F;QAC5F,sHAAsH;QACtH;YAAC;YAAQ;SAAO,CAAC,OAAO,CAAC,CAAA;YACvB,MAAM,UAAU,UAAU,CAAC,SAAS;YACpC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,SAAU;gBAClD,OAAO,CAAC,IAAI,GAAG;oBACb;oBACA,GAAG,KAAK;gBACV;YACF;QACF;IACF;IACA,OAAO;AACT;AAEA,SAAS,SAAS,GAAG,KAAK;IACxB,OAAO,MAAM,MAAM,CAAC,SAAS,IAAI,CAAC;AACpC;AAEA;;;CAGC,GAED,SAAS,0BAA0B,KAAK;IACtC,OAAO,SAAS,MAAM,SAAS,EAAE,MAAM,GAAG;AAC5C;AACA,SAAS,eAAe,KAAK;IAC3B,QAAQ,KAAK,CAAC;AAChB;AAEA,SAAS;IACP,OAAO;QACL,UAAU,CAAC;QACX,QAAQ,CAAC;QACT,SAAS,CAAC;QACV,cAAc,CAAC;QACf,aAAa,CAAC;QACd,MAAM,CAAC;QACP,cAAc,CAAC;IACjB;AACF;AACA,SAAS,gBAAgB,KAAK;IAC5B,OAAO;QACL;YACE,OAAO;gBACL,KAAI,GAAG;oBACL,OAAO,KAAK,CAAC,IAAI;gBACnB;gBACA,KAAI,GAAG,EAAE,KAAK;oBACZ,KAAK,CAAC,IAAI,GAAG;gBACf;YACF;QACF;IACF;AACF;AACA,SAAS,OAAO,EAAE,EAAE,KAAK;IACvB,OAAO,CAAA,GAAA,qKAAA,CAAA,UAAO,AAAD,EAAE,IAAI;QACjB,OAAO,gBAAgB;QACvB,UAAU,qKAAA,CAAA,aAAU,CAAC,QAAQ;IAC/B;AACF;AACA,SAAS,gBAAgB,aAAa,EAAE,KAAK;IAC3C,OAAO,OAAO,CAAC,GAAG,OAAS,IAAI,iBAAiB,OAAO;AACzD;AACA,SAAS,qBAAqB,KAAK;IACjC,MAAM,oBAAoB,gBAAgB,KAAK,cAAc,EAAE,MAAM,QAAQ;IAC7E,MAAM,kBAAkB,gBAAgB,KAAK,YAAY,EAAE,MAAM,MAAM;IACvE,MAAM,iBAAiB,gBAAgB,KAAK,WAAW,EAAE,MAAM,WAAW;IAC1E,MAAM,wBAAwB,gBAAgB,KAAK,kBAAkB,EAAE,MAAM,YAAY;IACzF,MAAM,gBAAgB,gBAAgB,KAAK,UAAU,EAAE,MAAM,IAAI;IACjE,MAAM,kBAAkB,gBAAgB,KAAK,YAAY,EAAE,MAAM,YAAY;IAC7E,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,wEAAwE;AACxE,kEAAkE;AAClE,SAAS,uBAAuB,KAAK,EAAE,cAAc;IACnD,MAAM,mBAAmB,OAAO,CAAC,GAAG,OAAS,IAAI,mKAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;YAC5F,YAAY;YACZ,GAAG,IAAI,CAAC,EAAE;QACZ,IAAI,MAAM,OAAO;IACjB,OAAO;AACT;AACA,SAAS,YAAY,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS;IACnD,MAAM,UAAU,SAAS,WAAW;IACpC,IAAI,CAAC,UAAU;QACb,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,UAAU,GAAG,CAAC;IAC9D;IACA,IAAI,UAAU;IACd,IAAI,KAAK,CAAC,KAAK,OAAO,CAAC,CAAA;QACrB,MAAM,OAAO,OAAO,CAAC,KAAK;QAE1B,uEAAuE;QACvE,IAAI,QAAQ,QAAQ,QAAQ,MAAM;YAChC,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,QAAQ,4BAA4B,EAAE,OAAO,GAAG,CAAC;QAC1F;QACA,UAAU;IACZ;IACA,OAAO;AACT;AACA,SAAS,yBAAyB,MAAM;IACtC,kEAAkE;IAClE,MAAM,oBAAoB,CAAC;IAC3B,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;QAC1B,IAAI,QAAQ;QACZ,MAAM,QAAQ,MAAM,CAAC,IAAI;QACzB,IAAI;QACJ,IAAI,OAAO,UAAU,YAAY;YAC/B,cAAc,CAAA;gBACZ,MAAM,SAAS,MAAM;gBACrB,OAAO,WAAW,GAAE,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,WAAW,GAAE,CAAA,GAAA,qLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;oBAC7E,KAAK,MAAM;gBACb,KAAK;YACP;QACF,OAAO;YACL,cAAc;QAChB;QACA,iBAAiB,CAAC,IAAI,GAAG;IAC3B;IACA,OAAO;AACT;AACA,SAAS,mBAAmB,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,cAAc;IAC/E,IAAI;QACF,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,4BAA4B,CAAC;QAChD;QACA,MAAM,oBAAoB,YAAY,YAAY,QAAQ,UAAU,aAAa;QAEjF,uEAAuE;QACvE,IAAI,CAAC,mBAAmB;YACtB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,UAAU,SAAS,CAAC;QACrE;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,YAAY,IAAI,UAAU,cAAc,eAAe,EAAE,MAAM,OAAO;QAC5E,QAAQ;QACR,OAAO;IACT;AACF;AACA,SAAS,gBAAgB,SAAS,EAAE,MAAM;IACxC;QACE,gCAAgC;QAChC,IAAI,QAAQ,OAAO;QAEnB,oEAAoE;QACpE,mEAAmE;QACnE,mEAAmE;QACnE,MAAM,mBAAmB,UAAU,OAAO,CAAC,aAAa;QACxD,MAAM,kBAAkB,MAAM,IAAI,CAAC;QACnC,IAAI,CAAC,iBAAiB;YACpB,OAAO;QACT;IACF;AACF;AACA,SAAS,qBAAqB,MAAM;IAClC,MAAM,kBAAkB,mBAAmB,OAAO,MAAM,EAAE,OAAO,QAAQ,EAAE,OAAO,SAAS,EAAE,OAAO,OAAO;IAC3G,OAAO,yBAAyB;QAC9B,GAAG,MAAM;QACT;IACF;AACF;AACA,SAAS,yBAAyB,EAChC,KAAK,EACL,SAAS,aAAa,EACtB,UAAU,EACV,qBAAqB,yBAAyB,EAC9C,MAAM,EACN,eAAe,EACf,SAAS,EACT,OAAO,EACP,QAAQ,EACT;IACC,MAAM,mBAAmB,2BAA2B;IACpD,SAAS,8BAA8B,GAAG,EAAE,IAAI,EAAE,OAAO;QACvD,MAAM,QAAQ,IAAI,UAAU,MAAM;QAClC,QAAQ;QACR,OAAO,mBAAmB;YACxB;YACA;YACA;QACF;IACF;IACA,SAAS,gBAAgB,6EAA6E,GACtG,GAAG,EAAE,gEAAgE,GACrE,MAAM,EAAE,yDAAyD,GACjE,OAAO;QACL,IAAI,kBAAkB;YACpB,kDAAkD;YAClD,OAAO,mBAAmB;gBACxB,OAAO;gBACP;gBACA;YACF;QACF;QACA,MAAM,WAAW;QACjB,IAAI;QACJ,IAAI;YACF,UAAU,YAAY,QAAQ,UAAU,KAAK;QAC/C,EAAE,OAAO,OAAO;YACd,OAAO,8BAA8B,KAAK,cAAc,eAAe,EAAE,MAAM,OAAO;QACxF;QACA,IAAI,OAAO,YAAY,UAAU;YAC/B,IAAI,MAAM;YACV,IAAI,MAAM,OAAO,CAAC,UAAU;gBAC1B,OAAO,cAAc,eAAe;gBACpC;oBACE,eAAe,CAAC,aAAa,EAAE,SAAS,WAAW,KAAK,yHAAyH,CAAC;gBACpL;YACF,OAAO;gBACL,OAAO,cAAc,iBAAiB;gBACtC;oBACE,eAAe,CAAC,aAAa,EAAE,SAAS,WAAW,KAAK,qKAAqK,CAAC;gBAChO;YACF;YACA,OAAO,8BAA8B,KAAK,MAAM;QAClD;QACA,IAAI;QAEJ,gEAAgE;QAChE,MAAM,eAAe,gBAAgB,SAAS;QAC9C,IAAI,cAAc,OAAO;QAEzB,kDAAkD;QAClD,kDAAkD;QAClD,IAAI,CAAC,WAAW,gBAAgB,EAAE;YAChC,WAAW,gBAAgB,GAAG,uBAAuB,OAAO;QAC9D;QACA,IAAI;YACF,gBAAgB,WAAW,gBAAgB,CAAC,SAAS,QAAQ,kCAAkC,eAAe,SAAS,WAAW;gBAChI,YAAY;oBACV,GAAG,UAAU;oBACb,mBAAkB,OAAO,EAAE,OAAO;wBAChC,kEAAkE;wBAClE,OAAO,WAAW,iBAAiB,CAAC,SAAS;4BAC3C;4BACA,GAAG,OAAO;wBACZ;oBACF;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM,cAAc;YACpB,OAAO,8BAA8B,KAAK,cAAc,eAAe,EAAE,YAAY,OAAO,GAAG,CAAC,qBAAqB,cAAc,CAAC,EAAE,EAAE,YAAY,eAAe,CAAC,CAAC,CAAC,GAAG,EAAE;QAC7K;QACA,IAAI;YACF,MAAM,mBAAmB,cAAc,MAAM,CAC7C,mEAAmE;YACnE,2DAA2D;YAC3D,4DAA4D;YAC5D,iCAAiC;YACjC,SAAS,yBAAyB,UAAU;YAC5C,IAAI,oBAAoB,MAAM;gBAC5B,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,IAAI,MAAM,EAAE,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC,GAAG,YAAY;YAC3G;YAEA,mEAAmE;YACnE,OAAO,WAAW,GAAE,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE,qBACnC,2BAA2B;YAC3B,MAAM,OAAO,CAAC,qBAAqB,OAAO,qBAAqB,WAAW,mBAAmB,OAAO;QACtG,EAAE,OAAO,OAAO;YACd,OAAO,8BAA8B,KAAK,cAAc,gBAAgB,EAAE,MAAM,OAAO;QACzF;IACF;IACA,SAAS,YAAY,6EAA6E,GAClG,GAAG,EAAE,gEAAgE,GACrE,MAAM,EAAE,yDAAyD,GACjE,OAAO;QACL,MAAM,SAAS,gBAAgB,KAAK,QAAQ;QAC5C,IAAI,OAAO,WAAW,UAAU;YAC9B,OAAO,8BAA8B,KAAK,cAAc,eAAe,EAAE,CAAC,cAAc,EAAE,IAAI,MAAM,EAAE,YAAY,CAAC,YAAY,EAAE,UAAU,EAAE,CAAC,GAAG,WAAW,qFAAqF,CAAC;QACpP;QACA,OAAO;IACT;IACA,YAAY,IAAI,GAAG;IAEnB,oDAAoD;IACpD,YAAY,MAAM,GAAG,CAAC,KAAK,QAAQ;QACjC,MAAM,SAAS,gBAAgB,KAC/B,0EAA0E;QAC1E,qEAAqE;QACrE,QAAQ;QACR,IAAI,OAAO,WAAW,UAAU;YAC9B,MAAM,QAAQ,IAAI,UAAU,cAAc,gBAAgB,EAAE;YAC5D,QAAQ;YACR,OAAO,mBAAmB;gBACxB;gBACA;gBACA;YACF;QACF;QACA,OAAO;IACT;IACA,YAAY,GAAG,GAAG,CAAA;QAChB,IAAI,kBAAkB;YACpB,kDAAkD;YAClD,OAAO,mBAAmB;gBACxB,OAAO;gBACP;gBACA;YACF;QACF;QACA,MAAM,WAAW;QACjB,IAAI;YACF,OAAO,YAAY,QAAQ,UAAU,KAAK;QAC5C,EAAE,OAAO,OAAO;YACd,OAAO,8BAA8B,KAAK,cAAc,eAAe,EAAE,MAAM,OAAO;QACxF;IACF;IACA,YAAY,GAAG,GAAG,CAAA;QAChB,IAAI,kBAAkB;YACpB,OAAO;QACT;QACA,IAAI;YACF,YAAY,QAAQ,iBAAiB,KAAK;YAC1C,OAAO;QACT,EAAE,OAAM;YACN,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,iBAAiB,SAAS,EAAE,eAAe;IAClD,OAAO,cAAc,kBAAkB,YAAY,UAAU,KAAK,CAAC,CAAC,kBAAkB,GAAG,EAAE,MAAM;AACnG;AAEA,MAAM,SAAS;AACf,MAAM,SAAS,SAAS;AACxB,MAAM,OAAO,SAAS;AACtB,MAAM,MAAM,OAAO;AACnB,MAAM,OAAO,MAAM;AACnB,MAAM,QAAQ,MAAM,CAAC,MAAM,EAAE,GAAG,gBAAgB;AAChD,MAAM,UAAU,QAAQ;AACxB,MAAM,OAAO,MAAM;AACnB,MAAM,eAAe;IACnB,QAAQ;IACR,SAAS;IACT,QAAQ;IACR,SAAS;IACT,MAAM;IACN,OAAO;IACP,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,UAAU;IACV,MAAM;IACN,OAAO;AACT;AACA,SAAS,wBAAwB,OAAO;IACtC,MAAM,WAAW,KAAK,GAAG,CAAC;IAC1B,IAAI,WAAW,QAAQ;QACrB,OAAO;IACT,OAAO,IAAI,WAAW,MAAM;QAC1B,OAAO;IACT,OAAO,IAAI,WAAW,KAAK;QACzB,OAAO;IACT,OAAO,IAAI,WAAW,MAAM;QAC1B,OAAO;IACT,OAAO,IAAI,WAAW,OAAO;QAC3B,OAAO;IACT,OAAO,IAAI,WAAW,MAAM;QAC1B,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,2BAA2B,OAAO,EAAE,IAAI;IAC/C,sEAAsE;IACtE,+CAA+C;IAC/C,OAAO,KAAK,KAAK,CAAC,UAAU,YAAY,CAAC,KAAK;AAChD;AACA,SAAS,gBAAgB,KAAK;IAC5B,MAAM,EACJ,QAAQ,QAAQ,aAAa,EAC7B,aAAa,aAAa,qBAAqB,MAAM,EACrD,OAAO,EACP,MAAM,EACN,UAAU,cAAc,EACxB,UAAU,cAAc,EACzB,GAAG;IACJ,SAAS,cAAc,OAAO;QAC5B,IAAI,CAAC,SAAS,UAAU;YACtB,IAAI,gBAAgB;gBAClB,UAAU;oBACR,GAAG,OAAO;oBACV,UAAU;gBACZ;YACF,OAAO;gBACL,QAAQ,IAAI,UAAU,cAAc,oBAAoB,EAAE,CAAC,mPAAmP,CAAC;YACjT;QACF;QACA,OAAO;IACT;IACA,SAAS,uBAAuB,WAAW,EAAE,eAAe,EAAE,SAAS;QACrE,IAAI;QACJ,IAAI,OAAO,oBAAoB,UAAU;YACvC,MAAM,aAAa;YACnB,UAAU,aAAa,CAAC,WAAW;YACnC,IAAI,CAAC,SAAS;gBACZ,MAAM,QAAQ,IAAI,UAAU,cAAc,cAAc,EAAE,CAAC,SAAS,EAAE,WAAW,oBAAoB,CAAC;gBACtG,QAAQ;gBACR,MAAM;YACR;QACF,OAAO;YACL,UAAU;QACZ;QACA,IAAI,WAAW;YACb,UAAU;gBACR,GAAG,OAAO;gBACV,GAAG,SAAS;YACd;QACF;QACA,OAAO;IACT;IACA,SAAS,kBAAkB,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW;QACxF,IAAI;QACJ,IAAI;YACF,UAAU,uBAAuB,aAAa,iBAAiB;QACjE,EAAE,OAAM;YACN,OAAO;QACT;QACA,IAAI;YACF,OAAO,UAAU;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,UAAU,cAAc,gBAAgB,EAAE,MAAM,OAAO;YACnE,OAAO;QACT;IACF;IACA,SAAS,SAAS,KAAK,EAAE,eAAe,EAAE,SAAS;QACjD,OAAO,kBAAkB,iBAAiB,WAAW,SAAS,UAAU,CAAA;YACtE,UAAU,cAAc;YACxB,OAAO,WAAW,iBAAiB,CAAC,QAAQ,SAAS,MAAM,CAAC;QAC9D,GAAG,IAAM,OAAO;IAClB;IACA,SAAS,cAAc,KAAK,EAAE,GAAG,EAAE,eAAe,EAAE,SAAS;QAC3D,OAAO,kBAAkB,iBAAiB,WAAW,SAAS,UAAU,CAAA;YACtE,UAAU,cAAc;YACxB,OAAO,WAAW,iBAAiB,CAAC,QAAQ,SAAS,WAAW,CAAC,OAAO;QAC1E,GAAG,IAAM;gBAAC,SAAS;gBAAQ,SAAS;aAAK,CAAC,IAAI,CAAC;IACjD;IACA,SAAS,OAAO,KAAK,EAAE,eAAe,EAAE,SAAS;QAC/C,OAAO,kBAAkB,iBAAiB,WAAW,SAAS,QAAQ,CAAA,UAAW,WAAW,eAAe,CAAC,QAAQ,SAAS,MAAM,CAAC,QAAQ,IAAM,OAAO;IAC3J;IACA,SAAS;QACP,mEAAmE;QACnE,iEAAiE;QACjE,IAAI,MAAM,GAAG,EAAE;YACb,OAAO,MAAM,GAAG;QAClB,OAAO;YACL,QAAQ,IAAI,UAAU,cAAc,oBAAoB,EAAE,CAAC,kOAAkO,CAAC;YAC9R,OAAO,IAAI;QACb;IACF;IACA,SAAS,aAAa,IAAI,EAAE,YAAY;QACtC,IAAI;YACF,IAAI,SAAS;YACb,MAAM,OAAO,CAAC;YACd,IAAI,wBAAwB,QAAQ,OAAO,iBAAiB,UAAU;gBACpE,UAAU,IAAI,KAAK;YACrB,OAAO,IAAI,cAAc;gBACvB,IAAI,aAAa,GAAG,IAAI,MAAM;oBAC5B,UAAU,IAAI,KAAK,aAAa,GAAG;gBACrC,OAAO;oBACL,UAAU;gBACZ;gBACA,OAAO,aAAa,IAAI;gBACxB,KAAK,KAAK,GAAG,aAAa,KAAK;gBAC/B,kDAAkD;gBAClD,KAAK,eAAe,GAAG,aAAa,eAAe;YACrD;YACA,IAAI,CAAC,SAAS;gBACZ,UAAU;YACZ;YACA,MAAM,WAAW,IAAI,KAAK;YAC1B,MAAM,UAAU,CAAC,SAAS,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;YAC3D,IAAI,CAAC,MAAM;gBACT,OAAO,wBAAwB;YACjC;YAEA,uEAAuE;YACvE,yEAAyE;YACzE,wEAAwE;YACxE,mEAAmE;YACnE,sEAAsE;YACtE,uEAAuE;YACvE,2EAA2E;YAC3E,iDAAiD;YACjD,KAAK,OAAO,GAAG,SAAS,WAAW,SAAS;YAC5C,MAAM,QAAQ,2BAA2B,SAAS;YAClD,OAAO,WAAW,qBAAqB,CAAC,QAAQ,MAAM,MAAM,CAAC,OAAO;QACtE,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,UAAU,cAAc,gBAAgB,EAAE,MAAM,OAAO;YACnE,OAAO,OAAO;QAChB;IACF;IACA,SAAS,KAAK,KAAK,EAAE,eAAe,EAAE,SAAS;QAC7C,MAAM,kBAAkB,EAAE;QAC1B,MAAM,aAAa,IAAI;QAEvB,yEAAyE;QACzE,4EAA4E;QAC5E,iCAAiC;QACjC,IAAI,QAAQ;QACZ,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI;YACJ,IAAI,OAAO,SAAS,UAAU;gBAC5B,iBAAiB,OAAO;gBACxB,WAAW,GAAG,CAAC,gBAAgB;YACjC,OAAO;gBACL,iBAAiB,OAAO;YAC1B;YACA,gBAAgB,IAAI,CAAC;YACrB;QACF;QACA,OAAO,kBAAkB,iBAAiB,WAAW,SAAS,MAC9D,uIAAuI;QACvI,CAAA;YACE,MAAM,SAAS,WAAW,aAAa,CAAC,QAAQ,SAAS,aAAa,CAAC,iBAAiB,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK,GAAG,WAAW,GAAG,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK;YACnL,IAAI,WAAW,IAAI,GAAG,GAAG;gBACvB,OAAO;YACT,OAAO;gBACL,OAAO,OAAO,IAAI,CAAC;YACrB;QACF,GAAG,IAAM,OAAO;IAClB;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,wBAAwB,QAAQ,EAAE,gBAAgB,EAAE,UAAU;IACrE,OAAO,OAAO,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC,KAAK,kBAAkB;QACxD,IAAI,IAAI,QAAQ,CAAC,MAAM;YACrB,IAAI,WAAW;YACf,IAAI,YAAY,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;YACjD,iBAAiB,IAAI,CAAC;QACxB;QAEA,uEAAuE;QACvE,IAAI,qBAAqB,QAAQ,OAAO,sBAAsB,UAAU;YACtE,wBAAwB,mBAAmB,kBAAkB,SAAS,YAAY;QACpF;IACF;AACF;AACA,SAAS,iBAAiB,QAAQ,EAAE,OAAO;IACzC,MAAM,mBAAmB,EAAE;IAC3B,wBAAwB,UAAU;IAClC,IAAI,iBAAiB,MAAM,GAAG,GAAG;QAC/B,QAAQ,IAAI,UAAU,cAAc,WAAW,EAAE,CAAC;;QAE9C,EAAE,iBAAiB,MAAM,KAAK,IAAI,QAAQ,OAAO,EAAE,EAAE,iBAAiB,IAAI,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BzF,CAAC;IACC;AACF;AAEA;;CAEC,GACD,SAAS,iBAAiB,EACxB,OAAO,EACP,kBAAkB,EAClB,QAAQ,EACR,OAAO,EACP,GAAG,MACJ;IACC,MAAM,eAAe,WAAW;IAChC,MAAM,0BAA0B,sBAAsB;IACtD;QACE,IAAI,UAAU;YACZ,iBAAiB,UAAU;QAC7B;IACF;IACA,OAAO;QACL,GAAG,IAAI;QACP,SAAS,WAAW;QACpB,UAAU,YAAY;QACtB,SAAS;QACT,oBAAoB;IACtB;AACF", "ignoreList": [0]}}]}