{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "N7q2b64Zt1JT/i+kp0zKQtSwAeXEad1q7osWZzH2+Lo=", "__NEXT_PREVIEW_MODE_ID": "9608406e7341571ef83b4e47182dd72f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f4ae6a1588394c466f0c25cd3cb0451e9f4c75b1f4c834f246ac319a298d5368", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "80f5b33c2f4aca1793ccdcafeab84575ae20d1ccbd0eb9209f887a157ee872e0"}}}, "sortedMiddleware": ["/"], "functions": {}}