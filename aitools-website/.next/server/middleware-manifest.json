{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "IBCKtSr50mVr5qyjqc84IbmgDrtjKtpeN9PFStgL+jE=", "__NEXT_PREVIEW_MODE_ID": "2787a33802a03c019304f9e62962fc68", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "22e58c23286023576049791b4a478e6b6bea69c94683fceb723c363c4a5ea7c1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3c0de1c1edeefb7fb6cc2bab207743b36b73342c4f3f92353eaf6730aef740f9"}}}, "sortedMiddleware": ["/"], "functions": {}}