{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(.*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "zTzdaM6IIh5AKAz2RvwAt9iOl6oMpHeQ6QV1OsohUCs=", "__NEXT_PREVIEW_MODE_ID": "97441fe0488fc0f6c443721699f3278e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d043073493e7ac101da0d06410e1d66f78f6b2f342dfb44fe5d1817d37af8cb0", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3423165861c59bbfd24725c672d1057c9bed4014ab50cd5a6460e33529ffb627"}}}, "sortedMiddleware": ["/"], "functions": {}}