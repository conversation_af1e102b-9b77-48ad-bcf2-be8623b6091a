{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_915e9f02._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__e8693e40._.js", "server/edge/chunks/edge-wrapper_ff61254e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "IBCKtSr50mVr5qyjqc84IbmgDrtjKtpeN9PFStgL+jE=", "__NEXT_PREVIEW_MODE_ID": "8685c2a5094df2b0459b4229cc571625", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6afc167c597e0e37837dde105b6358eda331b0d686043ec4e884d86ecd466b93", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0193e1530a1f231db16ff7542c031d0bb3935867bcb75408c1df1055e66be370"}}}, "sortedMiddleware": ["/"], "functions": {}}