"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/intl-messageformat";
exports.ids = ["vendor-chunks/intl-messageformat"];
exports.modules = {

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/core.js":
/*!*********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/core.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlMessageFormat: () => (/* binding */ IntlMessageFormat)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(rsc)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(rsc)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _formatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatters */ \"(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\n\n\n\n\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getDateTimeFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getPluralRules: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== _formatters__WEBPACK_IMPORTED_MODULE_3__.PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return (0,_formatters__WEBPACK_IMPORTED_MODULE_3__.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/core.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/error.js":
/*!**********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/error.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FormatError: () => (/* binding */ FormatError),\n/* harmony export */   InvalidValueError: () => (/* binding */ InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* binding */ InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* binding */ MissingValueError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\n\nvar InvalidValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\n\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\n\nvar MissingValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js":
/*!***************************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/formatters.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PART_TYPE: () => (/* binding */ PART_TYPE),\n/* harmony export */   formatToParts: () => (/* binding */ formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* binding */ isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(rsc)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error */ \"(rsc)/./node_modules/intl-messageformat/lib/src/error.js\");\n\n\nvar PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPoundElement)(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new _error__WEBPACK_IMPORTED_MODULE_1__.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new _error__WEBPACK_IMPORTED_MODULE_1__.FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", _error__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/intl-messageformat/lib/src/formatters.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/core.js":
/*!*********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/core.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlMessageFormat: () => (/* binding */ IntlMessageFormat)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(ssr)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(ssr)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _formatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatters */ \"(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\n\n\n\n\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getDateTimeFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getPluralRules: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== _formatters__WEBPACK_IMPORTED_MODULE_3__.PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return (0,_formatters__WEBPACK_IMPORTED_MODULE_3__.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/core.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/error.js":
/*!**********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/error.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FormatError: () => (/* binding */ FormatError),\n/* harmony export */   InvalidValueError: () => (/* binding */ InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* binding */ InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* binding */ MissingValueError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\n\nvar InvalidValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\n\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\n\nvar MissingValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js":
/*!***************************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/formatters.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PART_TYPE: () => (/* binding */ PART_TYPE),\n/* harmony export */   formatToParts: () => (/* binding */ formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* binding */ isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(ssr)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error */ \"(ssr)/./node_modules/intl-messageformat/lib/src/error.js\");\n\n\nvar PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPoundElement)(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new _error__WEBPACK_IMPORTED_MODULE_1__.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new _error__WEBPACK_IMPORTED_MODULE_1__.FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", _error__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/intl-messageformat/lib/src/formatters.js\n");

/***/ })

};
;