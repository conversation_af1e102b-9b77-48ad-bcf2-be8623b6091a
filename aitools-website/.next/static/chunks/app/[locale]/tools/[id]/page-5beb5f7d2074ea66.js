(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6249],{2388:(e,t,s)=>{"use strict";s.d(t,{N_:()=>n,a8:()=>c,rd:()=>o});var a=s(9984),l=s(981);let r=(0,a.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:n,redirect:i,usePathname:c,useRouter:o}=(0,l.A)(r)},2839:(e,t,s)=>{"use strict";s.d(t,{u:()=>d});var a=s(9509);function l(){if(a.env.NEXT_PUBLIC_APP_URL)return a.env.NEXT_PUBLIC_APP_URL;{let{protocol:e,hostname:t,port:s}=window.location;return"".concat(e,"//").concat(t).concat(s?":".concat(s):"")}}function r(){if(a.env.NEXT_PUBLIC_API_BASE_URL)return a.env.NEXT_PUBLIC_API_BASE_URL;let e=l();return"".concat(e,"/api")}function n(){return"production"}function i(){return"development"===n()}l(),r(),a.env.NEXTAUTH_URL?a.env.NEXTAUTH_URL:l(),n(),i(),n(),window.location.port||window.location.protocol,i();let c=r();class o{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s="".concat(this.baseURL).concat(e),a={headers:{"Content-Type":"application/json",...t.headers},...t},l=await fetch(s,a),r=await l.json();if(!l.ok)throw Error(r.error||"HTTP error! status: ".concat(l.status));return r}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/tools".concat(s?"?".concat(s):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/user/liked-tools".concat(s?"?".concat(s):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,a]=e;void 0!==a&&t.append(s,a.toString())});let s=t.toString();return this.request("/admin/tools".concat(s?"?".concat(s):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=c){this.baseURL=e}}let d=new o},3332:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},3467:(e,t,s)=>{"use strict";s.d(t,{$g:()=>d,Ef:()=>c,Y$:()=>i,kX:()=>a,mV:()=>o,tF:()=>u,v4:()=>n,vS:()=>l});let a={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},l=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],r={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},n=[{value:"",label:"所有价格"},{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],i=[{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],c=e=>{switch(e){case r.FREE.value:return r.FREE.color;case r.FREEMIUM.value:return r.FREEMIUM.color;case r.PAID.value:return r.PAID.color;default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case r.FREE.value:return r.FREE.label;case r.FREEMIUM.value:return r.FREEMIUM.label;case r.PAID.value:return r.PAID.label;default:return e}},d=e=>0===e?"免费":"\xa5".concat(e),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4601:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var a=s(5155),l=s(2115),r=s(2388),n=s(7652),i=s(2108),c=s(9911),o=s(6214);function d(e){let{toolId:t,initialLikes:s=0,initialLiked:d=!1,onLoginRequired:u,onUnlike:m,isInLikedPage:x=!1,showCount:h=!0,size:g="md"}=e,{data:p}=(0,i.useSession)(),{getToolState:y,initializeToolState:b,toggleLike:f}=(0,o.X)(),j=(0,r.a8)(),v=(0,n.c3)("common");null==j||j.startsWith("/en");let N=y(t);(0,l.useEffect)(()=>{b(t,s,d)},[t,s,d]);let w=async()=>{if(!p){null==u||u();return}if(N.loading)return;let e=N.liked;await f(t,x)&&x&&e&&m&&m(t)},k=(()=>{switch(g){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,a.jsxs)("button",{onClick:w,disabled:N.loading,className:"\n        ".concat(k.button,"\n        inline-flex items-center space-x-1\n        ").concat(N.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500","\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      "),title:v(N.liked?"unlike":"like"),children:[N.loading?(0,a.jsx)("div",{className:"".concat(k.icon," animate-spin rounded-full border-2 border-gray-300 border-t-red-500")}):N.liked?(0,a.jsx)(c.Mbv,{className:k.icon}):(0,a.jsx)(c.sOK,{className:k.icon}),h&&(0,a.jsx)("span",{className:"".concat(k.text," font-medium"),children:N.likes})]})}},6063:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var a=s(5155),l=s(2115),r=s(2108),n=s(2388),i=s(7652),c=s(3385),o=s(9911);function d(e){let{isOpen:t,onClose:s}=e,[d,u]=(0,l.useState)("method"),[m,x]=(0,l.useState)(""),[h,g]=(0,l.useState)(""),[p,y]=(0,l.useState)(!1),[b,f]=(0,l.useState)("");(0,n.a8)();let j=(0,i.c3)("auth");(0,c.Ym)();let v=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",s=document.createElement("div");s.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===t?"bg-green-500":"bg-red-500"),s.textContent=e,document.body.appendChild(s),setTimeout(()=>document.body.removeChild(s),3e3)},N=()=>{u("method"),x(""),g(""),f(""),s()},w=async e=>{try{y(!0),await (0,r.signIn)(e,{callbackUrl:"/"})}catch(e){v(j("login_failed"),"error")}finally{y(!1)}},k=async()=>{if(!m)return void f(j("email_required"));if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(m))return void f(j("email_invalid"));f(""),y(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:m})}),t=await e.json();t.success?(g(t.token),u("code"),v(j("verification_sent"))):v(t.error||j("send_failed"),"error")}catch(e){v(j("network_error"),"error")}finally{y(!1)}},_=async e=>{if(6===e.length){y(!0);try{let t=await (0,r.signIn)("email-code",{email:m,code:e,token:h,redirect:!1});(null==t?void 0:t.ok)?(v(j("login_success")),N()):v((null==t?void 0:t.error)||j("verification_error"),"error")}catch(e){v(j("network_error"),"error")}finally{y(!1)}}},E=(e,t)=>{if(t.length>1)return;let s=document.querySelectorAll(".code-input");if(s[e].value=t,t&&e<5){var a;null==(a=s[e+1])||a.focus()}let l=Array.from(s).map(e=>e.value).join("");6===l.length&&_(l)};return t?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:N}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===d&&j("login_title"),"email"===d&&j("email_login_title"),"code"===d&&j("verification_title")]}),(0,a.jsx)("button",{onClick:N,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(o.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:["method"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:j("choose_method")}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>w("google"),disabled:p,children:[(0,a.jsx)(o.DSS,{}),j("google_login")]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>w("github"),disabled:p,children:[(0,a.jsx)(o.hL4,{}),j("github_login")]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:j("or")})})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>u("email"),children:[(0,a.jsx)(o.maD,{}),j("email_login")]})]}),"email"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:j("email_instruction")}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:j("email_address")}),(0,a.jsx)("input",{type:"email",value:m,onChange:e=>x(e.target.value),placeholder:j("email_placeholder"),onKeyPress:e=>"Enter"===e.key&&k(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),b&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:b})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:k,disabled:p,children:p?j("sending"):j("send_code")}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>u("method"),children:j("back")})]})]}),"code"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:j("verification_instruction",{email:m})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:j("verification_code")}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,a.jsx)("input",{type:"text",maxLength:1,onChange:t=>E(e,t.target.value),disabled:p,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>u("email"),children:j("resend_code")}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>u("method"),children:j("back")})]})]})]})]})]}):null}},6214:(e,t,s)=>{"use strict";s.d(t,{LikeProvider:()=>c,X:()=>o});var a=s(5155),l=s(2115),r=s(2108);let n={liked:!1,likes:0,loading:!1},i=(0,l.createContext)(null);function c(e){let{children:t}=e,{data:s}=(0,r.useSession)(),[c,o]=(0,l.useState)({}),d=(0,l.useCallback)(e=>c[e]||n,[c]),u=(0,l.useCallback)(function(e,t){let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];o(a=>a[e]?a:{...a,[e]:{liked:s,likes:t,loading:!1}})},[]),m=(0,l.useCallback)(async e=>{if(s)try{let t=await fetch("/api/tools/".concat(e,"/like"));if(t.ok){let s=await t.json();s.success&&o(t=>({...t,[e]:{liked:s.data.liked,likes:s.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[s]),x=(0,l.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!s)return!1;o(t=>({...t,[e]:{...t[e]||n,loading:!0}}));try{let s=await fetch("/api/tools/".concat(e,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t?{forceUnlike:!0}:{})});if(s.ok){let t=await s.json();if(t.success)return o(s=>({...s,[e]:{liked:t.data.liked,likes:t.data.likes,loading:!1}})),!0}return o(t=>({...t,[e]:{...t[e]||n,loading:!1}})),!1}catch(t){return console.error("Like request failed:",t),o(t=>({...t,[e]:{...t[e]||n,loading:!1}})),!1}},[s]);return(0,l.useEffect)(()=>{s?Object.keys(c).forEach(e=>{m(e)}):o(e=>{let t={};return Object.keys(e).forEach(s=>{t[s]={...e[s],liked:!1,loading:!1}}),t})},[s]),(0,a.jsx)(i.Provider,{value:{toolStates:c,toggleLike:x,getToolState:d,initializeToolState:u,refreshToolState:m},children:t})}function o(){let e=(0,l.useContext)(i);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},6941:(e,t,s)=>{"use strict";s.d(t,{default:()=>v});var a=s(5155),l=s(2115),r=s(2388),n=s(7652),i=s(4601),c=s(2108),o=s(9911);function d(e){let{toolId:t,onLoginRequired:s}=e,{data:i}=(0,c.useSession)(),[d,u]=(0,l.useState)([]),[m,x]=(0,l.useState)(""),[h,g]=(0,l.useState)(null),[p,y]=(0,l.useState)(""),[b,f]=(0,l.useState)(!1),[j,v]=(0,l.useState)(!1),N=(0,r.a8)(),w=(0,n.c3)("comments"),k=(null==N?void 0:N.startsWith("/en"))?"en":"zh",_=async()=>{f(!0);try{let e=await fetch("/api/tools/".concat(t,"/comments"));if(e.ok){let t=await e.json();t.success&&u(t.data.comments)}}catch(e){console.error("Failed to fetch comments:",e)}finally{f(!1)}};(0,l.useEffect)(()=>{_()},[t]);let E=async()=>{if(!i){null==s||s();return}if(m.trim()){v(!0);try{let e=await fetch("/api/tools/".concat(t,"/comments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:m.trim()})});if(e.ok)(await e.json()).success&&(x(""),_());else{let t=await e.json();console.error("Comment submission failed:",t.message)}}catch(e){console.error("Comment submission error:",e)}finally{v(!1)}}},C=async e=>{if(!i){null==s||s();return}if(p.trim()){v(!0);try{let s=await fetch("/api/tools/".concat(t,"/comments"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:p.trim(),parentId:e})});if(s.ok)(await s.json()).success&&(y(""),g(null),_());else{let e=await s.json();console.error("Reply submission failed:",e.message)}}catch(e){console.error("Reply submission error:",e)}finally{v(!1)}}},S=e=>{let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/36e5);return s<1?w("just_now"):s<24?w("hours_ago",{hours:s}):s<168?w("days_ago",{days:Math.floor(s/24)}):t.toLocaleDateString("zh"===k?"zh-CN":"en-US")},A=e=>{let{comment:t,isReply:s=!1}=e;return(0,a.jsx)("div",{className:"".concat(s?"ml-8 border-l-2 border-gray-100 pl-4":""),children:(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:t.userId.image?(0,a.jsx)("img",{src:t.userId.image,alt:t.userId.name,className:"w-8 h-8 rounded-full"}):(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,a.jsx)(o.x$1,{className:"w-4 h-4 text-gray-600"})})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("span",{className:"font-medium text-gray-900",children:t.userId.name}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:S(t.createdAt)})]}),(0,a.jsx)("p",{className:"text-gray-700 mb-2",children:t.content}),(0,a.jsx)("div",{className:"flex items-center gap-4",children:!s&&(0,a.jsxs)("button",{onClick:()=>g(h===t._id?null:t._id),className:"text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1",children:[(0,a.jsx)(o.w1Z,{className:"w-3 h-3"}),w("reply")]})}),h===t._id&&(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("textarea",{value:p,onChange:e=>y(e.target.value),placeholder:w("write_reply"),className:"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:3,maxLength:1e3}),(0,a.jsxs)("div",{className:"flex justify-end gap-2 mt-2",children:[(0,a.jsx)("button",{onClick:()=>{g(null),y("")},className:"px-4 py-2 text-gray-600 hover:text-gray-800",children:w("cancel")}),(0,a.jsx)("button",{onClick:()=>C(t._id),disabled:j||!p.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:j?w("submitting"):w("send")})]})]}),t.replies&&t.replies.length>0&&(0,a.jsx)("div",{className:"mt-4 space-y-4",children:t.replies.map(e=>(0,a.jsx)(A,{comment:e,isReply:!0},e._id))})]})]})})};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:w("title",{count:d.length})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("textarea",{value:m,onChange:e=>x(e.target.value),placeholder:i?w("write_comment"):w("login_to_comment"),className:"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,maxLength:1e3,disabled:!i}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[m.length,"/1000"]}),(0,a.jsx)("button",{onClick:E,disabled:j||!m.trim()||!i,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:j?w("submitting"):w("submit_comment")})]})]}),b?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-500 mt-2",children:w("loading")})]}):0===d.length?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-gray-500",children:w("no_comments")})}):(0,a.jsx)("div",{className:"space-y-6",children:d.map(e=>(0,a.jsx)(A,{comment:e},e._id))})]})}var u=s(6063),m=s(2839),x=s(3467),h=s(9946);let g=(0,h.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);var p=s(2657),y=s(1976);let b=(0,h.A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var f=s(3332),j=s(3786);function v(e){let{initialTool:t,toolId:s}=e,[c,o]=(0,l.useState)(t),[h,v]=(0,l.useState)([]),[N,w]=(0,l.useState)(!1),k=(0,r.a8)(),_=(0,n.c3)("tool_detail"),E=(null==k?void 0:k.startsWith("/en"))?"en":"zh";(0,l.useEffect)(()=>{C(c.category)},[c.category]);let C=async e=>{try{let t=await m.u.getTools({category:e,status:"published",limit:3});if(t.success&&t.data){let e=t.data.tools.filter(e=>e._id!==s);v(e.slice(0,3))}}catch(e){}};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)("article",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsxs)("header",{className:"flex items-start justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[c.logo?(0,a.jsx)("img",{src:c.logo,alt:"".concat(c.name," logo"),className:"w-16 h-16 rounded-lg object-cover"}):(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-2xl",children:c.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:c.name}),c.tagline&&(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-3",children:c.tagline}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ".concat((0,x.Ef)(c.pricing)),children:[(0,a.jsx)(g,{className:"mr-1 h-4 w-4"}),(0,x.mV)(c.pricing)]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:_("views",{count:c.views||0})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(y.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:_("likes",{count:c.likes||0})})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.A,{toolId:c._id,initialLikes:c.likes,onLoginRequired:()=>w(!0)}),(0,a.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-500 transition-colors",children:(0,a.jsx)(b,{className:"h-5 w-5"})})]})]}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("p",{className:"text-gray-600 text-lg leading-relaxed",children:c.description})}),c.tags&&c.tags.length>0&&(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:c.tags.map((e,t)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 cursor-pointer",children:[(0,a.jsx)(f.A,{className:"mr-1 h-3 w-3"}),e]},t))}),(0,a.jsx)("div",{className:"flex flex-col sm:flex-row gap-4",children:(0,a.jsxs)("a",{href:c.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,a.jsx)(j.A,{className:"mr-2 h-5 w-5"}),_("visit_tool",{name:c.name})]})})]})}),(0,a.jsxs)("aside",{className:"lg:col-span-1",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:_("tool_info")}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:_("category")}),(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:c.category})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:_("pricing_model")}),(0,a.jsx)("span",{className:"px-2 py-1 rounded text-sm font-medium ".concat((0,x.Ef)(c.pricing)),children:(0,x.mV)(c.pricing)})]}),c.launchDate&&(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:_("launch_date")}),(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:new Date(c.launchDate).toLocaleDateString("zh"===E?"zh-CN":"en-US")})]})]})]}),h.length>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:_("related_tools")}),(0,a.jsx)("div",{className:"space-y-4",children:h.map(e=>(0,a.jsx)("div",{children:(0,a.jsx)(r.N_,{href:"/tools/".concat(e._id),className:"block p-3 rounded-lg hover:bg-gray-50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,a.jsx)("img",{src:e.logo,alt:e.name,className:"w-10 h-10 rounded object-cover"}):(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-sm",children:e.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-500 mt-1",children:[(0,a.jsx)("span",{className:"px-2 py-1 rounded ".concat((0,x.Ef)(e.pricing)),children:(0,x.mV)(e.pricing)}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:_("views",{count:e.views||0})}),(0,a.jsx)("span",{children:_("likes",{count:e.likes||0})})]})]})]})]})})},e._id))})]})]})]}),(0,a.jsx)("div",{className:"mt-12",children:(0,a.jsx)(d,{toolId:c._id,onLoginRequired:()=>w(!0)})}),(0,a.jsx)(u.A,{isOpen:N,onClose:()=>w(!1)})]})}},8361:(e,t,s)=>{Promise.resolve().then(s.bind(s,6160)),Promise.resolve().then(s.bind(s,6941))}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,3385,6160,2108,5002,8441,1684,7358],()=>t(8361)),_N_E=e.O()}]);