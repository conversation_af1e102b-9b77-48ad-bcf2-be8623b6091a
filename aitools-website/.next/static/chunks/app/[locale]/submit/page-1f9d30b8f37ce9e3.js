(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1298],{646:(e,s,l)=>{"use strict";l.d(s,{A:()=>r});let r=(0,l(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3929:(e,s,l)=>{Promise.resolve().then(l.bind(l,5166))},5166:(e,s,l)=>{"use strict";l.d(s,{default:()=>y});var r=l(5155),a=l(2115),t=l(2108),i=l(2388),o=l(7652),n=l(2731),d=l(9783),c=l(5734),m=l(6063),u=l(3467),x=l(2765),g=l(9946);let h=(0,g.A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);var b=l(9869);let p=(0,g.A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var f=l(8146),j=l(5131);function y(e){var s,l,g;let{}=e,y=(0,o.c3)("submit"),{data:v}=(0,t.useSession)(),N=(0,i.rd)(),w=(0,x.OD)(),[_,k]=(0,a.useState)({name:"",tagline:"",description:"",websiteUrl:"",logoFile:null,category:"",tags:[],pricingModel:""}),[A,F]=(0,a.useState)(null),[C,M]=(0,a.useState)(!1),[S,U]=(0,a.useState)("idle"),[q,O]=(0,a.useState)(!1),P=e=>{let{name:s,value:l}=e.target;k(e=>({...e,[s]:l}))},z=async e=>{if(e.preventDefault(),!v)return void O(!0);M(!0),U("idle");try{let e=new FormData;e.append("name",_.name),e.append("tagline",_.tagline),e.append("description",_.description),e.append("websiteUrl",_.websiteUrl),e.append("category",_.category),e.append("tags",JSON.stringify(_.tags)),e.append("pricingModel",_.pricingModel),_.logoFile&&e.append("logo",_.logoFile),(await fetch("/api/tools/submit",{method:"POST",body:e})).ok?(U("success"),k({name:"",tagline:"",description:"",websiteUrl:"",logoFile:null,category:"",tags:[],pricingModel:""}),F(null),setTimeout(()=>{N.push("/profile/submitted")},2e3)):U("error")}catch(e){console.error("Submit error:",e),U("error")}finally{M(!1)}};return(0,r.jsxs)(a.Fragment,{children:["success"===S&&(0,r.jsx)(c.A,{message:y("form.success_message")}),"error"===S&&(0,r.jsx)(d.A,{message:y("form.error_message")}),(0,r.jsxs)("form",{onSubmit:z,className:"space-y-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:y("form.basic_info")}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[y("form.tool_name")," *"]}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",value:_.name,onChange:P,placeholder:y("form.tool_name_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"tagline",className:"block text-sm font-medium text-gray-700 mb-2",children:y("form.tagline")}),(0,r.jsx)("input",{type:"text",id:"tagline",name:"tagline",value:_.tagline,onChange:P,placeholder:y("form.tagline_placeholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"websiteUrl",className:"block text-sm font-medium text-gray-700 mb-2",children:[y("form.website_url")," *"]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(h,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"url",id:"websiteUrl",name:"websiteUrl",value:_.websiteUrl,onChange:P,placeholder:y("form.website_url_placeholder"),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[y("form.description")," *"]}),(0,r.jsx)("textarea",{id:"description",name:"description",value:_.description,onChange:P,placeholder:y("form.description_placeholder"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:y("form.logo_upload")}),(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,r.jsx)("input",{type:"file",id:"logo",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>{var s;let l=null==(s=e.target.files)?void 0:s[0];if(l){if(!["image/jpeg","image/png","image/gif","image/webp"].includes(l.type))return void alert("Please upload a valid image file (JPEG, PNG, GIF, or WebP)");if(l.size>5242880)return void alert("File size must be less than 5MB");k(e=>({...e,logoFile:l}));let e=new FileReader;e.onload=e=>{var s;F(null==(s=e.target)?void 0:s.result)},e.readAsDataURL(l)}},className:"hidden"}),(0,r.jsxs)("label",{htmlFor:"logo",className:"cursor-pointer",children:[(0,r.jsx)(b.A,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("span",{className:"text-sm text-blue-600 hover:text-blue-500",children:"Click to upload"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:y("form.logo_upload_hint")})]})]})]})}),A&&(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-24 h-24 border border-gray-300 rounded-lg overflow-hidden",children:(0,r.jsx)("img",{src:A,alt:y("form.logo_preview"),className:"w-full h-full object-cover"})})})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:y("form.category_and_pricing")}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:[y("form.category")," *"]}),(0,r.jsxs)("select",{id:"category",name:"category",value:_.category,onChange:P,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,r.jsx)("option",{value:"",children:y("form.category_placeholder")}),w.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:y("form.tags")}),(0,r.jsx)(j.A,{selectedTags:_.tags,onTagsChange:e=>{k(s=>({...s,tags:e}))},maxTags:f.z,placeholder:y("form.tags_placeholder")})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"pricingModel",className:"block text-sm font-medium text-gray-700 mb-2",children:[y("form.pricing_model")," *"]}),(0,r.jsxs)("select",{id:"pricingModel",name:"pricingModel",value:_.pricingModel,onChange:P,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,r.jsx)("option",{value:"",children:y("form.pricing_placeholder")}),u.Y$.map(e=>(0,r.jsx)("option",{value:e.value,children:y("form.".concat(e.value))},e.value))]})]})]})]}),v&&(0,r.jsxs)("div",{className:"mb-8 bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-green-800 mb-2",children:y("form.submitter_info")}),(0,r.jsxs)("p",{className:"text-sm text-green-700",children:[y("form.submitter"),": ",(null==(s=v.user)?void 0:s.name)||(null==(l=v.user)?void 0:l.email)]}),(0,r.jsxs)("p",{className:"text-sm text-green-700",children:[y("form.email"),": ",null==(g=v.user)?void 0:g.email]})]}),(0,r.jsx)("div",{className:"mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(p,{className:"h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:y("form.guidelines_title")}),(0,r.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,r.jsxs)("li",{children:["• ",y("form.guideline_1")]}),(0,r.jsxs)("li",{children:["• ",y("form.guideline_2")]}),(0,r.jsxs)("li",{children:["• ",y("form.guideline_3")]}),(0,r.jsxs)("li",{children:["• ",y("form.guideline_4")]}),(0,r.jsxs)("li",{children:["• ",y("form.guideline_5")]})]})]})]})}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{type:"submit",disabled:C,className:"px-8 py-3 rounded-lg font-medium transition-colors ".concat(C?"bg-gray-400 text-gray-700 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"),children:C?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{size:"sm",className:"mr-2"}),y("form.submitting")]}):y("form.submit_button")})})]}),(0,r.jsx)(m.A,{isOpen:q,onClose:()=>O(!1)})]})}},5339:(e,s,l)=>{"use strict";l.d(s,{A:()=>r});let r=(0,l(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5734:(e,s,l)=>{"use strict";l.d(s,{A:()=>i});var r=l(5155),a=l(646),t=l(4416);function i(e){let{message:s,onClose:l,className:i=""}=e;return(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(i),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-green-800 text-sm",children:s})}),l&&(0,r.jsx)("button",{onClick:l,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,r.jsx)(t.A,{className:"w-4 h-4"})})]})})}},9783:(e,s,l)=>{"use strict";l.d(s,{A:()=>i});var r=l(5155),a=l(5339),t=l(4416);function i(e){let{message:s,onClose:l,className:i=""}=e;return(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(i),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:s})}),l&&(0,r.jsx)("button",{onClick:l,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(t.A,{className:"w-4 h-4"})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6711,3385,6160,2108,1577,4438,8441,1684,7358],()=>s(3929)),_N_E=e.O()}]);