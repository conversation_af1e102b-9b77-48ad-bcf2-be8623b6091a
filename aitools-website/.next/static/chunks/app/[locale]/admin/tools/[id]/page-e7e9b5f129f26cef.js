(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9713],{646:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},687:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(5155),s=r(2388),l=r(7652),n=r(7550);function o(){let e=(0,s.rd)(),t=(0,l.c3)("admin");return(0,a.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors",children:[(0,a.jsx)(n.A,{className:"w-4 h-4 mr-2"}),t("actions.back_to_review")]})}},981:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var a=r(5695),s=r(2115),l=r.t(s,2),n=r(3385),o=l["use".trim()],c=r(3225),i=r(6160),d=r(469),u=r(5155),m=r(8986);function h(e){let{Link:t,config:r,getPathname:l,...h}=function(e,t){var r,l,n;let m={...r=t||{},localePrefix:"object"==typeof(n=r.localePrefix)?n:{mode:n||"always"},localeCookie:!!((l=r.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof l&&l},localeDetection:r.localeDetection??!0,alternateLinks:r.alternateLinks??!0},h=m.pathnames,f=(0,s.forwardRef)(function({href:t,locale:r,...a},s){let l,n;"object"==typeof t?(l=t.pathname,n=t.params):l=t;let d=(0,c._x)(t),f=e(),p=(0,c.yL)(f)?o(f):f,g=d?x({locale:r||p,href:null==h?l:{pathname:l,params:n},forcePrefix:null!=r||void 0}):l;return(0,u.jsx)(i.default,{ref:s,href:"object"==typeof t?{...t,pathname:g}:g,locale:r,localeCookie:m.localeCookie,...a})});function x(e){let t,{forcePrefix:r,href:a,locale:s}=e;return null==h?"object"==typeof a?(t=a.pathname,a.query&&(t+=(0,d.Zn)(a.query))):t=a:t=(0,d.FP)({locale:s,...(0,d.TK)(a),pathnames:m.pathnames}),(0,d.x3)(t,s,m,r)}function p(e){return function(t,...r){return e(x(t),...r)}}return{config:m,Link:f,redirect:p(a.redirect),permanentRedirect:p(a.permanentRedirect),getPathname:x}}(n.Ym,e);return{...h,Link:t,usePathname:function(){let e=function(e){let t=(0,a.usePathname)(),r=(0,n.Ym)();return(0,s.useMemo)(()=>{if(!t)return t;let a=t,s=(0,c.XP)(r,e.localePrefix);if((0,c.wO)(s,t))a=(0,c.MY)(t,s);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,c.bL)(r);(0,c.wO)(e,t)&&(a=(0,c.MY)(t,e))}return a},[e.localePrefix,r,t])}(r),t=(0,n.Ym)();return(0,s.useMemo)(()=>e&&r.pathnames?(0,d.aM)(t,e,r.pathnames):e,[t,e])},useRouter:function(){let e=(0,a.useRouter)(),t=(0,n.Ym)(),o=(0,a.usePathname)();return(0,s.useMemo)(()=>{function a(e){return function(a,s){let{locale:n,...c}=s||{},i=[l({href:a,locale:n||t})];Object.keys(c).length>0&&i.push(c),e(...i),(0,m.A)(r.localeCookie,o,t,n)}}return{...e,push:a(e.push),replace:a(e.replace),prefetch:a(e.prefetch)}},[t,o,e])},getPathname:l}}},2388:(e,t,r)=>{"use strict";r.d(t,{N_:()=>n,a8:()=>c,rd:()=>i});var a=r(9984),s=r(981);let l=(0,a.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:n,redirect:o,usePathname:c,useRouter:i}=(0,s.A)(l)},3219:(e,t,r)=>{Promise.resolve().then(r.bind(r,5499)),Promise.resolve().then(r.bind(r,687))},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4861:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5339:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5499:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var a=r(5155),s=r(2115),l=r(2388),n=r(7652),o=r(9783),c=r(5734),i=r(646),d=r(4861);function u(e){let{tool:t,locale:r}=e,u=(0,l.rd)(),m=(0,n.c3)("admin"),[h,f]=(0,s.useState)(""),[x,p]=(0,s.useState)(""),[g,b]=(0,s.useState)(!1),[y,j]=(0,s.useState)(""),[k,w]=(0,s.useState)(!1),v=async()=>{w(!0);try{f("");let e=await fetch("/api/admin/tools/".concat(t._id,"/approve"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({reviewedBy:"admin",reviewNotes:m("success.tool_approved"),launchDate:new Date().toISOString()})}),r=await e.json();r.success?(p(m("success.tool_approved")),u.refresh()):f(r.error||m("errors.approve_failed"))}catch(e){f(m("errors.network_error"))}finally{w(!1)}},N=async()=>{if(!y.trim())return void f(m("errors.reject_reason_required"));w(!0);try{f("");let e=await fetch("/api/admin/tools/".concat(t._id,"/reject"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({reviewedBy:"admin",rejectReason:y})}),r=await e.json();r.success?(p(m("success.tool_rejected")),b(!1),j(""),u.refresh()):f(r.error||m("errors.reject_failed"))}catch(e){f(m("errors.network_error"))}finally{w(!1)}};return(0,a.jsxs)(a.Fragment,{children:[x&&(0,a.jsx)(c.A,{message:x,onClose:()=>p(""),className:"mb-6"}),h&&(0,a.jsx)(o.A,{message:h,onClose:()=>f(""),className:"mb-6"}),"pending"===t.status&&(0,a.jsxs)("div",{className:"flex space-x-3 mb-6",children:[(0,a.jsxs)("button",{onClick:v,disabled:k,className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,a.jsx)(i.A,{className:"w-4 h-4 mr-2"}),k?m("actions.processing"):m("actions.approve")]}),(0,a.jsxs)("button",{onClick:()=>b(!0),disabled:k,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2"}),m("actions.reject")]})]}),g&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:m("reject_modal.title")}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:m("reject_modal.description")}),(0,a.jsx)("textarea",{value:y,onChange:e=>j(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:m("reject_modal.placeholder")}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,a.jsx)("button",{onClick:()=>{b(!1),j("")},disabled:k,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors",children:m("actions.cancel")}),(0,a.jsx)("button",{onClick:N,disabled:!y.trim()||k,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:k?m("actions.processing"):m("actions.confirm_reject")})]})]})})]})}},5734:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(5155),s=r(646),l=r(4416);function n(e){let{message:t,onClose:r,className:n=""}=e;return(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(n),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(s.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-green-800 text-sm",children:t})}),r&&(0,a.jsx)("button",{onClick:r,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})]})})}},7550:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7652:(e,t,r)=>{"use strict";r.d(t,{c3:()=>l});var a=r(3385);function s(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let l=s(0,a.c3);s(0,a.kc)},9783:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(5155),s=r(5339),l=r(4416);function n(e){let{message:t,onClose:r,className:n=""}=e;return(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(n),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(s.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-red-800 text-sm",children:t})}),r&&(0,a.jsx)("button",{onClick:r,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})]})})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:d="",children:u,iconNode:m,...h}=e;return(0,a.createElement)("svg",{ref:t,...i,width:s,height:s,stroke:r,strokeWidth:n?24*Number(l)/Number(s):l,className:o("lucide",d),...!u&&!c(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),u=(e,t)=>{let r=(0,a.forwardRef)((r,l)=>{let{className:c,...i}=r;return(0,a.createElement)(d,{ref:l,iconNode:t,className:o("lucide-".concat(s(n(e))),"lucide-".concat(e),c),...i})});return r.displayName=n(e),r}},9984:(e,t,r)=>{"use strict";function a(e){return e}r.d(t,{A:()=>a})}},e=>{var t=t=>e(e.s=t);e.O(0,[3385,6160,8441,1684,7358],()=>t(3219)),_N_E=e.O()}]);