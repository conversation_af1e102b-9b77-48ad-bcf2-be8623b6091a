(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5230],{2839:(e,t,r)=>{"use strict";r.d(t,{u:()=>d});var s=r(9509);function a(){if(s.env.NEXT_PUBLIC_APP_URL)return s.env.NEXT_PUBLIC_APP_URL;{let{protocol:e,hostname:t,port:r}=window.location;return"".concat(e,"//").concat(t).concat(r?":".concat(r):"")}}function o(){if(s.env.NEXT_PUBLIC_API_BASE_URL)return s.env.NEXT_PUBLIC_API_BASE_URL;let e=a();return"".concat(e,"/api")}function n(){return"production"}function c(){return"development"===n()}a(),o(),s.env.NEXTAUTH_URL?s.env.NEXTAUTH_URL:a(),n(),c(),n(),window.location.port||window.location.protocol,c();let l=o();class i{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let r="".concat(this.baseURL).concat(e),s={headers:{"Content-Type":"application/json",...t.headers},...t},a=await fetch(r,s),o=await a.json();if(!a.ok)throw Error(o.error||"HTTP error! status: ".concat(a.status));return o}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[r,s]=e;void 0!==s&&t.append(r,s.toString())});let r=t.toString();return this.request("/tools".concat(r?"?".concat(r):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[r,s]=e;void 0!==s&&t.append(r,s.toString())});let r=t.toString();return this.request("/user/liked-tools".concat(r?"?".concat(r):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[r,s]=e;void 0!==s&&t.append(r,s.toString())});let r=t.toString();return this.request("/admin/tools".concat(r?"?".concat(r):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}constructor(e=l){this.baseURL=e}}let d=new i},4871:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(5155),a=r(2115),o=r(2388),n=r(5695),c=r(7652),l=r(7797),i=r(2839),d=r(7924);function u(e){var t;let{initialQuery:r,initialResults:u,initialCategories:h,initialPage:m=1,initialCategory:g="",initialSort:x="createdAt",locale:p}=e,y=(0,o.rd)(),b=(0,n.useSearchParams)(),f=(0,c.c3)("search"),[v,j]=(0,a.useState)(r),[N,_]=(0,a.useState)(u),[S]=(0,a.useState)(h),[w,P]=(0,a.useState)(g),[T,E]=(0,a.useState)(x),[L,U]=(0,a.useState)("grid"),[A,q]=(0,a.useState)(!1),[O,R]=(0,a.useState)(m),k=e=>{let t=new URLSearchParams(b||"");Object.entries(e).forEach(e=>{let[r,s]=e;s?t.set(r,s):t.delete(r)}),y.push("/search?".concat(t.toString()))},C=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"createdAt";q(!0);try{let a=await i.u.getTools({search:e,page:t,limit:12,category:r||void 0,sort:s,order:"desc"});a.success?_(a.data||null):(console.error("Search failed:",a.error),_(null))}catch(e){console.error("Search error:",e),_(null)}finally{q(!1)}},I=e=>{R(e),k({q:v,page:e.toString(),category:w,sort:T}),C(v,e,w,T)};return(0,a.useEffect)(()=>{v.trim()||C("",1,w,T)},[v,w,T]),(0,s.jsx)(a.Fragment,{children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:f("title")}),v&&(0,s.jsxs)("p",{className:"text-lg text-gray-600",children:[f("search_results",{term:v}),N&&" - ".concat(f("found_tools",{count:N.pagination.totalItems}))]})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:(0,s.jsx)("form",{onSubmit:e=>{null==e||e.preventDefault(),R(1),k({q:null==v?void 0:v.trim(),page:"1",category:w,sort:T}),C(null==v?void 0:v.trim(),1,w,T)},children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",placeholder:f("placeholder"),value:v,onChange:e=>j(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)(d.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,s.jsxs)("button",{type:"submit",className:"absolute right-2 top-2 px-4 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",style:{whiteSpace:"nowrap",marginLeft:10},children:[!v.trim()&&f("all"),v.trim()&&f("search_button")]})]})})}),!v&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 mb-4",children:(0,s.jsx)(d.A,{className:"h-12 w-12 mx-auto"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:f("start_search_title")}),(0,s.jsx)("p",{className:"text-gray-600",children:f("start_search_desc")})]}),A&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:f("searching")})]}),!A&&N&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("p",{className:"text-gray-600",children:[f("results_count",{showing:N.tools.length,total:N.pagination.totalItems}),w&&" ".concat(f("in_category",{category:(null==(t=S.find(e=>e.id===w))?void 0:t.name)||""}))]})}),N.tools.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"grid"===L?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8":"space-y-4 mb-8",children:N.tools.map(e=>(0,s.jsx)(l.default,{tool:e},e._id))}),N.pagination.totalPages>1&&(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>I(O-1),disabled:!N.pagination.hasPrevPage,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:f("prev_page")}),(0,s.jsx)("span",{className:"px-3 py-2 text-sm text-gray-700",children:f("page_info",{current:O,total:N.pagination.totalPages})}),(0,s.jsx)("button",{onClick:()=>I(O+1),disabled:!N.pagination.hasNextPage,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:f("next_page")})]})})]}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 mb-4",children:(0,s.jsx)(d.A,{className:"h-12 w-12 mx-auto"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:f("no_results")}),(0,s.jsx)("p",{className:"text-gray-600",children:f("try_different_keywords")})]})]})]})})}},7915:(e,t,r)=>{Promise.resolve().then(r.bind(r,4871))},7924:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,3385,6160,2108,5002,6766,7797,8441,1684,7358],()=>t(7915)),_N_E=e.O()}]);