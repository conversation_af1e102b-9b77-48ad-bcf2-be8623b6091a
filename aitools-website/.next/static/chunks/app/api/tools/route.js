/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/api/tools/route"],{

/***/ "(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@formatjs/fast-memoize/lib/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   memoize: () => (/* binding */ memoize),\n/* harmony export */   strategies: () => (/* binding */ strategies)\n/* harmony export */ });\n//\n// Main\n//\nfunction memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nvar ObjectWithoutPrototypeCache = /** @class */ (function () {\n    function ObjectWithoutPrototypeCache() {\n        this.cache = Object.create(null);\n    }\n    ObjectWithoutPrototypeCache.prototype.get = function (key) {\n        return this.cache[key];\n    };\n    ObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n        this.cache[key] = value;\n    };\n    return ObjectWithoutPrototypeCache;\n}());\nvar cacheDefault = {\n    create: function create() {\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nvar strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBestPattern: () => (/* binding */ getBestPattern)\n/* harmony export */ });\n/* harmony import */ var _time_data_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./time-data.generated */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\");\n\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nfunction getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[regionTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[languageTag || ''] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData[\"\".concat(languageTag, \"-001\")] ||\n        _time_data_generated__WEBPACK_IMPORTED_MODULE_0__.timeData['001'];\n    return hourCycles[0];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/error.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/error.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorKind: () => (/* binding */ ErrorKind)\n/* harmony export */ });\nvar ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/index.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.TYPE),\n/* harmony export */   _Parser: () => (/* binding */ _Parser),\n/* harmony export */   createLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isArgumentElement),\n/* harmony export */   isDateElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement),\n/* harmony export */   isPoundElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isPoundElement),\n/* harmony export */   isSelectElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement),\n/* harmony export */   isStructurallySame: () => (/* reexport safe */ _manipulator__WEBPACK_IMPORTED_MODULE_4__.isStructurallySame),\n/* harmony export */   isTagElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTagElement),\n/* harmony export */   isTimeElement: () => (/* reexport safe */ _types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parser */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _manipulator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./manipulator */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\");\n\n\n\n\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isSelectElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isPluralElement)(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberElement)(el) && (0,_types__WEBPACK_IMPORTED_MODULE_2__.isNumberSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if (((0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_2__.isTimeElement)(el)) &&\n            (0,_types__WEBPACK_IMPORTED_MODULE_2__.isDateTimeSkeleton)(el.style)) {\n            delete el.style.location;\n        }\n        else if ((0,_types__WEBPACK_IMPORTED_MODULE_2__.isTagElement)(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nfunction parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = (0,tslib__WEBPACK_IMPORTED_MODULE_3__.__assign)({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new _parser__WEBPACK_IMPORTED_MODULE_1__.Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\n\n// only for testing\nvar _Parser = _parser__WEBPACK_IMPORTED_MODULE_1__.Parser;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   hoistSelectors: () => (/* binding */ hoistSelectors),\n/* harmony export */   isStructurallySame: () => (/* binding */ isStructurallySame)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n\n\nfunction cloneDeep(obj) {\n    if (Array.isArray(obj)) {\n        // @ts-expect-error meh\n        return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], obj.map(cloneDeep), true);\n    }\n    if (obj !== null && typeof obj === 'object') {\n        // @ts-expect-error meh\n        return Object.keys(obj).reduce(function (cloned, k) {\n            // @ts-expect-error meh\n            cloned[k] = cloneDeep(obj[k]);\n            return cloned;\n        }, {});\n    }\n    return obj;\n}\nfunction hoistPluralOrSelectElement(ast, el, positionToInject) {\n    // pull this out of the ast and move it to the top\n    var cloned = cloneDeep(el);\n    var options = cloned.options;\n    cloned.options = Object.keys(options).reduce(function (all, k) {\n        var newValue = hoistSelectors((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__spreadArray)([], ast.slice(0, positionToInject), true), options[k].value, true), ast.slice(positionToInject + 1), true));\n        all[k] = {\n            value: newValue,\n        };\n        return all;\n    }, {});\n    return cloned;\n}\nfunction isPluralOrSelectElement(el) {\n    return (0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el);\n}\nfunction findPluralOrSelectElement(ast) {\n    return !!ast.find(function (el) {\n        if (isPluralOrSelectElement(el)) {\n            return true;\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            return findPluralOrSelectElement(el.children);\n        }\n        return false;\n    });\n}\n/**\n * Hoist all selectors to the beginning of the AST & flatten the\n * resulting options. E.g:\n * \"I have {count, plural, one{a dog} other{many dogs}}\"\n * becomes \"{count, plural, one{I have a dog} other{I have many dogs}}\".\n * If there are multiple selectors, the order of which one is hoisted 1st\n * is non-deterministic.\n * The goal is to provide as many full sentences as possible since fragmented\n * sentences are not translator-friendly\n * @param ast AST\n */\nfunction hoistSelectors(ast) {\n    for (var i = 0; i < ast.length; i++) {\n        var el = ast[i];\n        if (isPluralOrSelectElement(el)) {\n            return [hoistPluralOrSelectElement(ast, el, i)];\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el) && findPluralOrSelectElement([el])) {\n            throw new Error('Cannot hoist plural/select within a tag element. Please put the tag element inside each plural/select option');\n        }\n    }\n    return ast;\n}\n/**\n * Collect all variables in an AST to Record<string, TYPE>\n * @param ast AST to collect variables from\n * @param vars Record of variable name to variable type\n */\nfunction collectVariables(ast, vars) {\n    if (vars === void 0) { vars = new Map(); }\n    ast.forEach(function (el) {\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el) ||\n            (0,_types__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            if (el.value in vars && vars.get(el.value) !== el.type) {\n                throw new Error(\"Variable \".concat(el.value, \" has conflicting types\"));\n            }\n            vars.set(el.value, el.type);\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el) || (0,_types__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            vars.set(el.value, el.type);\n            Object.keys(el.options).forEach(function (k) {\n                collectVariables(el.options[k].value, vars);\n            });\n        }\n        if ((0,_types__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            vars.set(el.value, el.type);\n            collectVariables(el.children, vars);\n        }\n    });\n}\n/**\n * Check if 2 ASTs are structurally the same. This primarily means that\n * they have the same variables with the same type\n * @param a\n * @param b\n * @returns\n */\nfunction isStructurallySame(a, b) {\n    var aVars = new Map();\n    var bVars = new Map();\n    collectVariables(a, aVars);\n    collectVariables(b, bVars);\n    if (aVars.size !== bVars.size) {\n        return {\n            success: false,\n            error: new Error(\"Different number of variables: [\".concat(Array.from(aVars.keys()).join(', '), \"] vs [\").concat(Array.from(bVars.keys()).join(', '), \"]\")),\n        };\n    }\n    return Array.from(aVars.entries()).reduce(function (result, _a) {\n        var key = _a[0], type = _a[1];\n        if (!result.success) {\n            return result;\n        }\n        var bType = bVars.get(key);\n        if (bType == null) {\n            return {\n                success: false,\n                error: new Error(\"Missing variable \".concat(key, \" in message\")),\n            };\n        }\n        if (bType !== type) {\n            return {\n                success: false,\n                error: new Error(\"Variable \".concat(key, \" has conflicting types: \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[type], \" vs \").concat(_types__WEBPACK_IMPORTED_MODULE_0__.TYPE[bType])),\n            };\n        }\n        return result;\n    }, { success: true });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/manipulator.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/parser.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/parser.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Parser: () => (/* binding */ Parser)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/error.js\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./regex.generated */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\");\n/* harmony import */ var _formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @formatjs/icu-skeleton-parser */ \"(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/index.js\");\n/* harmony import */ var _date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./date-time-pattern-generator */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js\");\nvar _a;\n\n\n\n\n\n\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(_regex_generated__WEBPACK_IMPORTED_MODULE_2__.SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = (0,_date_time_pattern_generator__WEBPACK_IMPORTED_MODULE_4__.getBestPattern)(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseDateTimeSkeleton)(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.number\n                            : argType === 'date'\n                                ? _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.date\n                                : _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, (0,tslib__WEBPACK_IMPORTED_MODULE_5__.__assign)({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: _types__WEBPACK_IMPORTED_MODULE_1__.TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeletonFromString)(skeleton);\n        }\n        catch (e) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: _types__WEBPACK_IMPORTED_MODULE_1__.SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? (0,_formatjs_icu_skeleton_parser__WEBPACK_IMPORTED_MODULE_3__.parseNumberSkeleton)(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : _error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(_error__WEBPACK_IMPORTED_MODULE_0__.ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\n\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/parser.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SPACE_SEPARATOR_REGEX: () => (/* binding */ SPACE_SEPARATOR_REGEX),\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LW1lc3NhZ2Vmb3JtYXQtcGFyc2VyL2xpYi9yZWdleC5nZW5lcmF0ZWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNPO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvQGZvcm1hdGpzL2ljdS1tZXNzYWdlZm9ybWF0LXBhcnNlci9saWIvcmVnZXguZ2VuZXJhdGVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEBnZW5lcmF0ZWQgZnJvbSByZWdleC1nZW4udHNcbmV4cG9ydCB2YXIgU1BBQ0VfU0VQQVJBVE9SX1JFR0VYID0gL1sgXFx4QTBcXHUxNjgwXFx1MjAwMC1cXHUyMDBBXFx1MjAyRlxcdTIwNUZcXHUzMDAwXS87XG5leHBvcnQgdmFyIFdISVRFX1NQQUNFX1JFR0VYID0gL1tcXHQtXFxyIFxceDg1XFx1MjAwRVxcdTIwMEZcXHUyMDI4XFx1MjAyOV0vO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js":
/*!************************************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeData: () => (/* binding */ timeData)\n/* harmony export */ });\n// @generated from time-data-gen.ts\n// prettier-ignore  \nvar timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"419\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-HK\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"en-IL\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"en-MY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@formatjs/icu-messageformat-parser/lib/types.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SKELETON_TYPE: () => (/* binding */ SKELETON_TYPE),\n/* harmony export */   TYPE: () => (/* binding */ TYPE),\n/* harmony export */   createLiteralElement: () => (/* binding */ createLiteralElement),\n/* harmony export */   createNumberElement: () => (/* binding */ createNumberElement),\n/* harmony export */   isArgumentElement: () => (/* binding */ isArgumentElement),\n/* harmony export */   isDateElement: () => (/* binding */ isDateElement),\n/* harmony export */   isDateTimeSkeleton: () => (/* binding */ isDateTimeSkeleton),\n/* harmony export */   isLiteralElement: () => (/* binding */ isLiteralElement),\n/* harmony export */   isNumberElement: () => (/* binding */ isNumberElement),\n/* harmony export */   isNumberSkeleton: () => (/* binding */ isNumberSkeleton),\n/* harmony export */   isPluralElement: () => (/* binding */ isPluralElement),\n/* harmony export */   isPoundElement: () => (/* binding */ isPoundElement),\n/* harmony export */   isSelectElement: () => (/* binding */ isSelectElement),\n/* harmony export */   isTagElement: () => (/* binding */ isTagElement),\n/* harmony export */   isTimeElement: () => (/* binding */ isTimeElement)\n/* harmony export */ });\nvar TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nvar SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nfunction isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nfunction isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nfunction isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nfunction isDateElement(el) {\n    return el.type === TYPE.date;\n}\nfunction isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nfunction isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nfunction isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nfunction isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nfunction isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nfunction isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nfunction isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nfunction createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nfunction createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/types.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* binding */ parseDateTimeSkeleton)\n/* harmony export */ });\n/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nfunction parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvZGF0ZS10aW1lLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLElBQUksR0FBRyxJQUFJLE1BQU0sSUFBSSxjQUFjLElBQUksT0FBTyxJQUFJLEdBQUcsSUFBSSxHQUFHLElBQUksR0FBRyxFQUFFLE9BQU8sSUFBSSxRQUFRLElBQUksR0FBRyxJQUFJLEdBQUcsRUFBRSxHQUFHLElBQUksR0FBRyxJQUFJLFdBQVcsSUFBSTtBQUNySztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3Utc2tlbGV0b24tcGFyc2VyL2xpYi9kYXRlLXRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBodHRwczovL3VuaWNvZGUub3JnL3JlcG9ydHMvdHIzNS90cjM1LWRhdGVzLmh0bWwjRGF0ZV9GaWVsZF9TeW1ib2xfVGFibGVcbiAqIENyZWRpdDogaHR0cHM6Ly9naXRodWIuY29tL2NhcmlkeS9pbnRsLWRhdGV0aW1lZm9ybWF0LXBhdHRlcm4vYmxvYi9tYXN0ZXIvaW5kZXguanNcbiAqIHdpdGggc29tZSB0d2Vha3NcbiAqL1xudmFyIERBVEVfVElNRV9SRUdFWCA9IC8oPzpbRWVjXXsxLDZ9fEd7MSw1fXxbUXFdezEsNX18KD86W3lZdXJdK3xVezEsNX0pfFtNTF17MSw1fXxkezEsMn18RHsxLDN9fEZ7MX18W2FiQl17MSw1fXxbaGtIS117MSwyfXx3ezEsMn18V3sxfXxtezEsMn18c3sxLDJ9fFt6Wk92VnhYXXsxLDR9KSg/PShbXiddKidbXiddKicpKlteJ10qJCkvZztcbi8qKlxuICogUGFyc2UgRGF0ZSB0aW1lIHNrZWxldG9uIGludG8gSW50bC5EYXRlVGltZUZvcm1hdE9wdGlvbnNcbiAqIFJlZjogaHR0cHM6Ly91bmljb2RlLm9yZy9yZXBvcnRzL3RyMzUvdHIzNS1kYXRlcy5odG1sI0RhdGVfRmllbGRfU3ltYm9sX1RhYmxlXG4gKiBAcHVibGljXG4gKiBAcGFyYW0gc2tlbGV0b24gc2tlbGV0b24gc3RyaW5nXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZURhdGVUaW1lU2tlbGV0b24oc2tlbGV0b24pIHtcbiAgICB2YXIgcmVzdWx0ID0ge307XG4gICAgc2tlbGV0b24ucmVwbGFjZShEQVRFX1RJTUVfUkVHRVgsIGZ1bmN0aW9uIChtYXRjaCkge1xuICAgICAgICB2YXIgbGVuID0gbWF0Y2gubGVuZ3RoO1xuICAgICAgICBzd2l0Y2ggKG1hdGNoWzBdKSB7XG4gICAgICAgICAgICAvLyBFcmFcbiAgICAgICAgICAgIGNhc2UgJ0cnOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5lcmEgPSBsZW4gPT09IDQgPyAnbG9uZycgOiBsZW4gPT09IDUgPyAnbmFycm93JyA6ICdzaG9ydCc7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAvLyBZZWFyXG4gICAgICAgICAgICBjYXNlICd5JzpcbiAgICAgICAgICAgICAgICByZXN1bHQueWVhciA9IGxlbiA9PT0gMiA/ICcyLWRpZ2l0JyA6ICdudW1lcmljJztcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgJ1knOlxuICAgICAgICAgICAgY2FzZSAndSc6XG4gICAgICAgICAgICBjYXNlICdVJzpcbiAgICAgICAgICAgIGNhc2UgJ3InOlxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKCdgWS91L1UvcmAgKHllYXIpIHBhdHRlcm5zIGFyZSBub3Qgc3VwcG9ydGVkLCB1c2UgYHlgIGluc3RlYWQnKTtcbiAgICAgICAgICAgIC8vIFF1YXJ0ZXJcbiAgICAgICAgICAgIGNhc2UgJ3EnOlxuICAgICAgICAgICAgY2FzZSAnUSc6XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ2BxL1FgIChxdWFydGVyKSBwYXR0ZXJucyBhcmUgbm90IHN1cHBvcnRlZCcpO1xuICAgICAgICAgICAgLy8gTW9udGhcbiAgICAgICAgICAgIGNhc2UgJ00nOlxuICAgICAgICAgICAgY2FzZSAnTCc6XG4gICAgICAgICAgICAgICAgcmVzdWx0Lm1vbnRoID0gWydudW1lcmljJywgJzItZGlnaXQnLCAnc2hvcnQnLCAnbG9uZycsICduYXJyb3cnXVtsZW4gLSAxXTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIC8vIFdlZWtcbiAgICAgICAgICAgIGNhc2UgJ3cnOlxuICAgICAgICAgICAgY2FzZSAnVyc6XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ2B3L1dgICh3ZWVrKSBwYXR0ZXJucyBhcmUgbm90IHN1cHBvcnRlZCcpO1xuICAgICAgICAgICAgY2FzZSAnZCc6XG4gICAgICAgICAgICAgICAgcmVzdWx0LmRheSA9IFsnbnVtZXJpYycsICcyLWRpZ2l0J11bbGVuIC0gMV07XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBjYXNlICdEJzpcbiAgICAgICAgICAgIGNhc2UgJ0YnOlxuICAgICAgICAgICAgY2FzZSAnZyc6XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ2BEL0YvZ2AgKGRheSkgcGF0dGVybnMgYXJlIG5vdCBzdXBwb3J0ZWQsIHVzZSBgZGAgaW5zdGVhZCcpO1xuICAgICAgICAgICAgLy8gV2Vla2RheVxuICAgICAgICAgICAgY2FzZSAnRSc6XG4gICAgICAgICAgICAgICAgcmVzdWx0LndlZWtkYXkgPSBsZW4gPT09IDQgPyAnbG9uZycgOiBsZW4gPT09IDUgPyAnbmFycm93JyA6ICdzaG9ydCc7XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBjYXNlICdlJzpcbiAgICAgICAgICAgICAgICBpZiAobGVuIDwgNCkge1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgUmFuZ2VFcnJvcignYGUuLmVlZWAgKHdlZWtkYXkpIHBhdHRlcm5zIGFyZSBub3Qgc3VwcG9ydGVkJyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJlc3VsdC53ZWVrZGF5ID0gWydzaG9ydCcsICdsb25nJywgJ25hcnJvdycsICdzaG9ydCddW2xlbiAtIDRdO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAnYyc6XG4gICAgICAgICAgICAgICAgaWYgKGxlbiA8IDQpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ2BjLi5jY2NgICh3ZWVrZGF5KSBwYXR0ZXJucyBhcmUgbm90IHN1cHBvcnRlZCcpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXN1bHQud2Vla2RheSA9IFsnc2hvcnQnLCAnbG9uZycsICduYXJyb3cnLCAnc2hvcnQnXVtsZW4gLSA0XTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIC8vIFBlcmlvZFxuICAgICAgICAgICAgY2FzZSAnYSc6IC8vIEFNLCBQTVxuICAgICAgICAgICAgICAgIHJlc3VsdC5ob3VyMTIgPSB0cnVlO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAnYic6IC8vIGFtLCBwbSwgbm9vbiwgbWlkbmlnaHRcbiAgICAgICAgICAgIGNhc2UgJ0InOiAvLyBmbGV4aWJsZSBkYXkgcGVyaW9kc1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKCdgYi9CYCAocGVyaW9kKSBwYXR0ZXJucyBhcmUgbm90IHN1cHBvcnRlZCwgdXNlIGBhYCBpbnN0ZWFkJyk7XG4gICAgICAgICAgICAvLyBIb3VyXG4gICAgICAgICAgICBjYXNlICdoJzpcbiAgICAgICAgICAgICAgICByZXN1bHQuaG91ckN5Y2xlID0gJ2gxMic7XG4gICAgICAgICAgICAgICAgcmVzdWx0LmhvdXIgPSBbJ251bWVyaWMnLCAnMi1kaWdpdCddW2xlbiAtIDFdO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAnSCc6XG4gICAgICAgICAgICAgICAgcmVzdWx0LmhvdXJDeWNsZSA9ICdoMjMnO1xuICAgICAgICAgICAgICAgIHJlc3VsdC5ob3VyID0gWydudW1lcmljJywgJzItZGlnaXQnXVtsZW4gLSAxXTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgJ0snOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5ob3VyQ3ljbGUgPSAnaDExJztcbiAgICAgICAgICAgICAgICByZXN1bHQuaG91ciA9IFsnbnVtZXJpYycsICcyLWRpZ2l0J11bbGVuIC0gMV07XG4gICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICBjYXNlICdrJzpcbiAgICAgICAgICAgICAgICByZXN1bHQuaG91ckN5Y2xlID0gJ2gyNCc7XG4gICAgICAgICAgICAgICAgcmVzdWx0LmhvdXIgPSBbJ251bWVyaWMnLCAnMi1kaWdpdCddW2xlbiAtIDFdO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAnaic6XG4gICAgICAgICAgICBjYXNlICdKJzpcbiAgICAgICAgICAgIGNhc2UgJ0MnOlxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKCdgai9KL0NgIChob3VyKSBwYXR0ZXJucyBhcmUgbm90IHN1cHBvcnRlZCwgdXNlIGBoL0gvSy9rYCBpbnN0ZWFkJyk7XG4gICAgICAgICAgICAvLyBNaW51dGVcbiAgICAgICAgICAgIGNhc2UgJ20nOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5taW51dGUgPSBbJ251bWVyaWMnLCAnMi1kaWdpdCddW2xlbiAtIDFdO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgLy8gU2Vjb25kXG4gICAgICAgICAgICBjYXNlICdzJzpcbiAgICAgICAgICAgICAgICByZXN1bHQuc2Vjb25kID0gWydudW1lcmljJywgJzItZGlnaXQnXVtsZW4gLSAxXTtcbiAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgIGNhc2UgJ1MnOlxuICAgICAgICAgICAgY2FzZSAnQSc6XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ2BTL0FgIChzZWNvbmQpIHBhdHRlcm5zIGFyZSBub3Qgc3VwcG9ydGVkLCB1c2UgYHNgIGluc3RlYWQnKTtcbiAgICAgICAgICAgIC8vIFpvbmVcbiAgICAgICAgICAgIGNhc2UgJ3onOiAvLyAxLi4zLCA0OiBzcGVjaWZpYyBub24tbG9jYXRpb24gZm9ybWF0XG4gICAgICAgICAgICAgICAgcmVzdWx0LnRpbWVab25lTmFtZSA9IGxlbiA8IDQgPyAnc2hvcnQnIDogJ2xvbmcnO1xuICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAnWic6IC8vIDEuLjMsIDQsIDU6IFRoZSBJU084NjAxIHZhcmlvcyBmb3JtYXRzXG4gICAgICAgICAgICBjYXNlICdPJzogLy8gMSwgNDogbWlsbGlzZWNvbmRzIGluIGRheSBzaG9ydCwgbG9uZ1xuICAgICAgICAgICAgY2FzZSAndic6IC8vIDEsIDQ6IGdlbmVyaWMgbm9uLWxvY2F0aW9uIGZvcm1hdFxuICAgICAgICAgICAgY2FzZSAnVic6IC8vIDEsIDIsIDMsIDQ6IHRpbWUgem9uZSBJRCBvciBjaXR5XG4gICAgICAgICAgICBjYXNlICdYJzogLy8gMSwgMiwgMywgNDogVGhlIElTTzg2MDEgdmFyaW9zIGZvcm1hdHNcbiAgICAgICAgICAgIGNhc2UgJ3gnOiAvLyAxLCAyLCAzLCA0OiBUaGUgSVNPODYwMSB2YXJpb3MgZm9ybWF0c1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBSYW5nZUVycm9yKCdgWi9PL3YvVi9YL3hgICh0aW1lWm9uZSkgcGF0dGVybnMgYXJlIG5vdCBzdXBwb3J0ZWQsIHVzZSBgemAgaW5zdGVhZCcpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiAnJztcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzdWx0O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/index.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@formatjs/icu-skeleton-parser/lib/index.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* reexport safe */ _date_time__WEBPACK_IMPORTED_MODULE_0__.parseDateTimeSkeleton),\n/* harmony export */   parseNumberSkeleton: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var _date_time__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./date-time */ \"(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\");\n/* harmony import */ var _number__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number */ \"(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/number.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBNEI7QUFDSCIsInNvdXJjZXMiOlsiL1VzZXJzL3dvb2Qvd29ya3NwYWNlL2FpdG9vbHMvYWl0b29scy13ZWJzaXRlL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9kYXRlLXRpbWUnO1xuZXhwb3J0ICogZnJvbSAnLi9udW1iZXInO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/index.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/number.js":
/*!******************************************************************!*\
  !*** ./node_modules/@formatjs/icu-skeleton-parser/lib/number.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNumberSkeleton: () => (/* binding */ parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* binding */ parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.generated */ \"(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\");\n\n\nfunction parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(_regex_generated__WEBPACK_IMPORTED_MODULE_0__.WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nfunction parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/number.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AZm9ybWF0anMvaWN1LXNrZWxldG9uLXBhcnNlci9saWIvcmVnZXguZ2VuZXJhdGVkLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPIiwic291cmNlcyI6WyIvVXNlcnMvd29vZC93b3Jrc3BhY2UvYWl0b29scy9haXRvb2xzLXdlYnNpdGUvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3Utc2tlbGV0b24tcGFyc2VyL2xpYi9yZWdleC5nZW5lcmF0ZWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQGdlbmVyYXRlZCBmcm9tIHJlZ2V4LWdlbi50c1xuZXhwb3J0IHZhciBXSElURV9TUEFDRV9SRUdFWCA9IC9bXFx0LVxcciBcXHg4NVxcdTIwMEVcXHUyMDBGXFx1MjAyOFxcdTIwMjldL2k7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/core.js":
/*!*********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/core.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlMessageFormat: () => (/* binding */ IntlMessageFormat)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _formatters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatters */ \"(app-pages-browser)/./node_modules/intl-messageformat/lib/src/formatters.js\");\n/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\n\n\n\n\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getDateTimeFormat: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n        getPluralRules: (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.memoize)(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__spreadArray)([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_0__.strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        var _this = this;\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== _formatters__WEBPACK_IMPORTED_MODULE_3__.PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return (0,_formatters__WEBPACK_IMPORTED_MODULE_3__.formatToParts)(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__rest)(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, (0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_2__.__assign)({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_1__.parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/intl-messageformat/lib/src/core.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/error.js":
/*!**********************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/error.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FormatError: () => (/* binding */ FormatError),\n/* harmony export */   InvalidValueError: () => (/* binding */ InvalidValueError),\n/* harmony export */   InvalidValueTypeError: () => (/* binding */ InvalidValueTypeError),\n/* harmony export */   MissingValueError: () => (/* binding */ MissingValueError)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tslib */ \"(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\");\n\nvar ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\n\nvar InvalidValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\n\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\n\nvar MissingValueError = /** @class */ (function (_super) {\n    (0,tslib__WEBPACK_IMPORTED_MODULE_0__.__extends)(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/intl-messageformat/lib/src/error.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/intl-messageformat/lib/src/formatters.js":
/*!***************************************************************!*\
  !*** ./node_modules/intl-messageformat/lib/src/formatters.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PART_TYPE: () => (/* binding */ PART_TYPE),\n/* harmony export */   formatToParts: () => (/* binding */ formatToParts),\n/* harmony export */   isFormatXMLElementFn: () => (/* binding */ isFormatXMLElementFn)\n/* harmony export */ });\n/* harmony import */ var _formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @formatjs/icu-messageformat-parser */ \"(app-pages-browser)/./node_modules/@formatjs/icu-messageformat-parser/lib/index.js\");\n/* harmony import */ var _error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error */ \"(app-pages-browser)/./node_modules/intl-messageformat/lib/src/error.js\");\n\n\nvar PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nfunction isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nfunction formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isLiteralElement)(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPoundElement)(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new _error__WEBPACK_IMPORTED_MODULE_1__.MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isArgumentElement)(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTimeElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isDateTimeSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberElement)(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : (0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isNumberSkeleton)(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isTagElement)(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isSelectElement)(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if ((0,_formatjs_icu_messageformat_parser__WEBPACK_IMPORTED_MODULE_0__.isPluralElement)(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new _error__WEBPACK_IMPORTED_MODULE_1__.FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", _error__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new _error__WEBPACK_IMPORTED_MODULE_1__.InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/intl-messageformat/lib/src/formatters.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NextIntlClientProvider)\n/* harmony export */ });\n/* harmony import */ var use_intl_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-intl/react */ \"(app-pages-browser)/./node_modules/use-intl/dist/esm/development/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction NextIntlClientProvider(param) {\n    let { locale, ...rest } = param;\n    if (!locale) {\n        throw new Error(\"Couldn't infer the `locale` prop in `NextIntlClientProvider`, please provide it explicitly.\\n\\nSee https://next-intl.dev/docs/configuration#locale\");\n    }\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(use_intl_react__WEBPACK_IMPORTED_MODULE_1__.IntlProvider, {\n        locale: locale,\n        ...rest\n    });\n}\n_c = NextIntlClientProvider;\n\nvar _c;\n$RefreshReg$(_c, \"NextIntlClientProvider\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OzZEQUM4QztBQUNOO0FBRXhDLFNBQVNFLHVCQUF1QixLQUcvQjtRQUgrQixFQUM5QkMsTUFBTSxFQUNOLEdBQUdDLE1BQ0osR0FIK0I7SUFJOUIsSUFBSSxDQUFDRCxRQUFRO1FBQ1gsTUFBTSxJQUFJRSxNQUFNO0lBQ2xCO0lBQ0EsT0FBTyxXQUFXLEdBQUVKLHNEQUFHQSxDQUFDRCx3REFBWUEsRUFBRTtRQUNwQ0csUUFBUUE7UUFDUixHQUFHQyxJQUFJO0lBQ1Q7QUFDRjtLQVhTRjtBQWFvQyIsInNvdXJjZXMiOlsiL1VzZXJzL3dvb2Qvd29ya3NwYWNlL2FpdG9vbHMvYWl0b29scy13ZWJzaXRlL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgeyBJbnRsUHJvdmlkZXIgfSBmcm9tICd1c2UtaW50bC9yZWFjdCc7XG5pbXBvcnQgeyBqc3ggfSBmcm9tICdyZWFjdC9qc3gtcnVudGltZSc7XG5cbmZ1bmN0aW9uIE5leHRJbnRsQ2xpZW50UHJvdmlkZXIoe1xuICBsb2NhbGUsXG4gIC4uLnJlc3Rcbn0pIHtcbiAgaWYgKCFsb2NhbGUpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJDb3VsZG4ndCBpbmZlciB0aGUgYGxvY2FsZWAgcHJvcCBpbiBgTmV4dEludGxDbGllbnRQcm92aWRlcmAsIHBsZWFzZSBwcm92aWRlIGl0IGV4cGxpY2l0bHkuXFxuXFxuU2VlIGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL2NvbmZpZ3VyYXRpb24jbG9jYWxlXCIgKTtcbiAgfVxuICByZXR1cm4gLyojX19QVVJFX18qL2pzeChJbnRsUHJvdmlkZXIsIHtcbiAgICBsb2NhbGU6IGxvY2FsZSxcbiAgICAuLi5yZXN0XG4gIH0pO1xufVxuXG5leHBvcnQgeyBOZXh0SW50bENsaWVudFByb3ZpZGVyIGFzIGRlZmF1bHQgfTtcbiJdLCJuYW1lcyI6WyJJbnRsUHJvdmlkZXIiLCJqc3giLCJOZXh0SW50bENsaWVudFByb3ZpZGVyIiwibG9jYWxlIiwicmVzdCIsIkVycm9yIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fdevelopment%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fdevelopment%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js */ \"(app-pages-browser)/./node_modules/next-intl/dist/esm/development/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZ3b29kJTJGd29ya3NwYWNlJTJGYWl0b29scyUyRmFpdG9vbHMtd2Vic2l0ZSUyRm5vZGVfbW9kdWxlcyUyRm5leHQtaW50bCUyRmRpc3QlMkZlc20lMkZkZXZlbG9wbWVudCUyRnNoYXJlZCUyRk5leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsc1JBQTRMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiL1VzZXJzL3dvb2Qvd29ya3NwYWNlL2FpdG9vbHMvYWl0b29scy13ZWJzaXRlL25vZGVfbW9kdWxlcy9uZXh0LWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvc2hhcmVkL05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fdevelopment%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs":
/*!******************************************!*\
  !*** ./node_modules/tslib/tslib.es6.mjs ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __addDisposableResource: () => (/* binding */ __addDisposableResource),\n/* harmony export */   __assign: () => (/* binding */ __assign),\n/* harmony export */   __asyncDelegator: () => (/* binding */ __asyncDelegator),\n/* harmony export */   __asyncGenerator: () => (/* binding */ __asyncGenerator),\n/* harmony export */   __asyncValues: () => (/* binding */ __asyncValues),\n/* harmony export */   __await: () => (/* binding */ __await),\n/* harmony export */   __awaiter: () => (/* binding */ __awaiter),\n/* harmony export */   __classPrivateFieldGet: () => (/* binding */ __classPrivateFieldGet),\n/* harmony export */   __classPrivateFieldIn: () => (/* binding */ __classPrivateFieldIn),\n/* harmony export */   __classPrivateFieldSet: () => (/* binding */ __classPrivateFieldSet),\n/* harmony export */   __createBinding: () => (/* binding */ __createBinding),\n/* harmony export */   __decorate: () => (/* binding */ __decorate),\n/* harmony export */   __disposeResources: () => (/* binding */ __disposeResources),\n/* harmony export */   __esDecorate: () => (/* binding */ __esDecorate),\n/* harmony export */   __exportStar: () => (/* binding */ __exportStar),\n/* harmony export */   __extends: () => (/* binding */ __extends),\n/* harmony export */   __generator: () => (/* binding */ __generator),\n/* harmony export */   __importDefault: () => (/* binding */ __importDefault),\n/* harmony export */   __importStar: () => (/* binding */ __importStar),\n/* harmony export */   __makeTemplateObject: () => (/* binding */ __makeTemplateObject),\n/* harmony export */   __metadata: () => (/* binding */ __metadata),\n/* harmony export */   __param: () => (/* binding */ __param),\n/* harmony export */   __propKey: () => (/* binding */ __propKey),\n/* harmony export */   __read: () => (/* binding */ __read),\n/* harmony export */   __rest: () => (/* binding */ __rest),\n/* harmony export */   __rewriteRelativeImportExtension: () => (/* binding */ __rewriteRelativeImportExtension),\n/* harmony export */   __runInitializers: () => (/* binding */ __runInitializers),\n/* harmony export */   __setFunctionName: () => (/* binding */ __setFunctionName),\n/* harmony export */   __spread: () => (/* binding */ __spread),\n/* harmony export */   __spreadArray: () => (/* binding */ __spreadArray),\n/* harmony export */   __spreadArrays: () => (/* binding */ __spreadArrays),\n/* harmony export */   __values: () => (/* binding */ __values),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nvar __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nfunction __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nfunction __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nfunction __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nfunction __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nfunction __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nfunction __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nfunction __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nfunction __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nfunction __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nvar __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nfunction __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nfunction __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nfunction __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nfunction __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nfunction __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nfunction __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nfunction __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nfunction __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nfunction __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nfunction __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nfunction __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nfunction __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nfunction __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nfunction __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nfunction __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I: () => (/* binding */ IntlError),\n/* harmony export */   a: () => (/* binding */ IntlErrorCode),\n/* harmony export */   b: () => (/* binding */ createIntlFormatters),\n/* harmony export */   c: () => (/* binding */ createFormatter),\n/* harmony export */   d: () => (/* binding */ createCache),\n/* harmony export */   e: () => (/* binding */ createBaseTranslator),\n/* harmony export */   f: () => (/* binding */ defaultGetMessageFallback),\n/* harmony export */   g: () => (/* binding */ defaultOnError),\n/* harmony export */   i: () => (/* binding */ initializeConfig),\n/* harmony export */   r: () => (/* binding */ resolveNamespace)\n/* harmony export */ });\n/* harmony import */ var intl_messageformat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! intl-messageformat */ \"(app-pages-browser)/./node_modules/intl-messageformat/lib/src/core.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @formatjs/fast-memoize */ \"(app-pages-browser)/./node_modules/@formatjs/fast-memoize/lib/index.js\");\n\n\n\n\nclass IntlError extends Error {\n  constructor(code, originalMessage) {\n    let message = code;\n    if (originalMessage) {\n      message += ': ' + originalMessage;\n    }\n    super(message);\n    this.code = code;\n    if (originalMessage) {\n      this.originalMessage = originalMessage;\n    }\n  }\n}\n\nvar IntlErrorCode = /*#__PURE__*/function (IntlErrorCode) {\n  IntlErrorCode[\"MISSING_MESSAGE\"] = \"MISSING_MESSAGE\";\n  IntlErrorCode[\"MISSING_FORMAT\"] = \"MISSING_FORMAT\";\n  IntlErrorCode[\"ENVIRONMENT_FALLBACK\"] = \"ENVIRONMENT_FALLBACK\";\n  IntlErrorCode[\"INSUFFICIENT_PATH\"] = \"INSUFFICIENT_PATH\";\n  IntlErrorCode[\"INVALID_MESSAGE\"] = \"INVALID_MESSAGE\";\n  IntlErrorCode[\"INVALID_KEY\"] = \"INVALID_KEY\";\n  IntlErrorCode[\"FORMATTING_ERROR\"] = \"FORMATTING_ERROR\";\n  return IntlErrorCode;\n}(IntlErrorCode || {});\n\n/**\n * `intl-messageformat` uses separate keys for `date` and `time`, but there's\n * only one native API: `Intl.DateTimeFormat`. Additionally you might want to\n * include both a time and a date in a value, therefore the separation doesn't\n * seem so useful. We offer a single `dateTime` namespace instead, but we have\n * to convert the format before `intl-messageformat` can be used.\n */\nfunction convertFormatsToIntlMessageFormat(globalFormats, inlineFormats, timeZone) {\n  const mfDateDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.date;\n  const mfTimeDefaults = intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat.formats.time;\n  const dateTimeFormats = {\n    ...globalFormats?.dateTime,\n    ...inlineFormats?.dateTime\n  };\n  const allFormats = {\n    date: {\n      ...mfDateDefaults,\n      ...dateTimeFormats\n    },\n    time: {\n      ...mfTimeDefaults,\n      ...dateTimeFormats\n    },\n    number: {\n      ...globalFormats?.number,\n      ...inlineFormats?.number\n    }\n    // (list is not supported in ICU messages)\n  };\n  if (timeZone) {\n    // The only way to set a time zone with `intl-messageformat` is to merge it into the formats\n    // https://github.com/formatjs/formatjs/blob/8256c5271505cf2606e48e3c97ecdd16ede4f1b5/packages/intl/src/message.ts#L15\n    ['date', 'time'].forEach(property => {\n      const formats = allFormats[property];\n      for (const [key, value] of Object.entries(formats)) {\n        formats[key] = {\n          timeZone,\n          ...value\n        };\n      }\n    });\n  }\n  return allFormats;\n}\n\nfunction joinPath(...parts) {\n  return parts.filter(Boolean).join('.');\n}\n\n/**\n * Contains defaults that are used for all entry points into the core.\n * See also `InitializedIntlConfiguration`.\n */\n\nfunction defaultGetMessageFallback(props) {\n  return joinPath(props.namespace, props.key);\n}\nfunction defaultOnError(error) {\n  console.error(error);\n}\n\nfunction createCache() {\n  return {\n    dateTime: {},\n    number: {},\n    message: {},\n    relativeTime: {},\n    pluralRules: {},\n    list: {},\n    displayNames: {}\n  };\n}\nfunction createMemoCache(store) {\n  return {\n    create() {\n      return {\n        get(key) {\n          return store[key];\n        },\n        set(key, value) {\n          store[key] = value;\n        }\n      };\n    }\n  };\n}\nfunction memoFn(fn, cache) {\n  return (0,_formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.memoize)(fn, {\n    cache: createMemoCache(cache),\n    strategy: _formatjs_fast_memoize__WEBPACK_IMPORTED_MODULE_1__.strategies.variadic\n  });\n}\nfunction memoConstructor(ConstructorFn, cache) {\n  return memoFn((...args) => new ConstructorFn(...args), cache);\n}\nfunction createIntlFormatters(cache) {\n  const getDateTimeFormat = memoConstructor(Intl.DateTimeFormat, cache.dateTime);\n  const getNumberFormat = memoConstructor(Intl.NumberFormat, cache.number);\n  const getPluralRules = memoConstructor(Intl.PluralRules, cache.pluralRules);\n  const getRelativeTimeFormat = memoConstructor(Intl.RelativeTimeFormat, cache.relativeTime);\n  const getListFormat = memoConstructor(Intl.ListFormat, cache.list);\n  const getDisplayNames = memoConstructor(Intl.DisplayNames, cache.displayNames);\n  return {\n    getDateTimeFormat,\n    getNumberFormat,\n    getPluralRules,\n    getRelativeTimeFormat,\n    getListFormat,\n    getDisplayNames\n  };\n}\n\n// Placed here for improved tree shaking. Somehow when this is placed in\n// `formatters.tsx`, then it can't be shaken off from `next-intl`.\nfunction createMessageFormatter(cache, intlFormatters) {\n  const getMessageFormat = memoFn((...args) => new intl_messageformat__WEBPACK_IMPORTED_MODULE_2__.IntlMessageFormat(args[0], args[1], args[2], {\n    formatters: intlFormatters,\n    ...args[3]\n  }), cache.message);\n  return getMessageFormat;\n}\nfunction resolvePath(locale, messages, key, namespace) {\n  const fullKey = joinPath(namespace, key);\n  if (!messages) {\n    throw new Error(`No messages available at \\`${namespace}\\`.` );\n  }\n  let message = messages;\n  key.split('.').forEach(part => {\n    const next = message[part];\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (part == null || next == null) {\n      throw new Error(`Could not resolve \\`${fullKey}\\` in messages for locale \\`${locale}\\`.` );\n    }\n    message = next;\n  });\n  return message;\n}\nfunction prepareTranslationValues(values) {\n  // Workaround for https://github.com/formatjs/formatjs/issues/1467\n  const transformedValues = {};\n  Object.keys(values).forEach(key => {\n    let index = 0;\n    const value = values[key];\n    let transformed;\n    if (typeof value === 'function') {\n      transformed = chunks => {\n        const result = value(chunks);\n        return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(result) ? /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(result, {\n          key: key + index++\n        }) : result;\n      };\n    } else {\n      transformed = value;\n    }\n    transformedValues[key] = transformed;\n  });\n  return transformedValues;\n}\nfunction getMessagesOrError(locale, messages, namespace, onError = defaultOnError) {\n  try {\n    if (!messages) {\n      throw new Error(`No messages were configured.` );\n    }\n    const retrievedMessages = namespace ? resolvePath(locale, messages, namespace) : messages;\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!retrievedMessages) {\n      throw new Error(`No messages for namespace \\`${namespace}\\` found.` );\n    }\n    return retrievedMessages;\n  } catch (error) {\n    const intlError = new IntlError(IntlErrorCode.MISSING_MESSAGE, error.message);\n    onError(intlError);\n    return intlError;\n  }\n}\nfunction getPlainMessage(candidate, values) {\n  {\n    // Keep fast path in development\n    if (values) return undefined;\n\n    // Despite potentially no values being available, there can still be\n    // placeholders in the message if the user has forgotten to provide\n    // values. In this case we compile the message to receive an error.\n    const unescapedMessage = candidate.replace(/'([{}])/gi, '$1');\n    const hasPlaceholders = /<|{/.test(unescapedMessage);\n    if (!hasPlaceholders) {\n      return unescapedMessage;\n    }\n  }\n}\nfunction createBaseTranslator(config) {\n  const messagesOrError = getMessagesOrError(config.locale, config.messages, config.namespace, config.onError);\n  return createBaseTranslatorImpl({\n    ...config,\n    messagesOrError\n  });\n}\nfunction createBaseTranslatorImpl({\n  cache,\n  formats: globalFormats,\n  formatters,\n  getMessageFallback = defaultGetMessageFallback,\n  locale,\n  messagesOrError,\n  namespace,\n  onError,\n  timeZone\n}) {\n  const hasMessagesError = messagesOrError instanceof IntlError;\n  function getFallbackFromErrorAndNotify(key, code, message) {\n    const error = new IntlError(code, message);\n    onError(error);\n    return getMessageFallback({\n      error,\n      key,\n      namespace\n    });\n  }\n  function translateBaseFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    let message;\n    try {\n      message = resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n    if (typeof message === 'object') {\n      let code, errorMessage;\n      if (Array.isArray(message)) {\n        code = IntlErrorCode.INVALID_MESSAGE;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an array, but only strings are supported. See https://next-intl.dev/docs/usage/messages#arrays-of-messages`;\n        }\n      } else {\n        code = IntlErrorCode.INSUFFICIENT_PATH;\n        {\n          errorMessage = `Message at \\`${joinPath(namespace, key)}\\` resolved to an object, but only strings are supported. Use a \\`.\\` to retrieve nested messages. See https://next-intl.dev/docs/usage/messages#structuring-messages`;\n        }\n      }\n      return getFallbackFromErrorAndNotify(key, code, errorMessage);\n    }\n    let messageFormat;\n\n    // Hot path that avoids creating an `IntlMessageFormat` instance\n    const plainMessage = getPlainMessage(message, values);\n    if (plainMessage) return plainMessage;\n\n    // Lazy init the message formatter for better tree\n    // shaking in case message formatting is not used.\n    if (!formatters.getMessageFormat) {\n      formatters.getMessageFormat = createMessageFormatter(cache, formatters);\n    }\n    try {\n      messageFormat = formatters.getMessageFormat(message, locale, convertFormatsToIntlMessageFormat(globalFormats, formats, timeZone), {\n        formatters: {\n          ...formatters,\n          getDateTimeFormat(locales, options) {\n            // Workaround for https://github.com/formatjs/formatjs/issues/4279\n            return formatters.getDateTimeFormat(locales, {\n              timeZone,\n              ...options\n            });\n          }\n        }\n      });\n    } catch (error) {\n      const thrownError = error;\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, thrownError.message + ('originalMessage' in thrownError ? ` (${thrownError.originalMessage})` : '') );\n    }\n    try {\n      const formattedMessage = messageFormat.format(\n      // @ts-expect-error `intl-messageformat` expects a different format\n      // for rich text elements since a recent minor update. This\n      // needs to be evaluated in detail, possibly also in regards\n      // to be able to format to parts.\n      values ? prepareTranslationValues(values) : values);\n      if (formattedMessage == null) {\n        throw new Error(`Unable to format \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'}` );\n      }\n\n      // Limit the function signature to return strings or React elements\n      return /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(formattedMessage) ||\n      // Arrays of React elements\n      Array.isArray(formattedMessage) || typeof formattedMessage === 'string' ? formattedMessage : String(formattedMessage);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.FORMATTING_ERROR, error.message);\n    }\n  }\n  function translateFn(/** Use a dot to indicate a level of nesting (e.g. `namespace.nestedLabel`). */\n  key, /** Key value pairs for values to interpolate into the message. */\n  values, /** Provide custom formats for numbers, dates and times. */\n  formats) {\n    const result = translateBaseFn(key, values, formats);\n    if (typeof result !== 'string') {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.INVALID_MESSAGE, `The message \\`${key}\\` in ${namespace ? `namespace \\`${namespace}\\`` : 'messages'} didn't resolve to a string. If you want to format rich text, use \\`t.rich\\` instead.` );\n    }\n    return result;\n  }\n  translateFn.rich = translateBaseFn;\n\n  // Augment `translateBaseFn` to return plain strings\n  translateFn.markup = (key, values, formats) => {\n    const result = translateBaseFn(key,\n    // @ts-expect-error -- `MarkupTranslationValues` is practically a sub type\n    // of `RichTranslationValues` but TypeScript isn't smart enough here.\n    values, formats);\n    if (typeof result !== 'string') {\n      const error = new IntlError(IntlErrorCode.FORMATTING_ERROR, \"`t.markup` only accepts functions for formatting that receive and return strings.\\n\\nE.g. t.markup('markup', {b: (chunks) => `<b>${chunks}</b>`})\");\n      onError(error);\n      return getMessageFallback({\n        error,\n        key,\n        namespace\n      });\n    }\n    return result;\n  };\n  translateFn.raw = key => {\n    if (hasMessagesError) {\n      // We have already warned about this during render\n      return getMessageFallback({\n        error: messagesOrError,\n        key,\n        namespace\n      });\n    }\n    const messages = messagesOrError;\n    try {\n      return resolvePath(locale, messages, key, namespace);\n    } catch (error) {\n      return getFallbackFromErrorAndNotify(key, IntlErrorCode.MISSING_MESSAGE, error.message);\n    }\n  };\n  translateFn.has = key => {\n    if (hasMessagesError) {\n      return false;\n    }\n    try {\n      resolvePath(locale, messagesOrError, key, namespace);\n      return true;\n    } catch {\n      return false;\n    }\n  };\n  return translateFn;\n}\n\n/**\n * For the strictly typed messages to work we have to wrap the namespace into\n * a mandatory prefix. See https://stackoverflow.com/a/71529575/343045\n */\nfunction resolveNamespace(namespace, namespacePrefix) {\n  return namespace === namespacePrefix ? undefined : namespace.slice((namespacePrefix + '.').length);\n}\n\nconst SECOND = 1;\nconst MINUTE = SECOND * 60;\nconst HOUR = MINUTE * 60;\nconst DAY = HOUR * 24;\nconst WEEK = DAY * 7;\nconst MONTH = DAY * (365 / 12); // Approximation\nconst QUARTER = MONTH * 3;\nconst YEAR = DAY * 365;\nconst UNIT_SECONDS = {\n  second: SECOND,\n  seconds: SECOND,\n  minute: MINUTE,\n  minutes: MINUTE,\n  hour: HOUR,\n  hours: HOUR,\n  day: DAY,\n  days: DAY,\n  week: WEEK,\n  weeks: WEEK,\n  month: MONTH,\n  months: MONTH,\n  quarter: QUARTER,\n  quarters: QUARTER,\n  year: YEAR,\n  years: YEAR\n};\nfunction resolveRelativeTimeUnit(seconds) {\n  const absValue = Math.abs(seconds);\n  if (absValue < MINUTE) {\n    return 'second';\n  } else if (absValue < HOUR) {\n    return 'minute';\n  } else if (absValue < DAY) {\n    return 'hour';\n  } else if (absValue < WEEK) {\n    return 'day';\n  } else if (absValue < MONTH) {\n    return 'week';\n  } else if (absValue < YEAR) {\n    return 'month';\n  }\n  return 'year';\n}\nfunction calculateRelativeTimeValue(seconds, unit) {\n  // We have to round the resulting values, as `Intl.RelativeTimeFormat`\n  // will include fractions like '2.1 hours ago'.\n  return Math.round(seconds / UNIT_SECONDS[unit]);\n}\nfunction createFormatter(props) {\n  const {\n    _cache: cache = createCache(),\n    _formatters: formatters = createIntlFormatters(cache),\n    formats,\n    locale,\n    onError = defaultOnError,\n    timeZone: globalTimeZone\n  } = props;\n  function applyTimeZone(options) {\n    if (!options?.timeZone) {\n      if (globalTimeZone) {\n        options = {\n          ...options,\n          timeZone: globalTimeZone\n        };\n      } else {\n        onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`timeZone\\` parameter wasn't provided and there is no global default configured. Consider adding a global default to avoid markup mismatches caused by environment differences. Learn more: https://next-intl.dev/docs/configuration#time-zone` ));\n      }\n    }\n    return options;\n  }\n  function resolveFormatOrOptions(typeFormats, formatOrOptions, overrides) {\n    let options;\n    if (typeof formatOrOptions === 'string') {\n      const formatName = formatOrOptions;\n      options = typeFormats?.[formatName];\n      if (!options) {\n        const error = new IntlError(IntlErrorCode.MISSING_FORMAT, `Format \\`${formatName}\\` is not available.` );\n        onError(error);\n        throw error;\n      }\n    } else {\n      options = formatOrOptions;\n    }\n    if (overrides) {\n      options = {\n        ...options,\n        ...overrides\n      };\n    }\n    return options;\n  }\n  function getFormattedValue(formatOrOptions, overrides, typeFormats, formatter, getFallback) {\n    let options;\n    try {\n      options = resolveFormatOrOptions(typeFormats, formatOrOptions, overrides);\n    } catch {\n      return getFallback();\n    }\n    try {\n      return formatter(options);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return getFallback();\n    }\n  }\n  function dateTime(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).format(value);\n    }, () => String(value));\n  }\n  function dateTimeRange(start, end, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.dateTime, options => {\n      options = applyTimeZone(options);\n      return formatters.getDateTimeFormat(locale, options).formatRange(start, end);\n    }, () => [dateTime(start), dateTime(end)].join(' – '));\n  }\n  function number(value, formatOrOptions, overrides) {\n    return getFormattedValue(formatOrOptions, overrides, formats?.number, options => formatters.getNumberFormat(locale, options).format(value), () => String(value));\n  }\n  function getGlobalNow() {\n    // Only read when necessary to avoid triggering a `dynamicIO` error\n    // unnecessarily (`now` is only needed for `format.relativeTime`)\n    if (props.now) {\n      return props.now;\n    } else {\n      onError(new IntlError(IntlErrorCode.ENVIRONMENT_FALLBACK, `The \\`now\\` parameter wasn't provided to \\`relativeTime\\` and there is no global default configured, therefore the current time will be used as a fallback. See https://next-intl.dev/docs/usage/dates-times#relative-times-usenow` ));\n      return new Date();\n    }\n  }\n  function relativeTime(date, nowOrOptions) {\n    try {\n      let nowDate, unit;\n      const opts = {};\n      if (nowOrOptions instanceof Date || typeof nowOrOptions === 'number') {\n        nowDate = new Date(nowOrOptions);\n      } else if (nowOrOptions) {\n        if (nowOrOptions.now != null) {\n          nowDate = new Date(nowOrOptions.now);\n        } else {\n          nowDate = getGlobalNow();\n        }\n        unit = nowOrOptions.unit;\n        opts.style = nowOrOptions.style;\n        // @ts-expect-error -- Types are slightly outdated\n        opts.numberingSystem = nowOrOptions.numberingSystem;\n      }\n      if (!nowDate) {\n        nowDate = getGlobalNow();\n      }\n      const dateDate = new Date(date);\n      const seconds = (dateDate.getTime() - nowDate.getTime()) / 1000;\n      if (!unit) {\n        unit = resolveRelativeTimeUnit(seconds);\n      }\n\n      // `numeric: 'auto'` can theoretically produce output like \"yesterday\",\n      // but it only works with integers. E.g. -1 day will produce \"yesterday\",\n      // but -1.1 days will produce \"-1.1 days\". Rounding before formatting is\n      // not desired, as the given dates might cross a threshold were the\n      // output isn't correct anymore. Example: 2024-01-08T23:00:00.000Z and\n      // 2024-01-08T01:00:00.000Z would produce \"yesterday\", which is not the\n      // case. By using `always` we can ensure correct output. The only exception\n      // is the formatting of times <1 second as \"now\".\n      opts.numeric = unit === 'second' ? 'auto' : 'always';\n      const value = calculateRelativeTimeValue(seconds, unit);\n      return formatters.getRelativeTimeFormat(locale, opts).format(value, unit);\n    } catch (error) {\n      onError(new IntlError(IntlErrorCode.FORMATTING_ERROR, error.message));\n      return String(date);\n    }\n  }\n  function list(value, formatOrOptions, overrides) {\n    const serializedValue = [];\n    const richValues = new Map();\n\n    // `formatToParts` only accepts strings, therefore we have to temporarily\n    // replace React elements with a placeholder ID that can be used to retrieve\n    // the original value afterwards.\n    let index = 0;\n    for (const item of value) {\n      let serializedItem;\n      if (typeof item === 'object') {\n        serializedItem = String(index);\n        richValues.set(serializedItem, item);\n      } else {\n        serializedItem = String(item);\n      }\n      serializedValue.push(serializedItem);\n      index++;\n    }\n    return getFormattedValue(formatOrOptions, overrides, formats?.list,\n    // @ts-expect-error -- `richValues.size` is used to determine the return type, but TypeScript can't infer the meaning of this correctly\n    options => {\n      const result = formatters.getListFormat(locale, options).formatToParts(serializedValue).map(part => part.type === 'literal' ? part.value : richValues.get(part.value) || part.value);\n      if (richValues.size > 0) {\n        return result;\n      } else {\n        return result.join('');\n      }\n    }, () => String(value));\n  }\n  return {\n    dateTime,\n    number,\n    relativeTime,\n    list,\n    dateTimeRange\n  };\n}\n\nfunction validateMessagesSegment(messages, invalidKeyLabels, parentPath) {\n  Object.entries(messages).forEach(([key, messageOrMessages]) => {\n    if (key.includes('.')) {\n      let keyLabel = key;\n      if (parentPath) keyLabel += ` (at ${parentPath})`;\n      invalidKeyLabels.push(keyLabel);\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (messageOrMessages != null && typeof messageOrMessages === 'object') {\n      validateMessagesSegment(messageOrMessages, invalidKeyLabels, joinPath(parentPath, key));\n    }\n  });\n}\nfunction validateMessages(messages, onError) {\n  const invalidKeyLabels = [];\n  validateMessagesSegment(messages, invalidKeyLabels);\n  if (invalidKeyLabels.length > 0) {\n    onError(new IntlError(IntlErrorCode.INVALID_KEY, `Namespace keys can not contain the character \".\" as this is used to express nesting. Please remove it or replace it with another character.\n\nInvalid ${invalidKeyLabels.length === 1 ? 'key' : 'keys'}: ${invalidKeyLabels.join(', ')}\n\nIf you're migrating from a flat structure, you can convert your messages as follows:\n\nimport {set} from \"lodash\";\n\nconst input = {\n  \"one.one\": \"1.1\",\n  \"one.two\": \"1.2\",\n  \"two.one.one\": \"2.1.1\"\n};\n\nconst output = Object.entries(input).reduce(\n  (acc, [key, value]) => set(acc, key, value),\n  {}\n);\n\n// Output:\n//\n// {\n//   \"one\": {\n//     \"one\": \"1.1\",\n//     \"two\": \"1.2\"\n//   },\n//   \"two\": {\n//     \"one\": {\n//       \"one\": \"2.1.1\"\n//     }\n//   }\n// }\n` ));\n  }\n}\n\n/**\n * Enhances the incoming props with defaults.\n */\nfunction initializeConfig({\n  formats,\n  getMessageFallback,\n  messages,\n  onError,\n  ...rest\n}) {\n  const finalOnError = onError || defaultOnError;\n  const finalGetMessageFallback = getMessageFallback || defaultGetMessageFallback;\n  {\n    if (messages) {\n      validateMessages(messages, finalOnError);\n    }\n  }\n  return {\n    ...rest,\n    formats: formats || undefined,\n    messages: messages || undefined,\n    onError: finalOnError,\n    getMessageFallback: finalGetMessageFallback\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy91c2UtaW50bC9kaXN0L2VzbS9kZXZlbG9wbWVudC9pbml0aWFsaXplQ29uZmlnLUNSRDZldXVLLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBdUQ7QUFDRjtBQUNROztBQUU3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLG9CQUFvQjs7QUFFckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixpRUFBaUI7QUFDMUMseUJBQXlCLGlFQUFpQjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEIsY0FBYztBQUNkLGVBQWU7QUFDZixvQkFBb0I7QUFDcEIsbUJBQW1CO0FBQ25CLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsK0RBQU87QUFDaEI7QUFDQSxjQUFjLDhEQUFVO0FBQ3hCLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRCxpRUFBaUI7QUFDcEU7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELFVBQVU7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDZDQUE2QyxRQUFRLDhCQUE4QixPQUFPO0FBQzFGO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHFEQUFjLHdCQUF3QixtREFBWTtBQUM5RTtBQUNBLFNBQVM7QUFDVDtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EscURBQXFELFVBQVU7QUFDL0Q7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EscURBQXFEO0FBQ3JELGdDQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLHlCQUF5QjtBQUNsRTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EseUNBQXlDLHlCQUF5QjtBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOO0FBQ0EsOElBQThJLDRCQUE0QjtBQUMxSztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMsSUFBSSxRQUFRLDJCQUEyQixVQUFVLGlCQUFpQjtBQUNoSDs7QUFFQTtBQUNBLDBCQUEwQixxREFBYztBQUN4QztBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdHQUFnRyxJQUFJLFFBQVEsMkJBQTJCLFVBQVUsa0JBQWtCO0FBQ25LO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUxBQWlMLHFCQUFxQixPQUFPLE1BQU07QUFDbk47QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhFQUE4RSxXQUFXO0FBQ3pGO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDBDQUEwQyxXQUFXO0FBQ3JEO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFVBQVUsK0NBQStDLElBQUk7O0FBRTdEOztBQUVBLFFBQVEsS0FBSzs7QUFFYjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFK08iLCJzb3VyY2VzIjpbIi9Vc2Vycy93b29kL3dvcmtzcGFjZS9haXRvb2xzL2FpdG9vbHMtd2Vic2l0ZS9ub2RlX21vZHVsZXMvdXNlLWludGwvZGlzdC9lc20vZGV2ZWxvcG1lbnQvaW5pdGlhbGl6ZUNvbmZpZy1DUkQ2ZXV1Sy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBJbnRsTWVzc2FnZUZvcm1hdCB9IGZyb20gJ2ludGwtbWVzc2FnZWZvcm1hdCc7XG5pbXBvcnQgeyBpc1ZhbGlkRWxlbWVudCwgY2xvbmVFbGVtZW50IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbWVtb2l6ZSwgc3RyYXRlZ2llcyB9IGZyb20gJ0Bmb3JtYXRqcy9mYXN0LW1lbW9pemUnO1xuXG5jbGFzcyBJbnRsRXJyb3IgZXh0ZW5kcyBFcnJvciB7XG4gIGNvbnN0cnVjdG9yKGNvZGUsIG9yaWdpbmFsTWVzc2FnZSkge1xuICAgIGxldCBtZXNzYWdlID0gY29kZTtcbiAgICBpZiAob3JpZ2luYWxNZXNzYWdlKSB7XG4gICAgICBtZXNzYWdlICs9ICc6ICcgKyBvcmlnaW5hbE1lc3NhZ2U7XG4gICAgfVxuICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgIHRoaXMuY29kZSA9IGNvZGU7XG4gICAgaWYgKG9yaWdpbmFsTWVzc2FnZSkge1xuICAgICAgdGhpcy5vcmlnaW5hbE1lc3NhZ2UgPSBvcmlnaW5hbE1lc3NhZ2U7XG4gICAgfVxuICB9XG59XG5cbnZhciBJbnRsRXJyb3JDb2RlID0gLyojX19QVVJFX18qL2Z1bmN0aW9uIChJbnRsRXJyb3JDb2RlKSB7XG4gIEludGxFcnJvckNvZGVbXCJNSVNTSU5HX01FU1NBR0VcIl0gPSBcIk1JU1NJTkdfTUVTU0FHRVwiO1xuICBJbnRsRXJyb3JDb2RlW1wiTUlTU0lOR19GT1JNQVRcIl0gPSBcIk1JU1NJTkdfRk9STUFUXCI7XG4gIEludGxFcnJvckNvZGVbXCJFTlZJUk9OTUVOVF9GQUxMQkFDS1wiXSA9IFwiRU5WSVJPTk1FTlRfRkFMTEJBQ0tcIjtcbiAgSW50bEVycm9yQ29kZVtcIklOU1VGRklDSUVOVF9QQVRIXCJdID0gXCJJTlNVRkZJQ0lFTlRfUEFUSFwiO1xuICBJbnRsRXJyb3JDb2RlW1wiSU5WQUxJRF9NRVNTQUdFXCJdID0gXCJJTlZBTElEX01FU1NBR0VcIjtcbiAgSW50bEVycm9yQ29kZVtcIklOVkFMSURfS0VZXCJdID0gXCJJTlZBTElEX0tFWVwiO1xuICBJbnRsRXJyb3JDb2RlW1wiRk9STUFUVElOR19FUlJPUlwiXSA9IFwiRk9STUFUVElOR19FUlJPUlwiO1xuICByZXR1cm4gSW50bEVycm9yQ29kZTtcbn0oSW50bEVycm9yQ29kZSB8fCB7fSk7XG5cbi8qKlxuICogYGludGwtbWVzc2FnZWZvcm1hdGAgdXNlcyBzZXBhcmF0ZSBrZXlzIGZvciBgZGF0ZWAgYW5kIGB0aW1lYCwgYnV0IHRoZXJlJ3NcbiAqIG9ubHkgb25lIG5hdGl2ZSBBUEk6IGBJbnRsLkRhdGVUaW1lRm9ybWF0YC4gQWRkaXRpb25hbGx5IHlvdSBtaWdodCB3YW50IHRvXG4gKiBpbmNsdWRlIGJvdGggYSB0aW1lIGFuZCBhIGRhdGUgaW4gYSB2YWx1ZSwgdGhlcmVmb3JlIHRoZSBzZXBhcmF0aW9uIGRvZXNuJ3RcbiAqIHNlZW0gc28gdXNlZnVsLiBXZSBvZmZlciBhIHNpbmdsZSBgZGF0ZVRpbWVgIG5hbWVzcGFjZSBpbnN0ZWFkLCBidXQgd2UgaGF2ZVxuICogdG8gY29udmVydCB0aGUgZm9ybWF0IGJlZm9yZSBgaW50bC1tZXNzYWdlZm9ybWF0YCBjYW4gYmUgdXNlZC5cbiAqL1xuZnVuY3Rpb24gY29udmVydEZvcm1hdHNUb0ludGxNZXNzYWdlRm9ybWF0KGdsb2JhbEZvcm1hdHMsIGlubGluZUZvcm1hdHMsIHRpbWVab25lKSB7XG4gIGNvbnN0IG1mRGF0ZURlZmF1bHRzID0gSW50bE1lc3NhZ2VGb3JtYXQuZm9ybWF0cy5kYXRlO1xuICBjb25zdCBtZlRpbWVEZWZhdWx0cyA9IEludGxNZXNzYWdlRm9ybWF0LmZvcm1hdHMudGltZTtcbiAgY29uc3QgZGF0ZVRpbWVGb3JtYXRzID0ge1xuICAgIC4uLmdsb2JhbEZvcm1hdHM/LmRhdGVUaW1lLFxuICAgIC4uLmlubGluZUZvcm1hdHM/LmRhdGVUaW1lXG4gIH07XG4gIGNvbnN0IGFsbEZvcm1hdHMgPSB7XG4gICAgZGF0ZToge1xuICAgICAgLi4ubWZEYXRlRGVmYXVsdHMsXG4gICAgICAuLi5kYXRlVGltZUZvcm1hdHNcbiAgICB9LFxuICAgIHRpbWU6IHtcbiAgICAgIC4uLm1mVGltZURlZmF1bHRzLFxuICAgICAgLi4uZGF0ZVRpbWVGb3JtYXRzXG4gICAgfSxcbiAgICBudW1iZXI6IHtcbiAgICAgIC4uLmdsb2JhbEZvcm1hdHM/Lm51bWJlcixcbiAgICAgIC4uLmlubGluZUZvcm1hdHM/Lm51bWJlclxuICAgIH1cbiAgICAvLyAobGlzdCBpcyBub3Qgc3VwcG9ydGVkIGluIElDVSBtZXNzYWdlcylcbiAgfTtcbiAgaWYgKHRpbWVab25lKSB7XG4gICAgLy8gVGhlIG9ubHkgd2F5IHRvIHNldCBhIHRpbWUgem9uZSB3aXRoIGBpbnRsLW1lc3NhZ2Vmb3JtYXRgIGlzIHRvIG1lcmdlIGl0IGludG8gdGhlIGZvcm1hdHNcbiAgICAvLyBodHRwczovL2dpdGh1Yi5jb20vZm9ybWF0anMvZm9ybWF0anMvYmxvYi84MjU2YzUyNzE1MDVjZjI2MDZlNDhlM2M5N2VjZGQxNmVkZTRmMWI1L3BhY2thZ2VzL2ludGwvc3JjL21lc3NhZ2UudHMjTDE1XG4gICAgWydkYXRlJywgJ3RpbWUnXS5mb3JFYWNoKHByb3BlcnR5ID0+IHtcbiAgICAgIGNvbnN0IGZvcm1hdHMgPSBhbGxGb3JtYXRzW3Byb3BlcnR5XTtcbiAgICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKGZvcm1hdHMpKSB7XG4gICAgICAgIGZvcm1hdHNba2V5XSA9IHtcbiAgICAgICAgICB0aW1lWm9uZSxcbiAgICAgICAgICAuLi52YWx1ZVxuICAgICAgICB9O1xuICAgICAgfVxuICAgIH0pO1xuICB9XG4gIHJldHVybiBhbGxGb3JtYXRzO1xufVxuXG5mdW5jdGlvbiBqb2luUGF0aCguLi5wYXJ0cykge1xuICByZXR1cm4gcGFydHMuZmlsdGVyKEJvb2xlYW4pLmpvaW4oJy4nKTtcbn1cblxuLyoqXG4gKiBDb250YWlucyBkZWZhdWx0cyB0aGF0IGFyZSB1c2VkIGZvciBhbGwgZW50cnkgcG9pbnRzIGludG8gdGhlIGNvcmUuXG4gKiBTZWUgYWxzbyBgSW5pdGlhbGl6ZWRJbnRsQ29uZmlndXJhdGlvbmAuXG4gKi9cblxuZnVuY3Rpb24gZGVmYXVsdEdldE1lc3NhZ2VGYWxsYmFjayhwcm9wcykge1xuICByZXR1cm4gam9pblBhdGgocHJvcHMubmFtZXNwYWNlLCBwcm9wcy5rZXkpO1xufVxuZnVuY3Rpb24gZGVmYXVsdE9uRXJyb3IoZXJyb3IpIHtcbiAgY29uc29sZS5lcnJvcihlcnJvcik7XG59XG5cbmZ1bmN0aW9uIGNyZWF0ZUNhY2hlKCkge1xuICByZXR1cm4ge1xuICAgIGRhdGVUaW1lOiB7fSxcbiAgICBudW1iZXI6IHt9LFxuICAgIG1lc3NhZ2U6IHt9LFxuICAgIHJlbGF0aXZlVGltZToge30sXG4gICAgcGx1cmFsUnVsZXM6IHt9LFxuICAgIGxpc3Q6IHt9LFxuICAgIGRpc3BsYXlOYW1lczoge31cbiAgfTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZU1lbW9DYWNoZShzdG9yZSkge1xuICByZXR1cm4ge1xuICAgIGNyZWF0ZSgpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGdldChrZXkpIHtcbiAgICAgICAgICByZXR1cm4gc3RvcmVba2V5XTtcbiAgICAgICAgfSxcbiAgICAgICAgc2V0KGtleSwgdmFsdWUpIHtcbiAgICAgICAgICBzdG9yZVtrZXldID0gdmFsdWU7XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfVxuICB9O1xufVxuZnVuY3Rpb24gbWVtb0ZuKGZuLCBjYWNoZSkge1xuICByZXR1cm4gbWVtb2l6ZShmbiwge1xuICAgIGNhY2hlOiBjcmVhdGVNZW1vQ2FjaGUoY2FjaGUpLFxuICAgIHN0cmF0ZWd5OiBzdHJhdGVnaWVzLnZhcmlhZGljXG4gIH0pO1xufVxuZnVuY3Rpb24gbWVtb0NvbnN0cnVjdG9yKENvbnN0cnVjdG9yRm4sIGNhY2hlKSB7XG4gIHJldHVybiBtZW1vRm4oKC4uLmFyZ3MpID0+IG5ldyBDb25zdHJ1Y3RvckZuKC4uLmFyZ3MpLCBjYWNoZSk7XG59XG5mdW5jdGlvbiBjcmVhdGVJbnRsRm9ybWF0dGVycyhjYWNoZSkge1xuICBjb25zdCBnZXREYXRlVGltZUZvcm1hdCA9IG1lbW9Db25zdHJ1Y3RvcihJbnRsLkRhdGVUaW1lRm9ybWF0LCBjYWNoZS5kYXRlVGltZSk7XG4gIGNvbnN0IGdldE51bWJlckZvcm1hdCA9IG1lbW9Db25zdHJ1Y3RvcihJbnRsLk51bWJlckZvcm1hdCwgY2FjaGUubnVtYmVyKTtcbiAgY29uc3QgZ2V0UGx1cmFsUnVsZXMgPSBtZW1vQ29uc3RydWN0b3IoSW50bC5QbHVyYWxSdWxlcywgY2FjaGUucGx1cmFsUnVsZXMpO1xuICBjb25zdCBnZXRSZWxhdGl2ZVRpbWVGb3JtYXQgPSBtZW1vQ29uc3RydWN0b3IoSW50bC5SZWxhdGl2ZVRpbWVGb3JtYXQsIGNhY2hlLnJlbGF0aXZlVGltZSk7XG4gIGNvbnN0IGdldExpc3RGb3JtYXQgPSBtZW1vQ29uc3RydWN0b3IoSW50bC5MaXN0Rm9ybWF0LCBjYWNoZS5saXN0KTtcbiAgY29uc3QgZ2V0RGlzcGxheU5hbWVzID0gbWVtb0NvbnN0cnVjdG9yKEludGwuRGlzcGxheU5hbWVzLCBjYWNoZS5kaXNwbGF5TmFtZXMpO1xuICByZXR1cm4ge1xuICAgIGdldERhdGVUaW1lRm9ybWF0LFxuICAgIGdldE51bWJlckZvcm1hdCxcbiAgICBnZXRQbHVyYWxSdWxlcyxcbiAgICBnZXRSZWxhdGl2ZVRpbWVGb3JtYXQsXG4gICAgZ2V0TGlzdEZvcm1hdCxcbiAgICBnZXREaXNwbGF5TmFtZXNcbiAgfTtcbn1cblxuLy8gUGxhY2VkIGhlcmUgZm9yIGltcHJvdmVkIHRyZWUgc2hha2luZy4gU29tZWhvdyB3aGVuIHRoaXMgaXMgcGxhY2VkIGluXG4vLyBgZm9ybWF0dGVycy50c3hgLCB0aGVuIGl0IGNhbid0IGJlIHNoYWtlbiBvZmYgZnJvbSBgbmV4dC1pbnRsYC5cbmZ1bmN0aW9uIGNyZWF0ZU1lc3NhZ2VGb3JtYXR0ZXIoY2FjaGUsIGludGxGb3JtYXR0ZXJzKSB7XG4gIGNvbnN0IGdldE1lc3NhZ2VGb3JtYXQgPSBtZW1vRm4oKC4uLmFyZ3MpID0+IG5ldyBJbnRsTWVzc2FnZUZvcm1hdChhcmdzWzBdLCBhcmdzWzFdLCBhcmdzWzJdLCB7XG4gICAgZm9ybWF0dGVyczogaW50bEZvcm1hdHRlcnMsXG4gICAgLi4uYXJnc1szXVxuICB9KSwgY2FjaGUubWVzc2FnZSk7XG4gIHJldHVybiBnZXRNZXNzYWdlRm9ybWF0O1xufVxuZnVuY3Rpb24gcmVzb2x2ZVBhdGgobG9jYWxlLCBtZXNzYWdlcywga2V5LCBuYW1lc3BhY2UpIHtcbiAgY29uc3QgZnVsbEtleSA9IGpvaW5QYXRoKG5hbWVzcGFjZSwga2V5KTtcbiAgaWYgKCFtZXNzYWdlcykge1xuICAgIHRocm93IG5ldyBFcnJvcihgTm8gbWVzc2FnZXMgYXZhaWxhYmxlIGF0IFxcYCR7bmFtZXNwYWNlfVxcYC5gICk7XG4gIH1cbiAgbGV0IG1lc3NhZ2UgPSBtZXNzYWdlcztcbiAga2V5LnNwbGl0KCcuJykuZm9yRWFjaChwYXJ0ID0+IHtcbiAgICBjb25zdCBuZXh0ID0gbWVzc2FnZVtwYXJ0XTtcblxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW5uZWNlc3NhcnktY29uZGl0aW9uXG4gICAgaWYgKHBhcnQgPT0gbnVsbCB8fCBuZXh0ID09IG51bGwpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgQ291bGQgbm90IHJlc29sdmUgXFxgJHtmdWxsS2V5fVxcYCBpbiBtZXNzYWdlcyBmb3IgbG9jYWxlIFxcYCR7bG9jYWxlfVxcYC5gICk7XG4gICAgfVxuICAgIG1lc3NhZ2UgPSBuZXh0O1xuICB9KTtcbiAgcmV0dXJuIG1lc3NhZ2U7XG59XG5mdW5jdGlvbiBwcmVwYXJlVHJhbnNsYXRpb25WYWx1ZXModmFsdWVzKSB7XG4gIC8vIFdvcmthcm91bmQgZm9yIGh0dHBzOi8vZ2l0aHViLmNvbS9mb3JtYXRqcy9mb3JtYXRqcy9pc3N1ZXMvMTQ2N1xuICBjb25zdCB0cmFuc2Zvcm1lZFZhbHVlcyA9IHt9O1xuICBPYmplY3Qua2V5cyh2YWx1ZXMpLmZvckVhY2goa2V5ID0+IHtcbiAgICBsZXQgaW5kZXggPSAwO1xuICAgIGNvbnN0IHZhbHVlID0gdmFsdWVzW2tleV07XG4gICAgbGV0IHRyYW5zZm9ybWVkO1xuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdmdW5jdGlvbicpIHtcbiAgICAgIHRyYW5zZm9ybWVkID0gY2h1bmtzID0+IHtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gdmFsdWUoY2h1bmtzKTtcbiAgICAgICAgcmV0dXJuIC8qI19fUFVSRV9fKi9pc1ZhbGlkRWxlbWVudChyZXN1bHQpID8gLyojX19QVVJFX18qL2Nsb25lRWxlbWVudChyZXN1bHQsIHtcbiAgICAgICAgICBrZXk6IGtleSArIGluZGV4KytcbiAgICAgICAgfSkgOiByZXN1bHQ7XG4gICAgICB9O1xuICAgIH0gZWxzZSB7XG4gICAgICB0cmFuc2Zvcm1lZCA9IHZhbHVlO1xuICAgIH1cbiAgICB0cmFuc2Zvcm1lZFZhbHVlc1trZXldID0gdHJhbnNmb3JtZWQ7XG4gIH0pO1xuICByZXR1cm4gdHJhbnNmb3JtZWRWYWx1ZXM7XG59XG5mdW5jdGlvbiBnZXRNZXNzYWdlc09yRXJyb3IobG9jYWxlLCBtZXNzYWdlcywgbmFtZXNwYWNlLCBvbkVycm9yID0gZGVmYXVsdE9uRXJyb3IpIHtcbiAgdHJ5IHtcbiAgICBpZiAoIW1lc3NhZ2VzKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYE5vIG1lc3NhZ2VzIHdlcmUgY29uZmlndXJlZC5gICk7XG4gICAgfVxuICAgIGNvbnN0IHJldHJpZXZlZE1lc3NhZ2VzID0gbmFtZXNwYWNlID8gcmVzb2x2ZVBhdGgobG9jYWxlLCBtZXNzYWdlcywgbmFtZXNwYWNlKSA6IG1lc3NhZ2VzO1xuXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11bm5lY2Vzc2FyeS1jb25kaXRpb25cbiAgICBpZiAoIXJldHJpZXZlZE1lc3NhZ2VzKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYE5vIG1lc3NhZ2VzIGZvciBuYW1lc3BhY2UgXFxgJHtuYW1lc3BhY2V9XFxgIGZvdW5kLmAgKTtcbiAgICB9XG4gICAgcmV0dXJuIHJldHJpZXZlZE1lc3NhZ2VzO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnN0IGludGxFcnJvciA9IG5ldyBJbnRsRXJyb3IoSW50bEVycm9yQ29kZS5NSVNTSU5HX01FU1NBR0UsIGVycm9yLm1lc3NhZ2UpO1xuICAgIG9uRXJyb3IoaW50bEVycm9yKTtcbiAgICByZXR1cm4gaW50bEVycm9yO1xuICB9XG59XG5mdW5jdGlvbiBnZXRQbGFpbk1lc3NhZ2UoY2FuZGlkYXRlLCB2YWx1ZXMpIHtcbiAge1xuICAgIC8vIEtlZXAgZmFzdCBwYXRoIGluIGRldmVsb3BtZW50XG4gICAgaWYgKHZhbHVlcykgcmV0dXJuIHVuZGVmaW5lZDtcblxuICAgIC8vIERlc3BpdGUgcG90ZW50aWFsbHkgbm8gdmFsdWVzIGJlaW5nIGF2YWlsYWJsZSwgdGhlcmUgY2FuIHN0aWxsIGJlXG4gICAgLy8gcGxhY2Vob2xkZXJzIGluIHRoZSBtZXNzYWdlIGlmIHRoZSB1c2VyIGhhcyBmb3Jnb3R0ZW4gdG8gcHJvdmlkZVxuICAgIC8vIHZhbHVlcy4gSW4gdGhpcyBjYXNlIHdlIGNvbXBpbGUgdGhlIG1lc3NhZ2UgdG8gcmVjZWl2ZSBhbiBlcnJvci5cbiAgICBjb25zdCB1bmVzY2FwZWRNZXNzYWdlID0gY2FuZGlkYXRlLnJlcGxhY2UoLycoW3t9XSkvZ2ksICckMScpO1xuICAgIGNvbnN0IGhhc1BsYWNlaG9sZGVycyA9IC88fHsvLnRlc3QodW5lc2NhcGVkTWVzc2FnZSk7XG4gICAgaWYgKCFoYXNQbGFjZWhvbGRlcnMpIHtcbiAgICAgIHJldHVybiB1bmVzY2FwZWRNZXNzYWdlO1xuICAgIH1cbiAgfVxufVxuZnVuY3Rpb24gY3JlYXRlQmFzZVRyYW5zbGF0b3IoY29uZmlnKSB7XG4gIGNvbnN0IG1lc3NhZ2VzT3JFcnJvciA9IGdldE1lc3NhZ2VzT3JFcnJvcihjb25maWcubG9jYWxlLCBjb25maWcubWVzc2FnZXMsIGNvbmZpZy5uYW1lc3BhY2UsIGNvbmZpZy5vbkVycm9yKTtcbiAgcmV0dXJuIGNyZWF0ZUJhc2VUcmFuc2xhdG9ySW1wbCh7XG4gICAgLi4uY29uZmlnLFxuICAgIG1lc3NhZ2VzT3JFcnJvclxuICB9KTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZUJhc2VUcmFuc2xhdG9ySW1wbCh7XG4gIGNhY2hlLFxuICBmb3JtYXRzOiBnbG9iYWxGb3JtYXRzLFxuICBmb3JtYXR0ZXJzLFxuICBnZXRNZXNzYWdlRmFsbGJhY2sgPSBkZWZhdWx0R2V0TWVzc2FnZUZhbGxiYWNrLFxuICBsb2NhbGUsXG4gIG1lc3NhZ2VzT3JFcnJvcixcbiAgbmFtZXNwYWNlLFxuICBvbkVycm9yLFxuICB0aW1lWm9uZVxufSkge1xuICBjb25zdCBoYXNNZXNzYWdlc0Vycm9yID0gbWVzc2FnZXNPckVycm9yIGluc3RhbmNlb2YgSW50bEVycm9yO1xuICBmdW5jdGlvbiBnZXRGYWxsYmFja0Zyb21FcnJvckFuZE5vdGlmeShrZXksIGNvZGUsIG1lc3NhZ2UpIHtcbiAgICBjb25zdCBlcnJvciA9IG5ldyBJbnRsRXJyb3IoY29kZSwgbWVzc2FnZSk7XG4gICAgb25FcnJvcihlcnJvcik7XG4gICAgcmV0dXJuIGdldE1lc3NhZ2VGYWxsYmFjayh7XG4gICAgICBlcnJvcixcbiAgICAgIGtleSxcbiAgICAgIG5hbWVzcGFjZVxuICAgIH0pO1xuICB9XG4gIGZ1bmN0aW9uIHRyYW5zbGF0ZUJhc2VGbigvKiogVXNlIGEgZG90IHRvIGluZGljYXRlIGEgbGV2ZWwgb2YgbmVzdGluZyAoZS5nLiBgbmFtZXNwYWNlLm5lc3RlZExhYmVsYCkuICovXG4gIGtleSwgLyoqIEtleSB2YWx1ZSBwYWlycyBmb3IgdmFsdWVzIHRvIGludGVycG9sYXRlIGludG8gdGhlIG1lc3NhZ2UuICovXG4gIHZhbHVlcywgLyoqIFByb3ZpZGUgY3VzdG9tIGZvcm1hdHMgZm9yIG51bWJlcnMsIGRhdGVzIGFuZCB0aW1lcy4gKi9cbiAgZm9ybWF0cykge1xuICAgIGlmIChoYXNNZXNzYWdlc0Vycm9yKSB7XG4gICAgICAvLyBXZSBoYXZlIGFscmVhZHkgd2FybmVkIGFib3V0IHRoaXMgZHVyaW5nIHJlbmRlclxuICAgICAgcmV0dXJuIGdldE1lc3NhZ2VGYWxsYmFjayh7XG4gICAgICAgIGVycm9yOiBtZXNzYWdlc09yRXJyb3IsXG4gICAgICAgIGtleSxcbiAgICAgICAgbmFtZXNwYWNlXG4gICAgICB9KTtcbiAgICB9XG4gICAgY29uc3QgbWVzc2FnZXMgPSBtZXNzYWdlc09yRXJyb3I7XG4gICAgbGV0IG1lc3NhZ2U7XG4gICAgdHJ5IHtcbiAgICAgIG1lc3NhZ2UgPSByZXNvbHZlUGF0aChsb2NhbGUsIG1lc3NhZ2VzLCBrZXksIG5hbWVzcGFjZSk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiBnZXRGYWxsYmFja0Zyb21FcnJvckFuZE5vdGlmeShrZXksIEludGxFcnJvckNvZGUuTUlTU0lOR19NRVNTQUdFLCBlcnJvci5tZXNzYWdlKTtcbiAgICB9XG4gICAgaWYgKHR5cGVvZiBtZXNzYWdlID09PSAnb2JqZWN0Jykge1xuICAgICAgbGV0IGNvZGUsIGVycm9yTWVzc2FnZTtcbiAgICAgIGlmIChBcnJheS5pc0FycmF5KG1lc3NhZ2UpKSB7XG4gICAgICAgIGNvZGUgPSBJbnRsRXJyb3JDb2RlLklOVkFMSURfTUVTU0FHRTtcbiAgICAgICAge1xuICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGBNZXNzYWdlIGF0IFxcYCR7am9pblBhdGgobmFtZXNwYWNlLCBrZXkpfVxcYCByZXNvbHZlZCB0byBhbiBhcnJheSwgYnV0IG9ubHkgc3RyaW5ncyBhcmUgc3VwcG9ydGVkLiBTZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvdXNhZ2UvbWVzc2FnZXMjYXJyYXlzLW9mLW1lc3NhZ2VzYDtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29kZSA9IEludGxFcnJvckNvZGUuSU5TVUZGSUNJRU5UX1BBVEg7XG4gICAgICAgIHtcbiAgICAgICAgICBlcnJvck1lc3NhZ2UgPSBgTWVzc2FnZSBhdCBcXGAke2pvaW5QYXRoKG5hbWVzcGFjZSwga2V5KX1cXGAgcmVzb2x2ZWQgdG8gYW4gb2JqZWN0LCBidXQgb25seSBzdHJpbmdzIGFyZSBzdXBwb3J0ZWQuIFVzZSBhIFxcYC5cXGAgdG8gcmV0cmlldmUgbmVzdGVkIG1lc3NhZ2VzLiBTZWUgaHR0cHM6Ly9uZXh0LWludGwuZGV2L2RvY3MvdXNhZ2UvbWVzc2FnZXMjc3RydWN0dXJpbmctbWVzc2FnZXNgO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZXR1cm4gZ2V0RmFsbGJhY2tGcm9tRXJyb3JBbmROb3RpZnkoa2V5LCBjb2RlLCBlcnJvck1lc3NhZ2UpO1xuICAgIH1cbiAgICBsZXQgbWVzc2FnZUZvcm1hdDtcblxuICAgIC8vIEhvdCBwYXRoIHRoYXQgYXZvaWRzIGNyZWF0aW5nIGFuIGBJbnRsTWVzc2FnZUZvcm1hdGAgaW5zdGFuY2VcbiAgICBjb25zdCBwbGFpbk1lc3NhZ2UgPSBnZXRQbGFpbk1lc3NhZ2UobWVzc2FnZSwgdmFsdWVzKTtcbiAgICBpZiAocGxhaW5NZXNzYWdlKSByZXR1cm4gcGxhaW5NZXNzYWdlO1xuXG4gICAgLy8gTGF6eSBpbml0IHRoZSBtZXNzYWdlIGZvcm1hdHRlciBmb3IgYmV0dGVyIHRyZWVcbiAgICAvLyBzaGFraW5nIGluIGNhc2UgbWVzc2FnZSBmb3JtYXR0aW5nIGlzIG5vdCB1c2VkLlxuICAgIGlmICghZm9ybWF0dGVycy5nZXRNZXNzYWdlRm9ybWF0KSB7XG4gICAgICBmb3JtYXR0ZXJzLmdldE1lc3NhZ2VGb3JtYXQgPSBjcmVhdGVNZXNzYWdlRm9ybWF0dGVyKGNhY2hlLCBmb3JtYXR0ZXJzKTtcbiAgICB9XG4gICAgdHJ5IHtcbiAgICAgIG1lc3NhZ2VGb3JtYXQgPSBmb3JtYXR0ZXJzLmdldE1lc3NhZ2VGb3JtYXQobWVzc2FnZSwgbG9jYWxlLCBjb252ZXJ0Rm9ybWF0c1RvSW50bE1lc3NhZ2VGb3JtYXQoZ2xvYmFsRm9ybWF0cywgZm9ybWF0cywgdGltZVpvbmUpLCB7XG4gICAgICAgIGZvcm1hdHRlcnM6IHtcbiAgICAgICAgICAuLi5mb3JtYXR0ZXJzLFxuICAgICAgICAgIGdldERhdGVUaW1lRm9ybWF0KGxvY2FsZXMsIG9wdGlvbnMpIHtcbiAgICAgICAgICAgIC8vIFdvcmthcm91bmQgZm9yIGh0dHBzOi8vZ2l0aHViLmNvbS9mb3JtYXRqcy9mb3JtYXRqcy9pc3N1ZXMvNDI3OVxuICAgICAgICAgICAgcmV0dXJuIGZvcm1hdHRlcnMuZ2V0RGF0ZVRpbWVGb3JtYXQobG9jYWxlcywge1xuICAgICAgICAgICAgICB0aW1lWm9uZSxcbiAgICAgICAgICAgICAgLi4ub3B0aW9uc1xuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgdGhyb3duRXJyb3IgPSBlcnJvcjtcbiAgICAgIHJldHVybiBnZXRGYWxsYmFja0Zyb21FcnJvckFuZE5vdGlmeShrZXksIEludGxFcnJvckNvZGUuSU5WQUxJRF9NRVNTQUdFLCB0aHJvd25FcnJvci5tZXNzYWdlICsgKCdvcmlnaW5hbE1lc3NhZ2UnIGluIHRocm93bkVycm9yID8gYCAoJHt0aHJvd25FcnJvci5vcmlnaW5hbE1lc3NhZ2V9KWAgOiAnJykgKTtcbiAgICB9XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGZvcm1hdHRlZE1lc3NhZ2UgPSBtZXNzYWdlRm9ybWF0LmZvcm1hdChcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgYGludGwtbWVzc2FnZWZvcm1hdGAgZXhwZWN0cyBhIGRpZmZlcmVudCBmb3JtYXRcbiAgICAgIC8vIGZvciByaWNoIHRleHQgZWxlbWVudHMgc2luY2UgYSByZWNlbnQgbWlub3IgdXBkYXRlLiBUaGlzXG4gICAgICAvLyBuZWVkcyB0byBiZSBldmFsdWF0ZWQgaW4gZGV0YWlsLCBwb3NzaWJseSBhbHNvIGluIHJlZ2FyZHNcbiAgICAgIC8vIHRvIGJlIGFibGUgdG8gZm9ybWF0IHRvIHBhcnRzLlxuICAgICAgdmFsdWVzID8gcHJlcGFyZVRyYW5zbGF0aW9uVmFsdWVzKHZhbHVlcykgOiB2YWx1ZXMpO1xuICAgICAgaWYgKGZvcm1hdHRlZE1lc3NhZ2UgPT0gbnVsbCkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuYWJsZSB0byBmb3JtYXQgXFxgJHtrZXl9XFxgIGluICR7bmFtZXNwYWNlID8gYG5hbWVzcGFjZSBcXGAke25hbWVzcGFjZX1cXGBgIDogJ21lc3NhZ2VzJ31gICk7XG4gICAgICB9XG5cbiAgICAgIC8vIExpbWl0IHRoZSBmdW5jdGlvbiBzaWduYXR1cmUgdG8gcmV0dXJuIHN0cmluZ3Mgb3IgUmVhY3QgZWxlbWVudHNcbiAgICAgIHJldHVybiAvKiNfX1BVUkVfXyovaXNWYWxpZEVsZW1lbnQoZm9ybWF0dGVkTWVzc2FnZSkgfHxcbiAgICAgIC8vIEFycmF5cyBvZiBSZWFjdCBlbGVtZW50c1xuICAgICAgQXJyYXkuaXNBcnJheShmb3JtYXR0ZWRNZXNzYWdlKSB8fCB0eXBlb2YgZm9ybWF0dGVkTWVzc2FnZSA9PT0gJ3N0cmluZycgPyBmb3JtYXR0ZWRNZXNzYWdlIDogU3RyaW5nKGZvcm1hdHRlZE1lc3NhZ2UpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4gZ2V0RmFsbGJhY2tGcm9tRXJyb3JBbmROb3RpZnkoa2V5LCBJbnRsRXJyb3JDb2RlLkZPUk1BVFRJTkdfRVJST1IsIGVycm9yLm1lc3NhZ2UpO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiB0cmFuc2xhdGVGbigvKiogVXNlIGEgZG90IHRvIGluZGljYXRlIGEgbGV2ZWwgb2YgbmVzdGluZyAoZS5nLiBgbmFtZXNwYWNlLm5lc3RlZExhYmVsYCkuICovXG4gIGtleSwgLyoqIEtleSB2YWx1ZSBwYWlycyBmb3IgdmFsdWVzIHRvIGludGVycG9sYXRlIGludG8gdGhlIG1lc3NhZ2UuICovXG4gIHZhbHVlcywgLyoqIFByb3ZpZGUgY3VzdG9tIGZvcm1hdHMgZm9yIG51bWJlcnMsIGRhdGVzIGFuZCB0aW1lcy4gKi9cbiAgZm9ybWF0cykge1xuICAgIGNvbnN0IHJlc3VsdCA9IHRyYW5zbGF0ZUJhc2VGbihrZXksIHZhbHVlcywgZm9ybWF0cyk7XG4gICAgaWYgKHR5cGVvZiByZXN1bHQgIT09ICdzdHJpbmcnKSB7XG4gICAgICByZXR1cm4gZ2V0RmFsbGJhY2tGcm9tRXJyb3JBbmROb3RpZnkoa2V5LCBJbnRsRXJyb3JDb2RlLklOVkFMSURfTUVTU0FHRSwgYFRoZSBtZXNzYWdlIFxcYCR7a2V5fVxcYCBpbiAke25hbWVzcGFjZSA/IGBuYW1lc3BhY2UgXFxgJHtuYW1lc3BhY2V9XFxgYCA6ICdtZXNzYWdlcyd9IGRpZG4ndCByZXNvbHZlIHRvIGEgc3RyaW5nLiBJZiB5b3Ugd2FudCB0byBmb3JtYXQgcmljaCB0ZXh0LCB1c2UgXFxgdC5yaWNoXFxgIGluc3RlYWQuYCApO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xuICB9XG4gIHRyYW5zbGF0ZUZuLnJpY2ggPSB0cmFuc2xhdGVCYXNlRm47XG5cbiAgLy8gQXVnbWVudCBgdHJhbnNsYXRlQmFzZUZuYCB0byByZXR1cm4gcGxhaW4gc3RyaW5nc1xuICB0cmFuc2xhdGVGbi5tYXJrdXAgPSAoa2V5LCB2YWx1ZXMsIGZvcm1hdHMpID0+IHtcbiAgICBjb25zdCByZXN1bHQgPSB0cmFuc2xhdGVCYXNlRm4oa2V5LFxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgLS0gYE1hcmt1cFRyYW5zbGF0aW9uVmFsdWVzYCBpcyBwcmFjdGljYWxseSBhIHN1YiB0eXBlXG4gICAgLy8gb2YgYFJpY2hUcmFuc2xhdGlvblZhbHVlc2AgYnV0IFR5cGVTY3JpcHQgaXNuJ3Qgc21hcnQgZW5vdWdoIGhlcmUuXG4gICAgdmFsdWVzLCBmb3JtYXRzKTtcbiAgICBpZiAodHlwZW9mIHJlc3VsdCAhPT0gJ3N0cmluZycpIHtcbiAgICAgIGNvbnN0IGVycm9yID0gbmV3IEludGxFcnJvcihJbnRsRXJyb3JDb2RlLkZPUk1BVFRJTkdfRVJST1IsIFwiYHQubWFya3VwYCBvbmx5IGFjY2VwdHMgZnVuY3Rpb25zIGZvciBmb3JtYXR0aW5nIHRoYXQgcmVjZWl2ZSBhbmQgcmV0dXJuIHN0cmluZ3MuXFxuXFxuRS5nLiB0Lm1hcmt1cCgnbWFya3VwJywge2I6IChjaHVua3MpID0+IGA8Yj4ke2NodW5rc308L2I+YH0pXCIpO1xuICAgICAgb25FcnJvcihlcnJvcik7XG4gICAgICByZXR1cm4gZ2V0TWVzc2FnZUZhbGxiYWNrKHtcbiAgICAgICAgZXJyb3IsXG4gICAgICAgIGtleSxcbiAgICAgICAgbmFtZXNwYWNlXG4gICAgICB9KTtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfTtcbiAgdHJhbnNsYXRlRm4ucmF3ID0ga2V5ID0+IHtcbiAgICBpZiAoaGFzTWVzc2FnZXNFcnJvcikge1xuICAgICAgLy8gV2UgaGF2ZSBhbHJlYWR5IHdhcm5lZCBhYm91dCB0aGlzIGR1cmluZyByZW5kZXJcbiAgICAgIHJldHVybiBnZXRNZXNzYWdlRmFsbGJhY2soe1xuICAgICAgICBlcnJvcjogbWVzc2FnZXNPckVycm9yLFxuICAgICAgICBrZXksXG4gICAgICAgIG5hbWVzcGFjZVxuICAgICAgfSk7XG4gICAgfVxuICAgIGNvbnN0IG1lc3NhZ2VzID0gbWVzc2FnZXNPckVycm9yO1xuICAgIHRyeSB7XG4gICAgICByZXR1cm4gcmVzb2x2ZVBhdGgobG9jYWxlLCBtZXNzYWdlcywga2V5LCBuYW1lc3BhY2UpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICByZXR1cm4gZ2V0RmFsbGJhY2tGcm9tRXJyb3JBbmROb3RpZnkoa2V5LCBJbnRsRXJyb3JDb2RlLk1JU1NJTkdfTUVTU0FHRSwgZXJyb3IubWVzc2FnZSk7XG4gICAgfVxuICB9O1xuICB0cmFuc2xhdGVGbi5oYXMgPSBrZXkgPT4ge1xuICAgIGlmIChoYXNNZXNzYWdlc0Vycm9yKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHRyeSB7XG4gICAgICByZXNvbHZlUGF0aChsb2NhbGUsIG1lc3NhZ2VzT3JFcnJvciwga2V5LCBuYW1lc3BhY2UpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBjYXRjaCB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9O1xuICByZXR1cm4gdHJhbnNsYXRlRm47XG59XG5cbi8qKlxuICogRm9yIHRoZSBzdHJpY3RseSB0eXBlZCBtZXNzYWdlcyB0byB3b3JrIHdlIGhhdmUgdG8gd3JhcCB0aGUgbmFtZXNwYWNlIGludG9cbiAqIGEgbWFuZGF0b3J5IHByZWZpeC4gU2VlIGh0dHBzOi8vc3RhY2tvdmVyZmxvdy5jb20vYS83MTUyOTU3NS8zNDMwNDVcbiAqL1xuZnVuY3Rpb24gcmVzb2x2ZU5hbWVzcGFjZShuYW1lc3BhY2UsIG5hbWVzcGFjZVByZWZpeCkge1xuICByZXR1cm4gbmFtZXNwYWNlID09PSBuYW1lc3BhY2VQcmVmaXggPyB1bmRlZmluZWQgOiBuYW1lc3BhY2Uuc2xpY2UoKG5hbWVzcGFjZVByZWZpeCArICcuJykubGVuZ3RoKTtcbn1cblxuY29uc3QgU0VDT05EID0gMTtcbmNvbnN0IE1JTlVURSA9IFNFQ09ORCAqIDYwO1xuY29uc3QgSE9VUiA9IE1JTlVURSAqIDYwO1xuY29uc3QgREFZID0gSE9VUiAqIDI0O1xuY29uc3QgV0VFSyA9IERBWSAqIDc7XG5jb25zdCBNT05USCA9IERBWSAqICgzNjUgLyAxMik7IC8vIEFwcHJveGltYXRpb25cbmNvbnN0IFFVQVJURVIgPSBNT05USCAqIDM7XG5jb25zdCBZRUFSID0gREFZICogMzY1O1xuY29uc3QgVU5JVF9TRUNPTkRTID0ge1xuICBzZWNvbmQ6IFNFQ09ORCxcbiAgc2Vjb25kczogU0VDT05ELFxuICBtaW51dGU6IE1JTlVURSxcbiAgbWludXRlczogTUlOVVRFLFxuICBob3VyOiBIT1VSLFxuICBob3VyczogSE9VUixcbiAgZGF5OiBEQVksXG4gIGRheXM6IERBWSxcbiAgd2VlazogV0VFSyxcbiAgd2Vla3M6IFdFRUssXG4gIG1vbnRoOiBNT05USCxcbiAgbW9udGhzOiBNT05USCxcbiAgcXVhcnRlcjogUVVBUlRFUixcbiAgcXVhcnRlcnM6IFFVQVJURVIsXG4gIHllYXI6IFlFQVIsXG4gIHllYXJzOiBZRUFSXG59O1xuZnVuY3Rpb24gcmVzb2x2ZVJlbGF0aXZlVGltZVVuaXQoc2Vjb25kcykge1xuICBjb25zdCBhYnNWYWx1ZSA9IE1hdGguYWJzKHNlY29uZHMpO1xuICBpZiAoYWJzVmFsdWUgPCBNSU5VVEUpIHtcbiAgICByZXR1cm4gJ3NlY29uZCc7XG4gIH0gZWxzZSBpZiAoYWJzVmFsdWUgPCBIT1VSKSB7XG4gICAgcmV0dXJuICdtaW51dGUnO1xuICB9IGVsc2UgaWYgKGFic1ZhbHVlIDwgREFZKSB7XG4gICAgcmV0dXJuICdob3VyJztcbiAgfSBlbHNlIGlmIChhYnNWYWx1ZSA8IFdFRUspIHtcbiAgICByZXR1cm4gJ2RheSc7XG4gIH0gZWxzZSBpZiAoYWJzVmFsdWUgPCBNT05USCkge1xuICAgIHJldHVybiAnd2Vlayc7XG4gIH0gZWxzZSBpZiAoYWJzVmFsdWUgPCBZRUFSKSB7XG4gICAgcmV0dXJuICdtb250aCc7XG4gIH1cbiAgcmV0dXJuICd5ZWFyJztcbn1cbmZ1bmN0aW9uIGNhbGN1bGF0ZVJlbGF0aXZlVGltZVZhbHVlKHNlY29uZHMsIHVuaXQpIHtcbiAgLy8gV2UgaGF2ZSB0byByb3VuZCB0aGUgcmVzdWx0aW5nIHZhbHVlcywgYXMgYEludGwuUmVsYXRpdmVUaW1lRm9ybWF0YFxuICAvLyB3aWxsIGluY2x1ZGUgZnJhY3Rpb25zIGxpa2UgJzIuMSBob3VycyBhZ28nLlxuICByZXR1cm4gTWF0aC5yb3VuZChzZWNvbmRzIC8gVU5JVF9TRUNPTkRTW3VuaXRdKTtcbn1cbmZ1bmN0aW9uIGNyZWF0ZUZvcm1hdHRlcihwcm9wcykge1xuICBjb25zdCB7XG4gICAgX2NhY2hlOiBjYWNoZSA9IGNyZWF0ZUNhY2hlKCksXG4gICAgX2Zvcm1hdHRlcnM6IGZvcm1hdHRlcnMgPSBjcmVhdGVJbnRsRm9ybWF0dGVycyhjYWNoZSksXG4gICAgZm9ybWF0cyxcbiAgICBsb2NhbGUsXG4gICAgb25FcnJvciA9IGRlZmF1bHRPbkVycm9yLFxuICAgIHRpbWVab25lOiBnbG9iYWxUaW1lWm9uZVxuICB9ID0gcHJvcHM7XG4gIGZ1bmN0aW9uIGFwcGx5VGltZVpvbmUob3B0aW9ucykge1xuICAgIGlmICghb3B0aW9ucz8udGltZVpvbmUpIHtcbiAgICAgIGlmIChnbG9iYWxUaW1lWm9uZSkge1xuICAgICAgICBvcHRpb25zID0ge1xuICAgICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgICAgdGltZVpvbmU6IGdsb2JhbFRpbWVab25lXG4gICAgICAgIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBvbkVycm9yKG5ldyBJbnRsRXJyb3IoSW50bEVycm9yQ29kZS5FTlZJUk9OTUVOVF9GQUxMQkFDSywgYFRoZSBcXGB0aW1lWm9uZVxcYCBwYXJhbWV0ZXIgd2Fzbid0IHByb3ZpZGVkIGFuZCB0aGVyZSBpcyBubyBnbG9iYWwgZGVmYXVsdCBjb25maWd1cmVkLiBDb25zaWRlciBhZGRpbmcgYSBnbG9iYWwgZGVmYXVsdCB0byBhdm9pZCBtYXJrdXAgbWlzbWF0Y2hlcyBjYXVzZWQgYnkgZW52aXJvbm1lbnQgZGlmZmVyZW5jZXMuIExlYXJuIG1vcmU6IGh0dHBzOi8vbmV4dC1pbnRsLmRldi9kb2NzL2NvbmZpZ3VyYXRpb24jdGltZS16b25lYCApKTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG9wdGlvbnM7XG4gIH1cbiAgZnVuY3Rpb24gcmVzb2x2ZUZvcm1hdE9yT3B0aW9ucyh0eXBlRm9ybWF0cywgZm9ybWF0T3JPcHRpb25zLCBvdmVycmlkZXMpIHtcbiAgICBsZXQgb3B0aW9ucztcbiAgICBpZiAodHlwZW9mIGZvcm1hdE9yT3B0aW9ucyA9PT0gJ3N0cmluZycpIHtcbiAgICAgIGNvbnN0IGZvcm1hdE5hbWUgPSBmb3JtYXRPck9wdGlvbnM7XG4gICAgICBvcHRpb25zID0gdHlwZUZvcm1hdHM/Lltmb3JtYXROYW1lXTtcbiAgICAgIGlmICghb3B0aW9ucykge1xuICAgICAgICBjb25zdCBlcnJvciA9IG5ldyBJbnRsRXJyb3IoSW50bEVycm9yQ29kZS5NSVNTSU5HX0ZPUk1BVCwgYEZvcm1hdCBcXGAke2Zvcm1hdE5hbWV9XFxgIGlzIG5vdCBhdmFpbGFibGUuYCApO1xuICAgICAgICBvbkVycm9yKGVycm9yKTtcbiAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIG9wdGlvbnMgPSBmb3JtYXRPck9wdGlvbnM7XG4gICAgfVxuICAgIGlmIChvdmVycmlkZXMpIHtcbiAgICAgIG9wdGlvbnMgPSB7XG4gICAgICAgIC4uLm9wdGlvbnMsXG4gICAgICAgIC4uLm92ZXJyaWRlc1xuICAgICAgfTtcbiAgICB9XG4gICAgcmV0dXJuIG9wdGlvbnM7XG4gIH1cbiAgZnVuY3Rpb24gZ2V0Rm9ybWF0dGVkVmFsdWUoZm9ybWF0T3JPcHRpb25zLCBvdmVycmlkZXMsIHR5cGVGb3JtYXRzLCBmb3JtYXR0ZXIsIGdldEZhbGxiYWNrKSB7XG4gICAgbGV0IG9wdGlvbnM7XG4gICAgdHJ5IHtcbiAgICAgIG9wdGlvbnMgPSByZXNvbHZlRm9ybWF0T3JPcHRpb25zKHR5cGVGb3JtYXRzLCBmb3JtYXRPck9wdGlvbnMsIG92ZXJyaWRlcyk7XG4gICAgfSBjYXRjaCB7XG4gICAgICByZXR1cm4gZ2V0RmFsbGJhY2soKTtcbiAgICB9XG4gICAgdHJ5IHtcbiAgICAgIHJldHVybiBmb3JtYXR0ZXIob3B0aW9ucyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIG9uRXJyb3IobmV3IEludGxFcnJvcihJbnRsRXJyb3JDb2RlLkZPUk1BVFRJTkdfRVJST1IsIGVycm9yLm1lc3NhZ2UpKTtcbiAgICAgIHJldHVybiBnZXRGYWxsYmFjaygpO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBkYXRlVGltZSh2YWx1ZSwgZm9ybWF0T3JPcHRpb25zLCBvdmVycmlkZXMpIHtcbiAgICByZXR1cm4gZ2V0Rm9ybWF0dGVkVmFsdWUoZm9ybWF0T3JPcHRpb25zLCBvdmVycmlkZXMsIGZvcm1hdHM/LmRhdGVUaW1lLCBvcHRpb25zID0+IHtcbiAgICAgIG9wdGlvbnMgPSBhcHBseVRpbWVab25lKG9wdGlvbnMpO1xuICAgICAgcmV0dXJuIGZvcm1hdHRlcnMuZ2V0RGF0ZVRpbWVGb3JtYXQobG9jYWxlLCBvcHRpb25zKS5mb3JtYXQodmFsdWUpO1xuICAgIH0sICgpID0+IFN0cmluZyh2YWx1ZSkpO1xuICB9XG4gIGZ1bmN0aW9uIGRhdGVUaW1lUmFuZ2Uoc3RhcnQsIGVuZCwgZm9ybWF0T3JPcHRpb25zLCBvdmVycmlkZXMpIHtcbiAgICByZXR1cm4gZ2V0Rm9ybWF0dGVkVmFsdWUoZm9ybWF0T3JPcHRpb25zLCBvdmVycmlkZXMsIGZvcm1hdHM/LmRhdGVUaW1lLCBvcHRpb25zID0+IHtcbiAgICAgIG9wdGlvbnMgPSBhcHBseVRpbWVab25lKG9wdGlvbnMpO1xuICAgICAgcmV0dXJuIGZvcm1hdHRlcnMuZ2V0RGF0ZVRpbWVGb3JtYXQobG9jYWxlLCBvcHRpb25zKS5mb3JtYXRSYW5nZShzdGFydCwgZW5kKTtcbiAgICB9LCAoKSA9PiBbZGF0ZVRpbWUoc3RhcnQpLCBkYXRlVGltZShlbmQpXS5qb2luKCfigInigJPigIknKSk7XG4gIH1cbiAgZnVuY3Rpb24gbnVtYmVyKHZhbHVlLCBmb3JtYXRPck9wdGlvbnMsIG92ZXJyaWRlcykge1xuICAgIHJldHVybiBnZXRGb3JtYXR0ZWRWYWx1ZShmb3JtYXRPck9wdGlvbnMsIG92ZXJyaWRlcywgZm9ybWF0cz8ubnVtYmVyLCBvcHRpb25zID0+IGZvcm1hdHRlcnMuZ2V0TnVtYmVyRm9ybWF0KGxvY2FsZSwgb3B0aW9ucykuZm9ybWF0KHZhbHVlKSwgKCkgPT4gU3RyaW5nKHZhbHVlKSk7XG4gIH1cbiAgZnVuY3Rpb24gZ2V0R2xvYmFsTm93KCkge1xuICAgIC8vIE9ubHkgcmVhZCB3aGVuIG5lY2Vzc2FyeSB0byBhdm9pZCB0cmlnZ2VyaW5nIGEgYGR5bmFtaWNJT2AgZXJyb3JcbiAgICAvLyB1bm5lY2Vzc2FyaWx5IChgbm93YCBpcyBvbmx5IG5lZWRlZCBmb3IgYGZvcm1hdC5yZWxhdGl2ZVRpbWVgKVxuICAgIGlmIChwcm9wcy5ub3cpIHtcbiAgICAgIHJldHVybiBwcm9wcy5ub3c7XG4gICAgfSBlbHNlIHtcbiAgICAgIG9uRXJyb3IobmV3IEludGxFcnJvcihJbnRsRXJyb3JDb2RlLkVOVklST05NRU5UX0ZBTExCQUNLLCBgVGhlIFxcYG5vd1xcYCBwYXJhbWV0ZXIgd2Fzbid0IHByb3ZpZGVkIHRvIFxcYHJlbGF0aXZlVGltZVxcYCBhbmQgdGhlcmUgaXMgbm8gZ2xvYmFsIGRlZmF1bHQgY29uZmlndXJlZCwgdGhlcmVmb3JlIHRoZSBjdXJyZW50IHRpbWUgd2lsbCBiZSB1c2VkIGFzIGEgZmFsbGJhY2suIFNlZSBodHRwczovL25leHQtaW50bC5kZXYvZG9jcy91c2FnZS9kYXRlcy10aW1lcyNyZWxhdGl2ZS10aW1lcy11c2Vub3dgICkpO1xuICAgICAgcmV0dXJuIG5ldyBEYXRlKCk7XG4gICAgfVxuICB9XG4gIGZ1bmN0aW9uIHJlbGF0aXZlVGltZShkYXRlLCBub3dPck9wdGlvbnMpIHtcbiAgICB0cnkge1xuICAgICAgbGV0IG5vd0RhdGUsIHVuaXQ7XG4gICAgICBjb25zdCBvcHRzID0ge307XG4gICAgICBpZiAobm93T3JPcHRpb25zIGluc3RhbmNlb2YgRGF0ZSB8fCB0eXBlb2Ygbm93T3JPcHRpb25zID09PSAnbnVtYmVyJykge1xuICAgICAgICBub3dEYXRlID0gbmV3IERhdGUobm93T3JPcHRpb25zKTtcbiAgICAgIH0gZWxzZSBpZiAobm93T3JPcHRpb25zKSB7XG4gICAgICAgIGlmIChub3dPck9wdGlvbnMubm93ICE9IG51bGwpIHtcbiAgICAgICAgICBub3dEYXRlID0gbmV3IERhdGUobm93T3JPcHRpb25zLm5vdyk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgbm93RGF0ZSA9IGdldEdsb2JhbE5vdygpO1xuICAgICAgICB9XG4gICAgICAgIHVuaXQgPSBub3dPck9wdGlvbnMudW5pdDtcbiAgICAgICAgb3B0cy5zdHlsZSA9IG5vd09yT3B0aW9ucy5zdHlsZTtcbiAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvciAtLSBUeXBlcyBhcmUgc2xpZ2h0bHkgb3V0ZGF0ZWRcbiAgICAgICAgb3B0cy5udW1iZXJpbmdTeXN0ZW0gPSBub3dPck9wdGlvbnMubnVtYmVyaW5nU3lzdGVtO1xuICAgICAgfVxuICAgICAgaWYgKCFub3dEYXRlKSB7XG4gICAgICAgIG5vd0RhdGUgPSBnZXRHbG9iYWxOb3coKTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGRhdGVEYXRlID0gbmV3IERhdGUoZGF0ZSk7XG4gICAgICBjb25zdCBzZWNvbmRzID0gKGRhdGVEYXRlLmdldFRpbWUoKSAtIG5vd0RhdGUuZ2V0VGltZSgpKSAvIDEwMDA7XG4gICAgICBpZiAoIXVuaXQpIHtcbiAgICAgICAgdW5pdCA9IHJlc29sdmVSZWxhdGl2ZVRpbWVVbml0KHNlY29uZHMpO1xuICAgICAgfVxuXG4gICAgICAvLyBgbnVtZXJpYzogJ2F1dG8nYCBjYW4gdGhlb3JldGljYWxseSBwcm9kdWNlIG91dHB1dCBsaWtlIFwieWVzdGVyZGF5XCIsXG4gICAgICAvLyBidXQgaXQgb25seSB3b3JrcyB3aXRoIGludGVnZXJzLiBFLmcuIC0xIGRheSB3aWxsIHByb2R1Y2UgXCJ5ZXN0ZXJkYXlcIixcbiAgICAgIC8vIGJ1dCAtMS4xIGRheXMgd2lsbCBwcm9kdWNlIFwiLTEuMSBkYXlzXCIuIFJvdW5kaW5nIGJlZm9yZSBmb3JtYXR0aW5nIGlzXG4gICAgICAvLyBub3QgZGVzaXJlZCwgYXMgdGhlIGdpdmVuIGRhdGVzIG1pZ2h0IGNyb3NzIGEgdGhyZXNob2xkIHdlcmUgdGhlXG4gICAgICAvLyBvdXRwdXQgaXNuJ3QgY29ycmVjdCBhbnltb3JlLiBFeGFtcGxlOiAyMDI0LTAxLTA4VDIzOjAwOjAwLjAwMFogYW5kXG4gICAgICAvLyAyMDI0LTAxLTA4VDAxOjAwOjAwLjAwMFogd291bGQgcHJvZHVjZSBcInllc3RlcmRheVwiLCB3aGljaCBpcyBub3QgdGhlXG4gICAgICAvLyBjYXNlLiBCeSB1c2luZyBgYWx3YXlzYCB3ZSBjYW4gZW5zdXJlIGNvcnJlY3Qgb3V0cHV0LiBUaGUgb25seSBleGNlcHRpb25cbiAgICAgIC8vIGlzIHRoZSBmb3JtYXR0aW5nIG9mIHRpbWVzIDwxIHNlY29uZCBhcyBcIm5vd1wiLlxuICAgICAgb3B0cy5udW1lcmljID0gdW5pdCA9PT0gJ3NlY29uZCcgPyAnYXV0bycgOiAnYWx3YXlzJztcbiAgICAgIGNvbnN0IHZhbHVlID0gY2FsY3VsYXRlUmVsYXRpdmVUaW1lVmFsdWUoc2Vjb25kcywgdW5pdCk7XG4gICAgICByZXR1cm4gZm9ybWF0dGVycy5nZXRSZWxhdGl2ZVRpbWVGb3JtYXQobG9jYWxlLCBvcHRzKS5mb3JtYXQodmFsdWUsIHVuaXQpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBvbkVycm9yKG5ldyBJbnRsRXJyb3IoSW50bEVycm9yQ29kZS5GT1JNQVRUSU5HX0VSUk9SLCBlcnJvci5tZXNzYWdlKSk7XG4gICAgICByZXR1cm4gU3RyaW5nKGRhdGUpO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBsaXN0KHZhbHVlLCBmb3JtYXRPck9wdGlvbnMsIG92ZXJyaWRlcykge1xuICAgIGNvbnN0IHNlcmlhbGl6ZWRWYWx1ZSA9IFtdO1xuICAgIGNvbnN0IHJpY2hWYWx1ZXMgPSBuZXcgTWFwKCk7XG5cbiAgICAvLyBgZm9ybWF0VG9QYXJ0c2Agb25seSBhY2NlcHRzIHN0cmluZ3MsIHRoZXJlZm9yZSB3ZSBoYXZlIHRvIHRlbXBvcmFyaWx5XG4gICAgLy8gcmVwbGFjZSBSZWFjdCBlbGVtZW50cyB3aXRoIGEgcGxhY2Vob2xkZXIgSUQgdGhhdCBjYW4gYmUgdXNlZCB0byByZXRyaWV2ZVxuICAgIC8vIHRoZSBvcmlnaW5hbCB2YWx1ZSBhZnRlcndhcmRzLlxuICAgIGxldCBpbmRleCA9IDA7XG4gICAgZm9yIChjb25zdCBpdGVtIG9mIHZhbHVlKSB7XG4gICAgICBsZXQgc2VyaWFsaXplZEl0ZW07XG4gICAgICBpZiAodHlwZW9mIGl0ZW0gPT09ICdvYmplY3QnKSB7XG4gICAgICAgIHNlcmlhbGl6ZWRJdGVtID0gU3RyaW5nKGluZGV4KTtcbiAgICAgICAgcmljaFZhbHVlcy5zZXQoc2VyaWFsaXplZEl0ZW0sIGl0ZW0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2VyaWFsaXplZEl0ZW0gPSBTdHJpbmcoaXRlbSk7XG4gICAgICB9XG4gICAgICBzZXJpYWxpemVkVmFsdWUucHVzaChzZXJpYWxpemVkSXRlbSk7XG4gICAgICBpbmRleCsrO1xuICAgIH1cbiAgICByZXR1cm4gZ2V0Rm9ybWF0dGVkVmFsdWUoZm9ybWF0T3JPcHRpb25zLCBvdmVycmlkZXMsIGZvcm1hdHM/Lmxpc3QsXG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvciAtLSBgcmljaFZhbHVlcy5zaXplYCBpcyB1c2VkIHRvIGRldGVybWluZSB0aGUgcmV0dXJuIHR5cGUsIGJ1dCBUeXBlU2NyaXB0IGNhbid0IGluZmVyIHRoZSBtZWFuaW5nIG9mIHRoaXMgY29ycmVjdGx5XG4gICAgb3B0aW9ucyA9PiB7XG4gICAgICBjb25zdCByZXN1bHQgPSBmb3JtYXR0ZXJzLmdldExpc3RGb3JtYXQobG9jYWxlLCBvcHRpb25zKS5mb3JtYXRUb1BhcnRzKHNlcmlhbGl6ZWRWYWx1ZSkubWFwKHBhcnQgPT4gcGFydC50eXBlID09PSAnbGl0ZXJhbCcgPyBwYXJ0LnZhbHVlIDogcmljaFZhbHVlcy5nZXQocGFydC52YWx1ZSkgfHwgcGFydC52YWx1ZSk7XG4gICAgICBpZiAocmljaFZhbHVlcy5zaXplID4gMCkge1xuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuIHJlc3VsdC5qb2luKCcnKTtcbiAgICAgIH1cbiAgICB9LCAoKSA9PiBTdHJpbmcodmFsdWUpKTtcbiAgfVxuICByZXR1cm4ge1xuICAgIGRhdGVUaW1lLFxuICAgIG51bWJlcixcbiAgICByZWxhdGl2ZVRpbWUsXG4gICAgbGlzdCxcbiAgICBkYXRlVGltZVJhbmdlXG4gIH07XG59XG5cbmZ1bmN0aW9uIHZhbGlkYXRlTWVzc2FnZXNTZWdtZW50KG1lc3NhZ2VzLCBpbnZhbGlkS2V5TGFiZWxzLCBwYXJlbnRQYXRoKSB7XG4gIE9iamVjdC5lbnRyaWVzKG1lc3NhZ2VzKS5mb3JFYWNoKChba2V5LCBtZXNzYWdlT3JNZXNzYWdlc10pID0+IHtcbiAgICBpZiAoa2V5LmluY2x1ZGVzKCcuJykpIHtcbiAgICAgIGxldCBrZXlMYWJlbCA9IGtleTtcbiAgICAgIGlmIChwYXJlbnRQYXRoKSBrZXlMYWJlbCArPSBgIChhdCAke3BhcmVudFBhdGh9KWA7XG4gICAgICBpbnZhbGlkS2V5TGFiZWxzLnB1c2goa2V5TGFiZWwpO1xuICAgIH1cblxuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdW5uZWNlc3NhcnktY29uZGl0aW9uXG4gICAgaWYgKG1lc3NhZ2VPck1lc3NhZ2VzICE9IG51bGwgJiYgdHlwZW9mIG1lc3NhZ2VPck1lc3NhZ2VzID09PSAnb2JqZWN0Jykge1xuICAgICAgdmFsaWRhdGVNZXNzYWdlc1NlZ21lbnQobWVzc2FnZU9yTWVzc2FnZXMsIGludmFsaWRLZXlMYWJlbHMsIGpvaW5QYXRoKHBhcmVudFBhdGgsIGtleSkpO1xuICAgIH1cbiAgfSk7XG59XG5mdW5jdGlvbiB2YWxpZGF0ZU1lc3NhZ2VzKG1lc3NhZ2VzLCBvbkVycm9yKSB7XG4gIGNvbnN0IGludmFsaWRLZXlMYWJlbHMgPSBbXTtcbiAgdmFsaWRhdGVNZXNzYWdlc1NlZ21lbnQobWVzc2FnZXMsIGludmFsaWRLZXlMYWJlbHMpO1xuICBpZiAoaW52YWxpZEtleUxhYmVscy5sZW5ndGggPiAwKSB7XG4gICAgb25FcnJvcihuZXcgSW50bEVycm9yKEludGxFcnJvckNvZGUuSU5WQUxJRF9LRVksIGBOYW1lc3BhY2Uga2V5cyBjYW4gbm90IGNvbnRhaW4gdGhlIGNoYXJhY3RlciBcIi5cIiBhcyB0aGlzIGlzIHVzZWQgdG8gZXhwcmVzcyBuZXN0aW5nLiBQbGVhc2UgcmVtb3ZlIGl0IG9yIHJlcGxhY2UgaXQgd2l0aCBhbm90aGVyIGNoYXJhY3Rlci5cblxuSW52YWxpZCAke2ludmFsaWRLZXlMYWJlbHMubGVuZ3RoID09PSAxID8gJ2tleScgOiAna2V5cyd9OiAke2ludmFsaWRLZXlMYWJlbHMuam9pbignLCAnKX1cblxuSWYgeW91J3JlIG1pZ3JhdGluZyBmcm9tIGEgZmxhdCBzdHJ1Y3R1cmUsIHlvdSBjYW4gY29udmVydCB5b3VyIG1lc3NhZ2VzIGFzIGZvbGxvd3M6XG5cbmltcG9ydCB7c2V0fSBmcm9tIFwibG9kYXNoXCI7XG5cbmNvbnN0IGlucHV0ID0ge1xuICBcIm9uZS5vbmVcIjogXCIxLjFcIixcbiAgXCJvbmUudHdvXCI6IFwiMS4yXCIsXG4gIFwidHdvLm9uZS5vbmVcIjogXCIyLjEuMVwiXG59O1xuXG5jb25zdCBvdXRwdXQgPSBPYmplY3QuZW50cmllcyhpbnB1dCkucmVkdWNlKFxuICAoYWNjLCBba2V5LCB2YWx1ZV0pID0+IHNldChhY2MsIGtleSwgdmFsdWUpLFxuICB7fVxuKTtcblxuLy8gT3V0cHV0OlxuLy9cbi8vIHtcbi8vICAgXCJvbmVcIjoge1xuLy8gICAgIFwib25lXCI6IFwiMS4xXCIsXG4vLyAgICAgXCJ0d29cIjogXCIxLjJcIlxuLy8gICB9LFxuLy8gICBcInR3b1wiOiB7XG4vLyAgICAgXCJvbmVcIjoge1xuLy8gICAgICAgXCJvbmVcIjogXCIyLjEuMVwiXG4vLyAgICAgfVxuLy8gICB9XG4vLyB9XG5gICkpO1xuICB9XG59XG5cbi8qKlxuICogRW5oYW5jZXMgdGhlIGluY29taW5nIHByb3BzIHdpdGggZGVmYXVsdHMuXG4gKi9cbmZ1bmN0aW9uIGluaXRpYWxpemVDb25maWcoe1xuICBmb3JtYXRzLFxuICBnZXRNZXNzYWdlRmFsbGJhY2ssXG4gIG1lc3NhZ2VzLFxuICBvbkVycm9yLFxuICAuLi5yZXN0XG59KSB7XG4gIGNvbnN0IGZpbmFsT25FcnJvciA9IG9uRXJyb3IgfHwgZGVmYXVsdE9uRXJyb3I7XG4gIGNvbnN0IGZpbmFsR2V0TWVzc2FnZUZhbGxiYWNrID0gZ2V0TWVzc2FnZUZhbGxiYWNrIHx8IGRlZmF1bHRHZXRNZXNzYWdlRmFsbGJhY2s7XG4gIHtcbiAgICBpZiAobWVzc2FnZXMpIHtcbiAgICAgIHZhbGlkYXRlTWVzc2FnZXMobWVzc2FnZXMsIGZpbmFsT25FcnJvcik7XG4gICAgfVxuICB9XG4gIHJldHVybiB7XG4gICAgLi4ucmVzdCxcbiAgICBmb3JtYXRzOiBmb3JtYXRzIHx8IHVuZGVmaW5lZCxcbiAgICBtZXNzYWdlczogbWVzc2FnZXMgfHwgdW5kZWZpbmVkLFxuICAgIG9uRXJyb3I6IGZpbmFsT25FcnJvcixcbiAgICBnZXRNZXNzYWdlRmFsbGJhY2s6IGZpbmFsR2V0TWVzc2FnZUZhbGxiYWNrXG4gIH07XG59XG5cbmV4cG9ydCB7IEludGxFcnJvciBhcyBJLCBJbnRsRXJyb3JDb2RlIGFzIGEsIGNyZWF0ZUludGxGb3JtYXR0ZXJzIGFzIGIsIGNyZWF0ZUZvcm1hdHRlciBhcyBjLCBjcmVhdGVDYWNoZSBhcyBkLCBjcmVhdGVCYXNlVHJhbnNsYXRvciBhcyBlLCBkZWZhdWx0R2V0TWVzc2FnZUZhbGxiYWNrIGFzIGYsIGRlZmF1bHRPbkVycm9yIGFzIGcsIGluaXRpYWxpemVDb25maWcgYXMgaSwgcmVzb2x2ZU5hbWVzcGFjZSBhcyByIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/use-intl/dist/esm/development/react.js":
/*!*************************************************************!*\
  !*** ./node_modules/use-intl/dist/esm/development/react.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IntlProvider: () => (/* binding */ IntlProvider),\n/* harmony export */   useFormatter: () => (/* binding */ useFormatter),\n/* harmony export */   useLocale: () => (/* binding */ useLocale),\n/* harmony export */   useMessages: () => (/* binding */ useMessages),\n/* harmony export */   useNow: () => (/* binding */ useNow),\n/* harmony export */   useTimeZone: () => (/* binding */ useTimeZone),\n/* harmony export */   useTranslations: () => (/* binding */ useTranslations)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./initializeConfig-CRD6euuK.js */ \"(app-pages-browser)/./node_modules/use-intl/dist/esm/development/initializeConfig-CRD6euuK.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n\n\n\n\n\n\nconst IntlContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\n\nfunction IntlProvider({\n  children,\n  formats,\n  getMessageFallback,\n  locale,\n  messages,\n  now,\n  onError,\n  timeZone\n}) {\n  const prevContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(IntlContext);\n\n  // The formatter cache is released when the locale changes. For\n  // long-running apps with a persistent `IntlProvider` at the root,\n  // this can reduce the memory footprint (e.g. in React Native).\n  const cache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    return prevContext?.cache || (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.d)();\n  }, [locale, prevContext?.cache]);\n  const formatters = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => prevContext?.formatters || (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.b)(cache), [cache, prevContext?.formatters]);\n\n  // Memoizing this value helps to avoid triggering a re-render of all\n  // context consumers in case the configuration didn't change. However,\n  // if some of the non-primitive values change, a re-render will still\n  // be triggered. Note that there's no need to put `memo` on `IntlProvider`\n  // itself, because the `children` typically change on every render.\n  // There's some burden on the consumer side if it's important to reduce\n  // re-renders, put that's how React works.\n  // See: https://blog.isquaredsoftware.com/2020/05/blogged-answers-a-mostly-complete-guide-to-react-rendering-behavior/#context-updates-and-render-optimizations\n  const value = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    ...(0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.i)({\n      locale,\n      // (required by provider)\n      formats: formats === undefined ? prevContext?.formats : formats,\n      getMessageFallback: getMessageFallback || prevContext?.getMessageFallback,\n      messages: messages === undefined ? prevContext?.messages : messages,\n      now: now || prevContext?.now,\n      onError: onError || prevContext?.onError,\n      timeZone: timeZone || prevContext?.timeZone\n    }),\n    formatters,\n    cache\n  }), [cache, formats, formatters, getMessageFallback, locale, messages, now, onError, prevContext, timeZone]);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(IntlContext.Provider, {\n    value: value,\n    children: children\n  });\n}\n\nfunction useIntlContext() {\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(IntlContext);\n  if (!context) {\n    throw new Error('No intl context found. Have you configured the provider? See https://next-intl.dev/docs/usage/configuration#server-client-components' );\n  }\n  return context;\n}\n\nlet hasWarnedForMissingTimezone = false;\nconst isServer = typeof window === 'undefined';\nfunction useTranslationsImpl(allMessagesPrefixed, namespacePrefixed, namespacePrefix) {\n  const {\n    cache,\n    formats: globalFormats,\n    formatters,\n    getMessageFallback,\n    locale,\n    onError,\n    timeZone\n  } = useIntlContext();\n\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the hook invocation.\n  const allMessages = allMessagesPrefixed[namespacePrefix];\n  const namespace = (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.r)(namespacePrefixed, namespacePrefix);\n  if (!timeZone && !hasWarnedForMissingTimezone && isServer) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    hasWarnedForMissingTimezone = true;\n    onError(new _initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.I(_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.a.ENVIRONMENT_FALLBACK, `There is no \\`timeZone\\` configured, this can lead to markup mismatches caused by environment differences. Consider adding a global default: https://next-intl.dev/docs/configuration#time-zone` ));\n  }\n  const translate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.e)({\n    cache,\n    formatters,\n    getMessageFallback,\n    messages: allMessages,\n    namespace,\n    onError,\n    formats: globalFormats,\n    locale,\n    timeZone\n  }), [cache, formatters, getMessageFallback, allMessages, namespace, onError, globalFormats, locale, timeZone]);\n  return translate;\n}\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction useTranslations(namespace) {\n  const context = useIntlContext();\n  const messages = context.messages;\n\n  // We have to wrap the actual hook so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  // @ts-expect-error Use the explicit annotation instead\n  return useTranslationsImpl({\n    '!': messages\n  },\n  // @ts-expect-error\n  namespace ? `!.${namespace}` : '!', '!');\n}\n\nfunction useLocale() {\n  return useIntlContext().locale;\n}\n\nfunction getNow() {\n  return new Date();\n}\n\n/**\n * @see https://next-intl.dev/docs/usage/dates-times#relative-times-usenow\n */\nfunction useNow(options) {\n  const updateInterval = options?.updateInterval;\n  const {\n    now: globalNow\n  } = useIntlContext();\n  const [now, setNow] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(globalNow || getNow());\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!updateInterval) return;\n    const intervalId = setInterval(() => {\n      setNow(getNow());\n    }, updateInterval);\n    return () => {\n      clearInterval(intervalId);\n    };\n  }, [globalNow, updateInterval]);\n  return updateInterval == null && globalNow ? globalNow : now;\n}\n\nfunction useTimeZone() {\n  return useIntlContext().timeZone;\n}\n\nfunction useMessages() {\n  const context = useIntlContext();\n  if (!context.messages) {\n    throw new Error('No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages' );\n  }\n  return context.messages;\n}\n\nfunction useFormatter() {\n  const {\n    formats,\n    formatters,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone\n  } = useIntlContext();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => (0,_initializeConfig_CRD6euuK_js__WEBPACK_IMPORTED_MODULE_2__.c)({\n    formats,\n    locale,\n    now: globalNow,\n    onError,\n    timeZone,\n    _formatters: formatters\n  }), [formats, formatters, globalNow, locale, onError, timeZone]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/use-intl/dist/esm/development/react.js\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fwood%2Fworkspace%2Faitools%2Faitools-website%2Fnode_modules%2Fnext-intl%2Fdist%2Fesm%2Fdevelopment%2Fshared%2FNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);