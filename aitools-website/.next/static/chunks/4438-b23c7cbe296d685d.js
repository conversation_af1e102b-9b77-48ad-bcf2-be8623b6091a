"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4438],{2388:(e,t,l)=>{l.d(t,{N_:()=>c,a8:()=>i,rd:()=>o});var a=l(9984),s=l(981);let r=(0,a.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:c,redirect:n,usePathname:i,useRouter:o}=(0,s.A)(r)},2731:(e,t,l)=>{l.d(t,{A:()=>s});var a=l(5155);function s(e){let{size:t="md",className:l=""}=e;return(0,a.jsx)("div",{className:"flex justify-center items-center ".concat(l),children:(0,a.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},2765:(e,t,l)=>{l.d(t,{J2:()=>c,OD:()=>r,VP:()=>n});var a=l(7652);let s=[{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"}];function r(){return(function(){let e=(0,a.c3)("categories");return s.map(t=>({slug:t.slug,name:e("category_names.".concat(t.slug)),description:e("category_descriptions.".concat(t.slug)),icon:t.icon,color:t.color}))})().map(e=>({value:e.slug,label:e.name}))}function c(){let e=(0,a.c3)("categories"),t=r();return[{value:"",label:e("all_categories")},...t]}function n(e){return(0,a.c3)("categories")("category_names.".concat(e))||e}async function i(e){let t=await getTranslations({locale:e,namespace:"categories"});return s.map(e=>({slug:e.slug,name:t("category_names.".concat(e.slug)),description:t("category_descriptions.".concat(e.slug)),icon:e.icon,color:e.color}))}s.map(e=>e.slug),s.reduce((e,t)=>(e[t.slug]=t,e),{})},3467:(e,t,l)=>{l.d(t,{$g:()=>d,Ef:()=>i,Y$:()=>n,kX:()=>a,mV:()=>o,tF:()=>u,v4:()=>c,vS:()=>s});let a={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},s=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],r={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},c=[{value:"",label:"所有价格"},{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],n=[{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],i=e=>{switch(e){case r.FREE.value:return r.FREE.color;case r.FREEMIUM.value:return r.FREEMIUM.color;case r.PAID.value:return r.PAID.color;default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case r.FREE.value:return r.FREE.label;case r.FREEMIUM.value:return r.FREEMIUM.label;case r.PAID.value:return r.PAID.label;default:return e}},d=e=>0===e?"免费":"\xa5".concat(e),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},5131:(e,t,l)=>{l.d(t,{A:()=>u});var a=l(5155),s=l(2115),r=l(2388),c=l(7652),n=l(4416),i=l(7924),o=l(3332),d=l(8146);function u(e){let{selectedTags:t,onTagsChange:l,maxTags:u=d.z}=e,[m,g]=(0,s.useState)(""),[x,h]=(0,s.useState)(!1),b=(0,r.a8)(),p=(0,c.c3)("common");null==b||b.startsWith("/en");let y=e=>{t.includes(e)?l(t.filter(t=>t!==e)):t.length<u&&l([...t,e])},f=e=>{l(t.filter(t=>t!==e))},v=d.MV.filter(e=>e.toLowerCase().includes(m.toLowerCase())&&!t.includes(e));return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:p("select_tags")}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:p("selected_count",{count:t.length,max:u})})]}),t.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:p("selected_tags")}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.map(e=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[e,(0,a.jsx)("button",{type:"button",onClick:()=>f(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,a.jsx)(n.A,{className:"h-3 w-3"})})]},e))})]}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:p("select_tags_max",{max:u})}),(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)("input",{type:"text",placeholder:p("search_tags"),value:m,onChange:e=>g(e.target.value),onFocus:()=>h(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)(i.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(x||m)&&(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:v.length>0?(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 gap-1",children:v.map(e=>{let l=t.length>=u;return(0,a.jsx)("button",{type:"button",onClick:()=>{y(e),g(""),h(!1)},disabled:l,className:"\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ".concat(l?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700","\n                            "),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-3 w-3 mr-2 text-gray-400"}),e]})},e)})}),v.length>50&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:p("found_tags",{count:v.length})})]}):(0,a.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:p(m?"no_tags_found":"start_typing")})})})]})}),(x||m)&&(0,a.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{h(!1),g("")}}),t.length>=u&&(0,a.jsx)("p",{className:"text-sm text-amber-600",children:p("max_tags_limit",{max:u})})]})}},6063:(e,t,l)=>{l.d(t,{A:()=>d});var a=l(5155),s=l(2115),r=l(2108),c=l(2388),n=l(7652),i=l(3385),o=l(9911);function d(e){let{isOpen:t,onClose:l}=e,[d,u]=(0,s.useState)("method"),[m,g]=(0,s.useState)(""),[x,h]=(0,s.useState)(""),[b,p]=(0,s.useState)(!1),[y,f]=(0,s.useState)("");(0,c.a8)();let v=(0,n.c3)("auth");(0,i.Ym)();let j=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",l=document.createElement("div");l.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===t?"bg-green-500":"bg-red-500"),l.textContent=e,document.body.appendChild(l),setTimeout(()=>document.body.removeChild(l),3e3)},N=()=>{u("method"),g(""),h(""),f(""),l()},E=async e=>{try{p(!0),await (0,r.signIn)(e,{callbackUrl:"/"})}catch(e){j(v("login_failed"),"error")}finally{p(!1)}},_=async()=>{if(!m)return void f(v("email_required"));if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(m))return void f(v("email_invalid"));f(""),p(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:m})}),t=await e.json();t.success?(h(t.token),u("code"),j(v("verification_sent"))):j(t.error||v("send_failed"),"error")}catch(e){j(v("network_error"),"error")}finally{p(!1)}},w=async e=>{if(6===e.length){p(!0);try{let t=await (0,r.signIn)("email-code",{email:m,code:e,token:x,redirect:!1});(null==t?void 0:t.ok)?(j(v("login_success")),N()):j((null==t?void 0:t.error)||v("verification_error"),"error")}catch(e){j(v("network_error"),"error")}finally{p(!1)}}},C=(e,t)=>{if(t.length>1)return;let l=document.querySelectorAll(".code-input");if(l[e].value=t,t&&e<5){var a;null==(a=l[e+1])||a.focus()}let s=Array.from(l).map(e=>e.value).join("");6===s.length&&w(s)};return t?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:N}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===d&&v("login_title"),"email"===d&&v("email_login_title"),"code"===d&&v("verification_title")]}),(0,a.jsx)("button",{onClick:N,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(o.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:["method"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:v("choose_method")}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-red-300 rounded-lg text-red-700 hover:bg-red-50 transition-colors disabled:opacity-50",onClick:()=>E("google"),disabled:b,children:[(0,a.jsx)(o.DSS,{}),v("google_login")]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50",onClick:()=>E("github"),disabled:b,children:[(0,a.jsx)(o.hL4,{}),v("github_login")]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:v("or")})})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors",onClick:()=>u("email"),children:[(0,a.jsx)(o.maD,{}),v("email_login")]})]}),"email"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:v("email_instruction")}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:v("email_address")}),(0,a.jsx)("input",{type:"email",value:m,onChange:e=>g(e.target.value),placeholder:v("email_placeholder"),onKeyPress:e=>"Enter"===e.key&&_(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),y&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:y})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:_,disabled:b,children:b?v("sending"):v("send_code")}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>u("method"),children:v("back")})]})]}),"code"===d&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:v("verification_instruction",{email:m})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:v("verification_code")}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,a.jsx)("input",{type:"text",maxLength:1,onChange:t=>C(e,t.target.value),disabled:b,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>u("email"),children:v("resend_code")}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>u("method"),children:v("back")})]})]})]})]})]}):null}},8146:(e,t,l)=>{l.d(t,{MV:()=>a,z:()=>s});let a=["AI助手","ChatGPT","对话AI","智能问答","语言模型","写作助手","内容生成","文案创作","博客写作","营销文案","图像生成","图像编辑","AI绘画","头像生成","背景移除","视频生成","视频编辑","视频剪辑","短视频制作","视频字幕","语音合成","语音识别","音乐生成","语音转文字","文字转语音","代码生成","代码补全","代码审查","开发助手","低代码平台","数据分析","数据可视化","商业智能","机器学习","深度学习","办公自动化","文档处理","项目管理","团队协作","笔记工具","UI设计","Logo设计","网页设计","平面设计","原型设计","SEO优化","社交媒体营销","邮件营销","内容营销","市场分析","机器翻译","实时翻译","文档翻译","语音翻译"],s=3}}]);