"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7797],{2388:(e,t,l)=>{l.d(t,{N_:()=>r,a8:()=>o,rd:()=>c});var a=l(9984),s=l(981);let i=(0,a.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:r,redirect:n,usePathname:o,useRouter:c}=(0,s.A)(i)},3467:(e,t,l)=>{l.d(t,{$g:()=>d,Ef:()=>o,Y$:()=>n,kX:()=>a,mV:()=>c,tF:()=>u,v4:()=>r,vS:()=>s});let a={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},s=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],i={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},r=[{value:"",label:"所有价格"},{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],n=[{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],o=e=>{switch(e){case i.FREE.value:return i.FREE.color;case i.FREEMIUM.value:return i.FREEMIUM.color;case i.PAID.value:return i.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case i.FREE.value:return i.FREE.label;case i.FREEMIUM.value:return i.FREEMIUM.label;case i.PAID.value:return i.PAID.label;default:return e}},d=e=>0===e?"免费":"\xa5".concat(e),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4601:(e,t,l)=>{l.d(t,{A:()=>d});var a=l(5155),s=l(2115),i=l(2388),r=l(7652),n=l(2108),o=l(9911),c=l(6214);function d(e){let{toolId:t,initialLikes:l=0,initialLiked:d=!1,onLoginRequired:u,onUnlike:m,isInLikedPage:x=!1,showCount:h=!0,size:f="md"}=e,{data:b}=(0,n.useSession)(),{getToolState:g,initializeToolState:p,toggleLike:v}=(0,c.X)(),E=(0,i.a8)(),y=(0,r.c3)("common");null==E||E.startsWith("/en");let N=g(t);(0,s.useEffect)(()=>{p(t,l,d)},[t,l,d]);let k=async()=>{if(!b){null==u||u();return}if(N.loading)return;let e=N.liked;await v(t,x)&&x&&e&&m&&m(t)},j=(()=>{switch(f){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,a.jsxs)("button",{onClick:k,disabled:N.loading,className:"\n        ".concat(j.button,"\n        inline-flex items-center space-x-1\n        ").concat(N.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500","\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      "),title:y(N.liked?"unlike":"like"),children:[N.loading?(0,a.jsx)("div",{className:"".concat(j.icon," animate-spin rounded-full border-2 border-gray-300 border-t-red-500")}):N.liked?(0,a.jsx)(o.Mbv,{className:j.icon}):(0,a.jsx)(o.sOK,{className:j.icon}),h&&(0,a.jsx)("span",{className:"".concat(j.text," font-medium"),children:N.likes})]})}},6214:(e,t,l)=>{l.d(t,{LikeProvider:()=>o,X:()=>c});var a=l(5155),s=l(2115),i=l(2108);let r={liked:!1,likes:0,loading:!1},n=(0,s.createContext)(null);function o(e){let{children:t}=e,{data:l}=(0,i.useSession)(),[o,c]=(0,s.useState)({}),d=(0,s.useCallback)(e=>o[e]||r,[o]),u=(0,s.useCallback)(function(e,t){let l=arguments.length>2&&void 0!==arguments[2]&&arguments[2];c(a=>a[e]?a:{...a,[e]:{liked:l,likes:t,loading:!1}})},[]),m=(0,s.useCallback)(async e=>{if(l)try{let t=await fetch("/api/tools/".concat(e,"/like"));if(t.ok){let l=await t.json();l.success&&c(t=>({...t,[e]:{liked:l.data.liked,likes:l.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[l]),x=(0,s.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!l)return!1;c(t=>({...t,[e]:{...t[e]||r,loading:!0}}));try{let l=await fetch("/api/tools/".concat(e,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t?{forceUnlike:!0}:{})});if(l.ok){let t=await l.json();if(t.success)return c(l=>({...l,[e]:{liked:t.data.liked,likes:t.data.likes,loading:!1}})),!0}return c(t=>({...t,[e]:{...t[e]||r,loading:!1}})),!1}catch(t){return console.error("Like request failed:",t),c(t=>({...t,[e]:{...t[e]||r,loading:!1}})),!1}},[l]);return(0,s.useEffect)(()=>{l?Object.keys(o).forEach(e=>{m(e)}):c(e=>{let t={};return Object.keys(e).forEach(l=>{t[l]={...e[l],liked:!1,loading:!1}}),t})},[l]),(0,a.jsx)(n.Provider,{value:{toolStates:o,toggleLike:x,getToolState:d,initializeToolState:u,refreshToolState:m},children:t})}function c(){let e=(0,s.useContext)(n);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},7797:(e,t,l)=>{l.d(t,{default:()=>g});var a=l(5155),s=l(2115),i=l(2388),r=l(5695),n=l(7652),o=l(3786),c=l(2657),d=l(1976),u=l(4601),m=l(6766);function x(e){let{src:t,alt:l,width:i,height:r,className:n="",priority:o=!1,fill:c=!1,sizes:d,placeholder:u="empty",blurDataURL:x,fallbackSrc:h="/images/placeholder.svg",onError:f}=e,[b,g]=(0,s.useState)(t),[p,v]=(0,s.useState)(!0),[E,y]=(0,s.useState)(!1),N={src:b,alt:l,className:"".concat(n," ").concat(p?"opacity-0":"opacity-100"," transition-opacity duration-300"),onError:()=>{y(!0),v(!1),g(h),null==f||f()},onLoad:()=>{v(!1)},priority:o,placeholder:"blur"===u?"blur":"empty",blurDataURL:x||("blur"===u?function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,l=document.createElement("canvas");l.width=e,l.height=t;let a=l.getContext("2d");return a&&(a.fillStyle="#f3f4f6",a.fillRect(0,0,e,t)),l.toDataURL()}():void 0),sizes:d||(c?"100vw":void 0)};return c?(0,a.jsxs)("div",{className:"relative overflow-hidden",children:[(0,a.jsx)(m.default,{...N,fill:!0,style:{objectFit:"cover"}}),p&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"})]}):(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.default,{...N,width:i,height:r}),p&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse",style:{width:i,height:r}})]})}let h={toolLogo:{width:64,height:64}},f={toolLogo:"64px"};var b=l(3467);let g=e=>{let{tool:t,onLoginRequired:l,onUnlike:s,isInLikedPage:m=!1}=e,g=(0,r.useParams)(),p=(null==g?void 0:g.locale)||"zh",v=(0,n.c3)("common");return(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[t.logo?(0,a.jsx)(x,{src:t.logo,alt:"".concat(t.name," logo"),width:h.toolLogo.width,height:h.toolLogo.height,className:"rounded-lg object-cover",sizes:f.toolLogo,placeholder:"blur"}):(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:t.name.charAt(0).toUpperCase()})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:t.name}),(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((0,b.Ef)(t.pricing)),children:(0,b.mV)(t.pricing)})]})]}),(0,a.jsx)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,a.jsx)(o.A,{className:"h-5 w-5"})})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:t.description}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[t.tags.slice(0,3).map((e,t)=>(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},t)),t.tags.length>3&&(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",t.tags.length-3]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t.views})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:t.likes})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{toolId:t._id,initialLikes:t.likes,initialLiked:m,onLoginRequired:l,onUnlike:s,isInLikedPage:m}),(0,a.jsx)(i.N_,{href:"/".concat(p,"/tools/").concat(t._id),className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:v("view_details")})]})]})]})})}}}]);