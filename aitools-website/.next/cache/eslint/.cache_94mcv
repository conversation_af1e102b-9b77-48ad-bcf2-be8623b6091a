[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/about/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/BackButton.tsx": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/contact/page.tsx": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/page.tsx": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/privacy/page.tsx": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/page.tsx": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit-launch-date/[toolId]/page.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/page.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/terms/page.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/page.tsx": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts": "54", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "55", "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx": "56", "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts": "57", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "58", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "59", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx": "60", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "61", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "62", "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx": "63", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "64", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "65", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "66", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "67", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx": "68", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx": "69", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx": "70", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Footer.tsx": "71", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "72", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx": "73", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx": "74", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx": "75", "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/LikedToolsClient.tsx": "76", "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/SubmittedToolsList.tsx": "77", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "78", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "79", "/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx": "80", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx": "81", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx": "82", "/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitForm.tsx": "83", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "84", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "85", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx": "86", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx": "87", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx": "88", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx": "89", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories-i18n.ts": "90", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts": "91", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts": "92", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "93", "/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx": "94", "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts": "95", "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/request.ts": "96", "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/routing.ts": "97", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api-messages.ts": "98", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "99", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "100", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts": "101", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "102", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "103", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts": "104", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts": "105", "/Users/<USER>/workspace/aitools/aitools-website/src/middleware.ts": "106", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "107", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "108", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts": "109", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "110", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "111", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "112"}, {"size": 8982, "mtime": 1751080369147, "results": "113", "hashOfConfig": "114"}, {"size": 12973, "mtime": 1751216809007, "results": "115", "hashOfConfig": "114"}, {"size": 17434, "mtime": 1751216783637, "results": "116", "hashOfConfig": "114"}, {"size": 6022, "mtime": 1751216761592, "results": "117", "hashOfConfig": "114"}, {"size": 515, "mtime": 1751216207594, "results": "118", "hashOfConfig": "114"}, {"size": 11242, "mtime": 1751191337661, "results": "119", "hashOfConfig": "114"}, {"size": 8412, "mtime": 1751182074559, "results": "120", "hashOfConfig": "114"}, {"size": 7395, "mtime": 1751181316662, "results": "121", "hashOfConfig": "114"}, {"size": 11513, "mtime": 1751080448998, "results": "122", "hashOfConfig": "114"}, {"size": 13683, "mtime": 1751217289644, "results": "123", "hashOfConfig": "114"}, {"size": 5798, "mtime": 1751214976702, "results": "124", "hashOfConfig": "114"}, {"size": 15680, "mtime": 1751217133257, "results": "125", "hashOfConfig": "114"}, {"size": 8081, "mtime": 1751216739280, "results": "126", "hashOfConfig": "114"}, {"size": 10428, "mtime": 1751174621877, "results": "127", "hashOfConfig": "114"}, {"size": 2140, "mtime": 1751216235923, "results": "128", "hashOfConfig": "114"}, {"size": 10571, "mtime": 1751216222080, "results": "129", "hashOfConfig": "114"}, {"size": 349, "mtime": 1751179731860, "results": "130", "hashOfConfig": "114"}, {"size": 2799, "mtime": 1751127821278, "results": "131", "hashOfConfig": "114"}, {"size": 21012, "mtime": 1751216524695, "results": "132", "hashOfConfig": "114"}, {"size": 22788, "mtime": 1751217260901, "results": "133", "hashOfConfig": "114"}, {"size": 6797, "mtime": 1751216672531, "results": "134", "hashOfConfig": "114"}, {"size": 1575, "mtime": 1751216564783, "results": "135", "hashOfConfig": "114"}, {"size": 3505, "mtime": 1751216596119, "results": "136", "hashOfConfig": "114"}, {"size": 950, "mtime": 1751178436052, "results": "137", "hashOfConfig": "114"}, {"size": 12464, "mtime": 1751216641723, "results": "138", "hashOfConfig": "114"}, {"size": 11961, "mtime": 1751080591727, "results": "139", "hashOfConfig": "114"}, {"size": 4543, "mtime": 1750930937103, "results": "140", "hashOfConfig": "114"}, {"size": 6972, "mtime": 1751018762448, "results": "141", "hashOfConfig": "114"}, {"size": 3885, "mtime": 1751018851458, "results": "142", "hashOfConfig": "114"}, {"size": 5752, "mtime": 1751217219404, "results": "143", "hashOfConfig": "114"}, {"size": 5831, "mtime": 1751171869040, "results": "144", "hashOfConfig": "114"}, {"size": 5321, "mtime": 1750906802986, "results": "145", "hashOfConfig": "114"}, {"size": 2206, "mtime": 1751160497677, "results": "146", "hashOfConfig": "114"}, {"size": 2381, "mtime": 1751160563812, "results": "147", "hashOfConfig": "114"}, {"size": 3265, "mtime": 1751163869055, "results": "148", "hashOfConfig": "114"}, {"size": 171, "mtime": 1750921851894, "results": "149", "hashOfConfig": "114"}, {"size": 3714, "mtime": 1750921931408, "results": "150", "hashOfConfig": "114"}, {"size": 4683, "mtime": 1751160718688, "results": "151", "hashOfConfig": "114"}, {"size": 2289, "mtime": 1751181379570, "results": "152", "hashOfConfig": "114"}, {"size": 4267, "mtime": 1751161594662, "results": "153", "hashOfConfig": "114"}, {"size": 2553, "mtime": 1751161424205, "results": "154", "hashOfConfig": "114"}, {"size": 4419, "mtime": 1751160947066, "results": "155", "hashOfConfig": "114"}, {"size": 5242, "mtime": 1751013821668, "results": "156", "hashOfConfig": "114"}, {"size": 1022, "mtime": 1750984456438, "results": "157", "hashOfConfig": "114"}, {"size": 4812, "mtime": 1751162755345, "results": "158", "hashOfConfig": "114"}, {"size": 12910, "mtime": 1751197326048, "results": "159", "hashOfConfig": "114"}, {"size": 4227, "mtime": 1751162633491, "results": "160", "hashOfConfig": "114"}, {"size": 8858, "mtime": 1751199328528, "results": "161", "hashOfConfig": "114"}, {"size": 3803, "mtime": 1751161706933, "results": "162", "hashOfConfig": "114"}, {"size": 5368, "mtime": 1751160424453, "results": "163", "hashOfConfig": "114"}, {"size": 6078, "mtime": 1751161088375, "results": "164", "hashOfConfig": "114"}, {"size": 2794, "mtime": 1751164176243, "results": "165", "hashOfConfig": "114"}, {"size": 2491, "mtime": 1751160639981, "results": "166", "hashOfConfig": "114"}, {"size": 3444, "mtime": 1751161223047, "results": "167", "hashOfConfig": "114"}, {"size": 431, "mtime": 1751165597653, "results": "168", "hashOfConfig": "114"}, {"size": 245, "mtime": 1751244021142, "results": "169", "hashOfConfig": "114"}, {"size": 2921, "mtime": 1751181616429, "results": "170", "hashOfConfig": "114"}, {"size": 1552, "mtime": 1751216288085, "results": "171", "hashOfConfig": "114"}, {"size": 845, "mtime": 1750908285683, "results": "172", "hashOfConfig": "114"}, {"size": 7729, "mtime": 1751167113024, "results": "173", "hashOfConfig": "114"}, {"size": 77, "mtime": 1751171640781, "results": "174", "hashOfConfig": "114"}, {"size": 505, "mtime": 1750908273441, "results": "175", "hashOfConfig": "114"}, {"size": 3837, "mtime": 1751216892942, "results": "176", "hashOfConfig": "114"}, {"size": 863, "mtime": 1750908296528, "results": "177", "hashOfConfig": "114"}, {"size": 6109, "mtime": 1751216870398, "results": "178", "hashOfConfig": "114"}, {"size": 4874, "mtime": 1751217017959, "results": "179", "hashOfConfig": "114"}, {"size": 9251, "mtime": 1751216490067, "results": "180", "hashOfConfig": "114"}, {"size": 6887, "mtime": 1751243760925, "results": "181", "hashOfConfig": "114"}, {"size": 4645, "mtime": 1751243916016, "results": "182", "hashOfConfig": "114"}, {"size": 11332, "mtime": 1751243986103, "results": "183", "hashOfConfig": "114"}, {"size": 2993, "mtime": 1751243822873, "results": "184", "hashOfConfig": "114"}, {"size": 2451, "mtime": 1751243869428, "results": "185", "hashOfConfig": "114"}, {"size": 1680, "mtime": 1751216156055, "results": "186", "hashOfConfig": "114"}, {"size": 1554, "mtime": 1751217104534, "results": "187", "hashOfConfig": "114"}, {"size": 1240, "mtime": 1751243730311, "results": "188", "hashOfConfig": "114"}, {"size": 7499, "mtime": 1751217176907, "results": "189", "hashOfConfig": "114"}, {"size": 23334, "mtime": 1751216831566, "results": "190", "hashOfConfig": "114"}, {"size": 867, "mtime": 1750922283437, "results": "191", "hashOfConfig": "114"}, {"size": 362, "mtime": 1750922147686, "results": "192", "hashOfConfig": "114"}, {"size": 8948, "mtime": 1751216415293, "results": "193", "hashOfConfig": "114"}, {"size": 5275, "mtime": 1751023043127, "results": "194", "hashOfConfig": "114"}, {"size": 2605, "mtime": 1751019665417, "results": "195", "hashOfConfig": "114"}, {"size": 14693, "mtime": 1751216370126, "results": "196", "hashOfConfig": "114"}, {"size": 9319, "mtime": 1751216453727, "results": "197", "hashOfConfig": "114"}, {"size": 3366, "mtime": 1751216477033, "results": "198", "hashOfConfig": "114"}, {"size": 10116, "mtime": 1751217323841, "results": "199", "hashOfConfig": "114"}, {"size": 8869, "mtime": 1751216466888, "results": "200", "hashOfConfig": "114"}, {"size": 5433, "mtime": 1751023279983, "results": "201", "hashOfConfig": "114"}, {"size": 3048, "mtime": 1751023262678, "results": "202", "hashOfConfig": "114"}, {"size": 5155, "mtime": 1751181125061, "results": "203", "hashOfConfig": "114"}, {"size": 3849, "mtime": 1751072665492, "results": "204", "hashOfConfig": "114"}, {"size": 4413, "mtime": 1751019030952, "results": "205", "hashOfConfig": "114"}, {"size": 2449, "mtime": 1750942881883, "results": "206", "hashOfConfig": "114"}, {"size": 5354, "mtime": 1751086484747, "results": "207", "hashOfConfig": "114"}, {"size": 432, "mtime": 1751216104505, "results": "208", "hashOfConfig": "114"}, {"size": 410, "mtime": 1751211931429, "results": "209", "hashOfConfig": "114"}, {"size": 757, "mtime": 1751216119022, "results": "210", "hashOfConfig": "114"}, {"size": 10379, "mtime": 1751198770845, "results": "211", "hashOfConfig": "114"}, {"size": 7104, "mtime": 1751198770899, "results": "212", "hashOfConfig": "114"}, {"size": 5474, "mtime": 1751122879189, "results": "213", "hashOfConfig": "114"}, {"size": 3457, "mtime": 1751122835025, "results": "214", "hashOfConfig": "114"}, {"size": 921, "mtime": 1750903252798, "results": "215", "hashOfConfig": "114"}, {"size": 5018, "mtime": 1751186011040, "results": "216", "hashOfConfig": "114"}, {"size": 3274, "mtime": 1751081422931, "results": "217", "hashOfConfig": "114"}, {"size": 3985, "mtime": 1751017840303, "results": "218", "hashOfConfig": "114"}, {"size": 490, "mtime": 1751216137026, "results": "219", "hashOfConfig": "114"}, {"size": 1667, "mtime": 1750903308052, "results": "220", "hashOfConfig": "114"}, {"size": 2141, "mtime": 1750921803605, "results": "221", "hashOfConfig": "114"}, {"size": 3989, "mtime": 1750984256539, "results": "222", "hashOfConfig": "114"}, {"size": 4859, "mtime": 1751181597006, "results": "223", "hashOfConfig": "114"}, {"size": 3406, "mtime": 1751090710634, "results": "224", "hashOfConfig": "114"}, {"size": 720, "mtime": 1750903327281, "results": "225", "hashOfConfig": "114"}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ifueu7", {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 18, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 16, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 15, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 21, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/about/page.tsx", ["562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx", ["577", "578", "579", "580", "581", "582", "583", "584", "585", "586"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx", ["587", "588", "589", "590", "591", "592", "593"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx", ["594", "595", "596"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/BackButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx", ["597", "598", "599", "600"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx", ["601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615", "616", "617", "618"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx", ["619"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/contact/page.tsx", ["620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633", "634", "635"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx", ["636"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/page.tsx", ["637"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx", ["638", "639", "640", "641", "642"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/privacy/page.tsx", ["643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx", ["658"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/page.tsx", ["659", "660", "661"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx", ["662"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx", ["663", "664", "665", "666", "667", "668"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx", ["669"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit-launch-date/[toolId]/page.tsx", ["670", "671"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/page.tsx", ["672", "673", "674"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/terms/page.tsx", ["675", "676", "677", "678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx", ["696", "697", "698", "699"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx", ["700"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx", ["701"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/page.tsx", ["702"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["703", "704"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["705", "706"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts", ["707"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts", ["708"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts", ["709", "710"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["711", "712", "713", "714", "715"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["716", "717", "718", "719"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["720"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts", ["721", "722", "723", "724"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx", ["725", "726"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", ["727"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["728", "729", "730", "731", "732"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx", ["733", "734", "735"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageClient.tsx", ["736", "737"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageClient.tsx", ["738", "739"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx", ["740"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/LikedToolsClient.tsx", ["741"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/SubmittedToolsList.tsx", ["742", "743", "744", "745"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx", ["746", "747", "748", "749", "750", "751", "752", "753", "754", "755"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx", ["756", "757", "758"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitForm.tsx", ["759", "760"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["761", "762", "763", "764"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", ["765", "766"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx", ["767", "768", "769", "770", "771"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx", ["772"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx", ["773"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx", ["774", "775", "776"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories-i18n.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx", ["777"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/request.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/routing.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api-messages.ts", ["778"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["779"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["780", "781", "782", "783", "784"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts", ["785"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/middleware.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["786"], [], {"ruleId": "787", "severity": 2, "message": "788", "line": 89, "column": 15, "nodeType": "789", "endLine": 92, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 89, "column": 15, "nodeType": "789", "endLine": 92, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 89, "column": 15, "nodeType": "789", "endLine": 92, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 89, "column": 15, "nodeType": "789", "endLine": 92, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 89, "column": 15, "nodeType": "789", "endLine": 92, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 89, "column": 15, "nodeType": "789", "endLine": 92, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 89, "column": 15, "nodeType": "789", "endLine": 92, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 89, "column": 15, "nodeType": "789", "endLine": 92, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "790", "line": 200, "column": 13, "nodeType": "789", "endLine": 203, "endColumn": 14}, {"ruleId": "787", "severity": 2, "message": "790", "line": 200, "column": 13, "nodeType": "789", "endLine": 203, "endColumn": 14}, {"ruleId": "787", "severity": 2, "message": "790", "line": 200, "column": 13, "nodeType": "789", "endLine": 203, "endColumn": 14}, {"ruleId": "787", "severity": 2, "message": "790", "line": 200, "column": 13, "nodeType": "789", "endLine": 203, "endColumn": 14}, {"ruleId": "787", "severity": 2, "message": "790", "line": 200, "column": 13, "nodeType": "789", "endLine": 203, "endColumn": 14}, {"ruleId": "787", "severity": 2, "message": "790", "line": 200, "column": 13, "nodeType": "789", "endLine": 203, "endColumn": 14}, {"ruleId": "787", "severity": 2, "message": "790", "line": 200, "column": 13, "nodeType": "789", "endLine": 203, "endColumn": 14}, {"ruleId": "791", "severity": 2, "message": "792", "line": 13, "column": 3, "nodeType": null, "messageId": "793", "endLine": 13, "endColumn": 8}, {"ruleId": "791", "severity": 2, "message": "794", "line": 18, "column": 3, "nodeType": null, "messageId": "793", "endLine": 18, "endColumn": 8}, {"ruleId": "791", "severity": 2, "message": "795", "line": 19, "column": 3, "nodeType": null, "messageId": "793", "endLine": 19, "endColumn": 11}, {"ruleId": "791", "severity": 2, "message": "796", "line": 22, "column": 3, "nodeType": null, "messageId": "793", "endLine": 22, "endColumn": 7}, {"ruleId": "791", "severity": 2, "message": "797", "line": 28, "column": 9, "nodeType": null, "messageId": "793", "endLine": 28, "endColumn": 17}, {"ruleId": "798", "severity": 1, "message": "799", "line": 40, "column": 6, "nodeType": "800", "endLine": 40, "endColumn": 17, "suggestions": "801"}, {"ruleId": "791", "severity": 2, "message": "802", "line": 54, "column": 14, "nodeType": null, "messageId": "793", "endLine": 54, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "803", "line": 61, "column": 9, "nodeType": null, "messageId": "793", "endLine": 61, "endColumn": 19}, {"ruleId": "791", "severity": 2, "message": "804", "line": 70, "column": 9, "nodeType": null, "messageId": "793", "endLine": 70, "endColumn": 24}, {"ruleId": "791", "severity": 2, "message": "805", "line": 83, "column": 9, "nodeType": null, "messageId": "793", "endLine": 83, "endColumn": 27}, {"ruleId": "791", "severity": 2, "message": "806", "line": 16, "column": 3, "nodeType": null, "messageId": "793", "endLine": 16, "endColumn": 6}, {"ruleId": "791", "severity": 2, "message": "797", "line": 29, "column": 9, "nodeType": null, "messageId": "793", "endLine": 29, "endColumn": 17}, {"ruleId": "798", "severity": 1, "message": "807", "line": 48, "column": 6, "nodeType": "800", "endLine": 48, "endColumn": 20, "suggestions": "808"}, {"ruleId": "791", "severity": 2, "message": "802", "line": 65, "column": 14, "nodeType": null, "messageId": "793", "endLine": 65, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "802", "line": 98, "column": 14, "nodeType": null, "messageId": "793", "endLine": 98, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "802", "line": 124, "column": 14, "nodeType": null, "messageId": "793", "endLine": 124, "endColumn": 17}, {"ruleId": "809", "severity": 1, "message": "810", "line": 288, "column": 27, "nodeType": "789", "endLine": 292, "endColumn": 29}, {"ruleId": "791", "severity": 2, "message": "811", "line": 22, "column": 3, "nodeType": null, "messageId": "793", "endLine": 22, "endColumn": 9}, {"ruleId": "791", "severity": 2, "message": "802", "line": 59, "column": 14, "nodeType": null, "messageId": "793", "endLine": 59, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "802", "line": 98, "column": 14, "nodeType": null, "messageId": "793", "endLine": 98, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "812", "line": 4, "column": 21, "nodeType": null, "messageId": "793", "endLine": 4, "endColumn": 25}, {"ruleId": "791", "severity": 2, "message": "813", "line": 9, "column": 3, "nodeType": null, "messageId": "793", "endLine": 9, "endColumn": 12}, {"ruleId": "809", "severity": 1, "message": "810", "line": 148, "column": 13, "nodeType": "789", "endLine": 152, "endColumn": 15}, {"ruleId": "809", "severity": 1, "message": "810", "line": 228, "column": 19, "nodeType": "789", "endLine": 233, "endColumn": 21}, {"ruleId": "791", "severity": 2, "message": "814", "line": 7, "column": 29, "nodeType": null, "messageId": "793", "endLine": 7, "endColumn": 44}, {"ruleId": "791", "severity": 2, "message": "815", "line": 9, "column": 10, "nodeType": null, "messageId": "793", "endLine": 9, "endColumn": 19}, {"ruleId": "791", "severity": 2, "message": "816", "line": 111, "column": 12, "nodeType": null, "messageId": "793", "endLine": 111, "endColumn": 17}, {"ruleId": "787", "severity": 2, "message": "788", "line": 228, "column": 15, "nodeType": "789", "endLine": 231, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 228, "column": 15, "nodeType": "789", "endLine": 231, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 228, "column": 15, "nodeType": "789", "endLine": 231, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 228, "column": 15, "nodeType": "789", "endLine": 231, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 228, "column": 15, "nodeType": "789", "endLine": 231, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 228, "column": 15, "nodeType": "789", "endLine": 231, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 228, "column": 15, "nodeType": "789", "endLine": 231, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 228, "column": 15, "nodeType": "789", "endLine": 231, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "817", "line": 240, "column": 17, "nodeType": "789", "endLine": 243, "endColumn": 18}, {"ruleId": "787", "severity": 2, "message": "817", "line": 240, "column": 17, "nodeType": "789", "endLine": 243, "endColumn": 18}, {"ruleId": "787", "severity": 2, "message": "817", "line": 240, "column": 17, "nodeType": "789", "endLine": 243, "endColumn": 18}, {"ruleId": "787", "severity": 2, "message": "817", "line": 240, "column": 17, "nodeType": "789", "endLine": 243, "endColumn": 18}, {"ruleId": "787", "severity": 2, "message": "817", "line": 240, "column": 17, "nodeType": "789", "endLine": 243, "endColumn": 18}, {"ruleId": "787", "severity": 2, "message": "817", "line": 240, "column": 17, "nodeType": "789", "endLine": 243, "endColumn": 18}, {"ruleId": "787", "severity": 2, "message": "817", "line": 240, "column": 17, "nodeType": "789", "endLine": 243, "endColumn": 18}, {"ruleId": "791", "severity": 2, "message": "816", "line": 65, "column": 12, "nodeType": null, "messageId": "793", "endLine": 65, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "818", "line": 5, "column": 38, "nodeType": null, "messageId": "793", "endLine": 5, "endColumn": 44}, {"ruleId": "787", "severity": 2, "message": "788", "line": 99, "column": 15, "nodeType": "789", "endLine": 102, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 99, "column": 15, "nodeType": "789", "endLine": 102, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 99, "column": 15, "nodeType": "789", "endLine": 102, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 99, "column": 15, "nodeType": "789", "endLine": 102, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 99, "column": 15, "nodeType": "789", "endLine": 102, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 99, "column": 15, "nodeType": "789", "endLine": 102, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 99, "column": 15, "nodeType": "789", "endLine": 102, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 99, "column": 15, "nodeType": "789", "endLine": 102, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "819", "line": 174, "column": 19, "nodeType": "789", "endLine": 174, "endColumn": 80}, {"ruleId": "787", "severity": 2, "message": "819", "line": 174, "column": 19, "nodeType": "789", "endLine": 174, "endColumn": 80}, {"ruleId": "787", "severity": 2, "message": "819", "line": 174, "column": 19, "nodeType": "789", "endLine": 174, "endColumn": 80}, {"ruleId": "787", "severity": 2, "message": "819", "line": 174, "column": 19, "nodeType": "789", "endLine": 174, "endColumn": 80}, {"ruleId": "787", "severity": 2, "message": "819", "line": 174, "column": 19, "nodeType": "789", "endLine": 174, "endColumn": 80}, {"ruleId": "787", "severity": 2, "message": "819", "line": 174, "column": 19, "nodeType": "789", "endLine": 174, "endColumn": 80}, {"ruleId": "787", "severity": 2, "message": "819", "line": 174, "column": 19, "nodeType": "789", "endLine": 174, "endColumn": 80}, {"ruleId": "791", "severity": 2, "message": "802", "line": 95, "column": 14, "nodeType": null, "messageId": "793", "endLine": 95, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "816", "line": 81, "column": 12, "nodeType": null, "messageId": "793", "endLine": 81, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "820", "line": 20, "column": 17, "nodeType": null, "messageId": "793", "endLine": 20, "endColumn": 24}, {"ruleId": "821", "severity": 2, "message": "822", "line": 21, "column": 38, "nodeType": "823", "messageId": "824", "endLine": 21, "endColumn": 41, "suggestions": "825"}, {"ruleId": "798", "severity": 1, "message": "826", "line": 37, "column": 6, "nodeType": "800", "endLine": 37, "endColumn": 23, "suggestions": "827"}, {"ruleId": "791", "severity": 2, "message": "802", "line": 63, "column": 14, "nodeType": null, "messageId": "793", "endLine": 63, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "802", "line": 87, "column": 14, "nodeType": null, "messageId": "793", "endLine": 87, "endColumn": 17}, {"ruleId": "787", "severity": 2, "message": "788", "line": 65, "column": 15, "nodeType": "789", "endLine": 68, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 65, "column": 15, "nodeType": "789", "endLine": 68, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 65, "column": 15, "nodeType": "789", "endLine": 68, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 65, "column": 15, "nodeType": "789", "endLine": 68, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 65, "column": 15, "nodeType": "789", "endLine": 68, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 65, "column": 15, "nodeType": "789", "endLine": 68, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 65, "column": 15, "nodeType": "789", "endLine": 68, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 65, "column": 15, "nodeType": "789", "endLine": 68, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "790", "line": 224, "column": 22, "nodeType": "789", "endLine": 224, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 224, "column": 22, "nodeType": "789", "endLine": 224, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 224, "column": 22, "nodeType": "789", "endLine": 224, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 224, "column": 22, "nodeType": "789", "endLine": 224, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 224, "column": 22, "nodeType": "789", "endLine": 224, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 224, "column": 22, "nodeType": "789", "endLine": 224, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 224, "column": 22, "nodeType": "789", "endLine": 224, "endColumn": 41}, {"ruleId": "821", "severity": 2, "message": "822", "line": 57, "column": 37, "nodeType": "823", "messageId": "824", "endLine": 57, "endColumn": 40, "suggestions": "828"}, {"ruleId": "791", "severity": 2, "message": "829", "line": 11, "column": 3, "nodeType": null, "messageId": "793", "endLine": 11, "endColumn": 7}, {"ruleId": "791", "severity": 2, "message": "830", "line": 18, "column": 3, "nodeType": null, "messageId": "793", "endLine": 18, "endColumn": 7}, {"ruleId": "809", "severity": 1, "message": "810", "line": 96, "column": 19, "nodeType": "789", "endLine": 100, "endColumn": 21}, {"ruleId": "791", "severity": 2, "message": "831", "line": 6, "column": 9, "nodeType": null, "messageId": "793", "endLine": 6, "endColumn": 10}, {"ruleId": "791", "severity": 2, "message": "832", "line": 12, "column": 3, "nodeType": null, "messageId": "793", "endLine": 12, "endColumn": 7}, {"ruleId": "791", "severity": 2, "message": "806", "line": 15, "column": 3, "nodeType": null, "messageId": "793", "endLine": 15, "endColumn": 6}, {"ruleId": "791", "severity": 2, "message": "802", "line": 94, "column": 14, "nodeType": null, "messageId": "793", "endLine": 94, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "802", "line": 111, "column": 14, "nodeType": null, "messageId": "793", "endLine": 111, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "802", "line": 128, "column": 14, "nodeType": null, "messageId": "793", "endLine": 128, "endColumn": 17}, {"ruleId": "809", "severity": 1, "message": "810", "line": 197, "column": 23, "nodeType": "789", "endLine": 201, "endColumn": 25}, {"ruleId": "809", "severity": 1, "message": "810", "line": 476, "column": 19, "nodeType": "789", "endLine": 480, "endColumn": 21}, {"ruleId": "821", "severity": 2, "message": "822", "line": 17, "column": 36, "nodeType": "823", "messageId": "824", "endLine": 17, "endColumn": 39, "suggestions": "833"}, {"ruleId": "798", "severity": 1, "message": "834", "line": 33, "column": 6, "nodeType": "800", "endLine": 33, "endColumn": 22, "suggestions": "835"}, {"ruleId": "791", "severity": 2, "message": "820", "line": 13, "column": 17, "nodeType": null, "messageId": "793", "endLine": 13, "endColumn": 24}, {"ruleId": "821", "severity": 2, "message": "822", "line": 14, "column": 36, "nodeType": "823", "messageId": "824", "endLine": 14, "endColumn": 39, "suggestions": "836"}, {"ruleId": "798", "severity": 1, "message": "834", "line": 29, "column": 6, "nodeType": "800", "endLine": 29, "endColumn": 22, "suggestions": "837"}, {"ruleId": "787", "severity": 2, "message": "788", "line": 66, "column": 15, "nodeType": "789", "endLine": 69, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 66, "column": 15, "nodeType": "789", "endLine": 69, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 66, "column": 15, "nodeType": "789", "endLine": 69, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 66, "column": 15, "nodeType": "789", "endLine": 69, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 66, "column": 15, "nodeType": "789", "endLine": 69, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 66, "column": 15, "nodeType": "789", "endLine": 69, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 66, "column": 15, "nodeType": "789", "endLine": 69, "endColumn": 16}, {"ruleId": "787", "severity": 2, "message": "788", "line": 66, "column": 15, "nodeType": "789", "endLine": 69, "endColumn": 16}, {"ruleId": "838", "severity": 2, "message": "839", "line": 127, "column": 25, "nodeType": "840", "messageId": "841", "suggestions": "842"}, {"ruleId": "838", "severity": 2, "message": "839", "line": 127, "column": 28, "nodeType": "840", "messageId": "841", "suggestions": "843"}, {"ruleId": "838", "severity": 2, "message": "839", "line": 127, "column": 30, "nodeType": "840", "messageId": "841", "suggestions": "844"}, {"ruleId": "838", "severity": 2, "message": "839", "line": 127, "column": 34, "nodeType": "840", "messageId": "841", "suggestions": "845"}, {"ruleId": "838", "severity": 2, "message": "839", "line": 219, "column": 23, "nodeType": "840", "messageId": "841", "suggestions": "846"}, {"ruleId": "838", "severity": 2, "message": "839", "line": 219, "column": 26, "nodeType": "840", "messageId": "841", "suggestions": "847"}, {"ruleId": "787", "severity": 2, "message": "790", "line": 274, "column": 22, "nodeType": "789", "endLine": 274, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 274, "column": 22, "nodeType": "789", "endLine": 274, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 274, "column": 22, "nodeType": "789", "endLine": 274, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 274, "column": 22, "nodeType": "789", "endLine": 274, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 274, "column": 22, "nodeType": "789", "endLine": 274, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 274, "column": 22, "nodeType": "789", "endLine": 274, "endColumn": 41}, {"ruleId": "787", "severity": 2, "message": "790", "line": 274, "column": 22, "nodeType": "789", "endLine": 274, "endColumn": 41}, {"ruleId": "791", "severity": 2, "message": "816", "line": 27, "column": 14, "nodeType": null, "messageId": "793", "endLine": 27, "endColumn": 19}, {"ruleId": "791", "severity": 2, "message": "816", "line": 46, "column": 14, "nodeType": null, "messageId": "793", "endLine": 46, "endColumn": 19}, {"ruleId": "821", "severity": 2, "message": "822", "line": 63, "column": 42, "nodeType": "823", "messageId": "824", "endLine": 63, "endColumn": 45, "suggestions": "848"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 64, "column": 42, "nodeType": "823", "messageId": "824", "endLine": 64, "endColumn": 45, "suggestions": "849"}, {"ruleId": "791", "severity": 2, "message": "816", "line": 81, "column": 14, "nodeType": null, "messageId": "793", "endLine": 81, "endColumn": 19}, {"ruleId": "791", "severity": 2, "message": "816", "line": 73, "column": 12, "nodeType": null, "messageId": "793", "endLine": 73, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "816", "line": 66, "column": 12, "nodeType": null, "messageId": "793", "endLine": 66, "endColumn": 17}, {"ruleId": "821", "severity": 2, "message": "822", "line": 20, "column": 18, "nodeType": "823", "messageId": "824", "endLine": 20, "endColumn": 21, "suggestions": "850"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 56, "column": 22, "nodeType": "823", "messageId": "824", "endLine": 56, "endColumn": 25, "suggestions": "851"}, {"ruleId": "791", "severity": 2, "message": "852", "line": 8, "column": 27, "nodeType": null, "messageId": "793", "endLine": 8, "endColumn": 34}, {"ruleId": "821", "severity": 2, "message": "822", "line": 96, "column": 23, "nodeType": "823", "messageId": "824", "endLine": 96, "endColumn": 26, "suggestions": "853"}, {"ruleId": "791", "severity": 2, "message": "854", "line": 30, "column": 13, "nodeType": null, "messageId": "793", "endLine": 30, "endColumn": 26}, {"ruleId": "791", "severity": 2, "message": "812", "line": 5, "column": 8, "nodeType": null, "messageId": "793", "endLine": 5, "endColumn": 12}, {"ruleId": "791", "severity": 2, "message": "855", "line": 92, "column": 11, "nodeType": null, "messageId": "793", "endLine": 92, "endColumn": 14}, {"ruleId": "821", "severity": 2, "message": "822", "line": 162, "column": 25, "nodeType": "823", "messageId": "824", "endLine": 162, "endColumn": 28, "suggestions": "856"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 184, "column": 20, "nodeType": "823", "messageId": "824", "endLine": 184, "endColumn": 23, "suggestions": "857"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 216, "column": 28, "nodeType": "823", "messageId": "824", "endLine": 216, "endColumn": 31, "suggestions": "858"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 244, "column": 19, "nodeType": "823", "messageId": "824", "endLine": 244, "endColumn": 22, "suggestions": "859"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 245, "column": 56, "nodeType": "823", "messageId": "824", "endLine": 245, "endColumn": 59, "suggestions": "860"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 245, "column": 79, "nodeType": "823", "messageId": "824", "endLine": 245, "endColumn": 82, "suggestions": "861"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 23, "column": 18, "nodeType": "823", "messageId": "824", "endLine": 23, "endColumn": 21, "suggestions": "862"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 67, "column": 22, "nodeType": "823", "messageId": "824", "endLine": 67, "endColumn": 25, "suggestions": "863"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 167, "column": 19, "nodeType": "823", "messageId": "824", "endLine": 167, "endColumn": 22, "suggestions": "864"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 172, "column": 70, "nodeType": "823", "messageId": "824", "endLine": 172, "endColumn": 73, "suggestions": "865"}, {"ruleId": "791", "severity": 2, "message": "816", "line": 59, "column": 14, "nodeType": null, "messageId": "793", "endLine": 59, "endColumn": 19}, {"ruleId": "821", "severity": 2, "message": "822", "line": 41, "column": 18, "nodeType": "823", "messageId": "824", "endLine": 41, "endColumn": 21, "suggestions": "866"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 54, "column": 22, "nodeType": "823", "messageId": "824", "endLine": 54, "endColumn": 25, "suggestions": "867"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 91, "column": 52, "nodeType": "823", "messageId": "824", "endLine": 91, "endColumn": 55, "suggestions": "868"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 92, "column": 52, "nodeType": "823", "messageId": "824", "endLine": 92, "endColumn": 55, "suggestions": "869"}, {"ruleId": "791", "severity": 2, "message": "870", "line": 31, "column": 9, "nodeType": null, "messageId": "793", "endLine": 31, "endColumn": 22}, {"ruleId": "791", "severity": 2, "message": "802", "line": 64, "column": 14, "nodeType": null, "messageId": "793", "endLine": 64, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "870", "line": 28, "column": 9, "nodeType": null, "messageId": "793", "endLine": 28, "endColumn": 22}, {"ruleId": "791", "severity": 2, "message": "797", "line": 24, "column": 9, "nodeType": null, "messageId": "793", "endLine": 24, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "871", "line": 26, "column": 9, "nodeType": null, "messageId": "793", "endLine": 26, "endColumn": 15}, {"ruleId": "791", "severity": 2, "message": "816", "line": 51, "column": 14, "nodeType": null, "messageId": "793", "endLine": 51, "endColumn": 19}, {"ruleId": "791", "severity": 2, "message": "816", "line": 91, "column": 14, "nodeType": null, "messageId": "793", "endLine": 91, "endColumn": 19}, {"ruleId": "791", "severity": 2, "message": "816", "line": 118, "column": 14, "nodeType": null, "messageId": "793", "endLine": 118, "endColumn": 19}, {"ruleId": "791", "severity": 2, "message": "871", "line": 18, "column": 9, "nodeType": null, "messageId": "793", "endLine": 18, "endColumn": 15}, {"ruleId": "809", "severity": 1, "message": "810", "line": 66, "column": 13, "nodeType": "789", "endLine": 70, "endColumn": 15}, {"ruleId": "809", "severity": 1, "message": "810", "line": 98, "column": 21, "nodeType": "789", "endLine": 102, "endColumn": 23}, {"ruleId": "791", "severity": 2, "message": "797", "line": 29, "column": 9, "nodeType": null, "messageId": "793", "endLine": 29, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "871", "line": 31, "column": 9, "nodeType": null, "messageId": "793", "endLine": 31, "endColumn": 15}, {"ruleId": "791", "severity": 2, "message": "797", "line": 38, "column": 9, "nodeType": null, "messageId": "793", "endLine": 38, "endColumn": 17}, {"ruleId": "791", "severity": 2, "message": "871", "line": 40, "column": 9, "nodeType": null, "messageId": "793", "endLine": 40, "endColumn": 15}, {"ruleId": "791", "severity": 2, "message": "811", "line": 12, "column": 44, "nodeType": null, "messageId": "793", "endLine": 12, "endColumn": 50}, {"ruleId": "798", "severity": 1, "message": "872", "line": 41, "column": 6, "nodeType": "800", "endLine": 41, "endColumn": 49, "suggestions": "873"}, {"ruleId": "874", "severity": 2, "message": "875", "line": 25, "column": 11, "nodeType": "876", "messageId": "877", "endLine": 25, "endColumn": 34, "suggestions": "878"}, {"ruleId": "798", "severity": 1, "message": "807", "line": 76, "column": 6, "nodeType": "800", "endLine": 76, "endColumn": 22, "suggestions": "879"}, {"ruleId": "791", "severity": 2, "message": "816", "line": 91, "column": 14, "nodeType": null, "messageId": "793", "endLine": 91, "endColumn": 19}, {"ruleId": "791", "severity": 2, "message": "816", "line": 117, "column": 14, "nodeType": null, "messageId": "793", "endLine": 117, "endColumn": 19}, {"ruleId": "791", "severity": 2, "message": "812", "line": 8, "column": 10, "nodeType": null, "messageId": "793", "endLine": 8, "endColumn": 14}, {"ruleId": "791", "severity": 2, "message": "880", "line": 9, "column": 18, "nodeType": null, "messageId": "793", "endLine": 9, "endColumn": 24}, {"ruleId": "791", "severity": 2, "message": "881", "line": 9, "column": 26, "nodeType": null, "messageId": "793", "endLine": 9, "endColumn": 30}, {"ruleId": "791", "severity": 2, "message": "882", "line": 9, "column": 32, "nodeType": null, "messageId": "793", "endLine": 9, "endColumn": 36}, {"ruleId": "791", "severity": 2, "message": "883", "line": 9, "column": 38, "nodeType": null, "messageId": "793", "endLine": 9, "endColumn": 45}, {"ruleId": "791", "severity": 2, "message": "884", "line": 9, "column": 47, "nodeType": null, "messageId": "793", "endLine": 9, "endColumn": 55}, {"ruleId": "791", "severity": 2, "message": "811", "line": 29, "column": 3, "nodeType": null, "messageId": "793", "endLine": 29, "endColumn": 9}, {"ruleId": "791", "severity": 2, "message": "885", "line": 38, "column": 28, "nodeType": null, "messageId": "793", "endLine": 38, "endColumn": 47}, {"ruleId": "791", "severity": 2, "message": "886", "line": 39, "column": 18, "nodeType": null, "messageId": "793", "endLine": 39, "endColumn": 27}, {"ruleId": "791", "severity": 2, "message": "887", "line": 40, "column": 20, "nodeType": null, "messageId": "793", "endLine": 40, "endColumn": 31}, {"ruleId": "821", "severity": 2, "message": "822", "line": 50, "column": 33, "nodeType": "823", "messageId": "824", "endLine": 50, "endColumn": 36, "suggestions": "888"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 63, "column": 33, "nodeType": "823", "messageId": "824", "endLine": 63, "endColumn": 36, "suggestions": "889"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 185, "column": 22, "nodeType": "823", "messageId": "824", "endLine": 185, "endColumn": 25, "suggestions": "890"}, {"ruleId": "874", "severity": 2, "message": "875", "line": 21, "column": 11, "nodeType": "876", "messageId": "877", "endLine": 21, "endColumn": 26, "suggestions": "891"}, {"ruleId": "809", "severity": 1, "message": "810", "line": 276, "column": 23, "nodeType": "789", "endLine": 280, "endColumn": 25}, {"ruleId": "791", "severity": 2, "message": "892", "line": 7, "column": 27, "nodeType": null, "messageId": "793", "endLine": 7, "endColumn": 34}, {"ruleId": "791", "severity": 2, "message": "893", "line": 7, "column": 36, "nodeType": null, "messageId": "793", "endLine": 7, "endColumn": 46}, {"ruleId": "798", "severity": 1, "message": "894", "line": 64, "column": 6, "nodeType": "800", "endLine": 64, "endColumn": 14, "suggestions": "895"}, {"ruleId": "809", "severity": 1, "message": "810", "line": 165, "column": 13, "nodeType": "789", "endLine": 169, "endColumn": 15}, {"ruleId": "791", "severity": 2, "message": "870", "line": 39, "column": 9, "nodeType": null, "messageId": "793", "endLine": 39, "endColumn": 22}, {"ruleId": "798", "severity": 1, "message": "896", "line": 47, "column": 6, "nodeType": "800", "endLine": 47, "endColumn": 42, "suggestions": "897"}, {"ruleId": "791", "severity": 2, "message": "898", "line": 28, "column": 16, "nodeType": null, "messageId": "793", "endLine": 28, "endColumn": 23}, {"ruleId": "798", "severity": 1, "message": "899", "line": 40, "column": 6, "nodeType": "800", "endLine": 40, "endColumn": 21, "suggestions": "900"}, {"ruleId": "791", "severity": 2, "message": "802", "line": 55, "column": 14, "nodeType": null, "messageId": "793", "endLine": 55, "endColumn": 17}, {"ruleId": "809", "severity": 1, "message": "810", "line": 70, "column": 19, "nodeType": "789", "endLine": 74, "endColumn": 21}, {"ruleId": "809", "severity": 1, "message": "810", "line": 199, "column": 27, "nodeType": "789", "endLine": 203, "endColumn": 29}, {"ruleId": "791", "severity": 2, "message": "870", "line": 40, "column": 9, "nodeType": null, "messageId": "793", "endLine": 40, "endColumn": 22}, {"ruleId": "809", "severity": 1, "message": "810", "line": 180, "column": 7, "nodeType": "789", "endLine": 189, "endColumn": 9}, {"ruleId": "791", "severity": 2, "message": "901", "line": 37, "column": 10, "nodeType": null, "messageId": "793", "endLine": 37, "endColumn": 18}, {"ruleId": "902", "severity": 1, "message": "903", "line": 78, "column": 9, "nodeType": "789", "endLine": 82, "endColumn": 11}, {"ruleId": "902", "severity": 1, "message": "903", "line": 92, "column": 7, "nodeType": "789", "endLine": 96, "endColumn": 9}, {"ruleId": "798", "severity": 1, "message": "904", "line": 176, "column": 6, "nodeType": "800", "endLine": 176, "endColumn": 15, "suggestions": "905"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 279, "column": 16, "nodeType": "823", "messageId": "824", "endLine": 279, "endColumn": 19, "suggestions": "906"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 6, "column": 34, "nodeType": "823", "messageId": "824", "endLine": 6, "endColumn": 37, "suggestions": "907"}, {"ruleId": "791", "severity": 2, "message": "908", "line": 7, "column": 10, "nodeType": null, "messageId": "793", "endLine": 7, "endColumn": 24}, {"ruleId": "821", "severity": 2, "message": "822", "line": 75, "column": 60, "nodeType": "823", "messageId": "824", "endLine": 75, "endColumn": 63, "suggestions": "909"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 155, "column": 31, "nodeType": "823", "messageId": "824", "endLine": 155, "endColumn": 34, "suggestions": "910"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 161, "column": 26, "nodeType": "823", "messageId": "824", "endLine": 161, "endColumn": 29, "suggestions": "911"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 162, "column": 26, "nodeType": "823", "messageId": "824", "endLine": 162, "endColumn": 29, "suggestions": "912"}, {"ruleId": "821", "severity": 2, "message": "822", "line": 85, "column": 35, "nodeType": "823", "messageId": "824", "endLine": 85, "endColumn": 38, "suggestions": "913"}, {"ruleId": "791", "severity": 2, "message": "914", "line": 1, "column": 8, "nodeType": null, "messageId": "793", "endLine": 1, "endColumn": 16}, "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", "Do not use an `<a>` element to navigate to `/contact/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'pathname' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["915"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'Eye' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["916"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "'locale' is defined but never used.", "'Tool' is defined but never used.", "'ArrowLeft' is defined but never used.", "'isValidCategory' is defined but never used.", "'getLocale' is defined but never used.", "'error' is defined but never used.", "Do not use an `<a>` element to navigate to `/categories/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "'MapPin' is defined but never used.", "Do not use an `<a>` element to navigate to `/faq/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "'session' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["917", "918"], "React Hook useEffect has missing dependencies: 'fetchOrderInfo' and 'router'. Either include them or remove the dependency array.", ["919"], ["920", "921"], "'User' is defined but never used.", "'Edit' is defined but never used.", "'t' is assigned a value but never used.", "'Mail' is defined but never used.", ["922", "923"], "React Hook useEffect has missing dependencies: 'fetchToolInfo' and 'router'. Either include them or remove the dependency array.", ["924"], ["925", "926"], ["927"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["928", "929", "930", "931"], ["932", "933", "934", "935"], ["936", "937", "938", "939"], ["940", "941", "942", "943"], ["944", "945", "946", "947"], ["948", "949", "950", "951"], ["952", "953"], ["954", "955"], ["956", "957"], ["958", "959"], "'request' is defined but never used.", ["960", "961"], "'paymentMethod' is assigned a value but never used.", "'now' is assigned a value but never used.", ["962", "963"], ["964", "965"], ["966", "967"], ["968", "969"], ["970", "971"], ["972", "973"], ["974", "975"], ["976", "977"], ["978", "979"], ["980", "981"], ["982", "983"], ["984", "985"], ["986", "987"], ["988", "989"], "'currentLocale' is assigned a value but never used.", "'locale' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["990"], "@typescript-eslint/no-empty-object-type", "An empty interface declaration allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowInterfaces' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "Identifier", "noEmptyInterface", ["991", "992"], ["993"], "'Filter' is defined but never used.", "'Grid' is defined but never used.", "'List' is defined but never used.", "'SortAsc' is defined but never used.", "'SortDesc' is defined but never used.", "'setSelectedCategory' is assigned a value but never used.", "'setSortBy' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", ["994", "995"], ["996", "997"], ["998", "999"], ["1000", "1001"], "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["1002"], "React Hook useEffect has a missing dependency: 'initializeToolState'. Either include it or remove the dependency array.", ["1003"], "'setTool' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchRelatedTools'. Either include it or remove the dependency array.", ["1004"], "'hasError' is assigned a value but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "React Hook useEffect has missing dependencies: 'refreshToolState' and 'toolStates'. Either include them or remove the dependency array.", ["1005"], ["1006", "1007"], ["1008", "1009"], "'getNextAuthUrl' is defined but never used.", ["1010", "1011"], ["1012", "1013"], ["1014", "1015"], ["1016", "1017"], ["1018", "1019"], "'mongoose' is defined but never used.", {"desc": "1020", "fix": "1021"}, {"desc": "1022", "fix": "1023"}, {"messageId": "1024", "fix": "1025", "desc": "1026"}, {"messageId": "1027", "fix": "1028", "desc": "1029"}, {"desc": "1030", "fix": "1031"}, {"messageId": "1024", "fix": "1032", "desc": "1026"}, {"messageId": "1027", "fix": "1033", "desc": "1029"}, {"messageId": "1024", "fix": "1034", "desc": "1026"}, {"messageId": "1027", "fix": "1035", "desc": "1029"}, {"desc": "1036", "fix": "1037"}, {"messageId": "1024", "fix": "1038", "desc": "1026"}, {"messageId": "1027", "fix": "1039", "desc": "1029"}, {"desc": "1036", "fix": "1040"}, {"messageId": "1041", "data": "1042", "fix": "1043", "desc": "1044"}, {"messageId": "1041", "data": "1045", "fix": "1046", "desc": "1047"}, {"messageId": "1041", "data": "1048", "fix": "1049", "desc": "1050"}, {"messageId": "1041", "data": "1051", "fix": "1052", "desc": "1053"}, {"messageId": "1041", "data": "1054", "fix": "1055", "desc": "1044"}, {"messageId": "1041", "data": "1056", "fix": "1057", "desc": "1047"}, {"messageId": "1041", "data": "1058", "fix": "1059", "desc": "1050"}, {"messageId": "1041", "data": "1060", "fix": "1061", "desc": "1053"}, {"messageId": "1041", "data": "1062", "fix": "1063", "desc": "1044"}, {"messageId": "1041", "data": "1064", "fix": "1065", "desc": "1047"}, {"messageId": "1041", "data": "1066", "fix": "1067", "desc": "1050"}, {"messageId": "1041", "data": "1068", "fix": "1069", "desc": "1053"}, {"messageId": "1041", "data": "1070", "fix": "1071", "desc": "1044"}, {"messageId": "1041", "data": "1072", "fix": "1073", "desc": "1047"}, {"messageId": "1041", "data": "1074", "fix": "1075", "desc": "1050"}, {"messageId": "1041", "data": "1076", "fix": "1077", "desc": "1053"}, {"messageId": "1041", "data": "1078", "fix": "1079", "desc": "1044"}, {"messageId": "1041", "data": "1080", "fix": "1081", "desc": "1047"}, {"messageId": "1041", "data": "1082", "fix": "1083", "desc": "1050"}, {"messageId": "1041", "data": "1084", "fix": "1085", "desc": "1053"}, {"messageId": "1041", "data": "1086", "fix": "1087", "desc": "1044"}, {"messageId": "1041", "data": "1088", "fix": "1089", "desc": "1047"}, {"messageId": "1041", "data": "1090", "fix": "1091", "desc": "1050"}, {"messageId": "1041", "data": "1092", "fix": "1093", "desc": "1053"}, {"messageId": "1024", "fix": "1094", "desc": "1026"}, {"messageId": "1027", "fix": "1095", "desc": "1029"}, {"messageId": "1024", "fix": "1096", "desc": "1026"}, {"messageId": "1027", "fix": "1097", "desc": "1029"}, {"messageId": "1024", "fix": "1098", "desc": "1026"}, {"messageId": "1027", "fix": "1099", "desc": "1029"}, {"messageId": "1024", "fix": "1100", "desc": "1026"}, {"messageId": "1027", "fix": "1101", "desc": "1029"}, {"messageId": "1024", "fix": "1102", "desc": "1026"}, {"messageId": "1027", "fix": "1103", "desc": "1029"}, {"messageId": "1024", "fix": "1104", "desc": "1026"}, {"messageId": "1027", "fix": "1105", "desc": "1029"}, {"messageId": "1024", "fix": "1106", "desc": "1026"}, {"messageId": "1027", "fix": "1107", "desc": "1029"}, {"messageId": "1024", "fix": "1108", "desc": "1026"}, {"messageId": "1027", "fix": "1109", "desc": "1029"}, {"messageId": "1024", "fix": "1110", "desc": "1026"}, {"messageId": "1027", "fix": "1111", "desc": "1029"}, {"messageId": "1024", "fix": "1112", "desc": "1026"}, {"messageId": "1027", "fix": "1113", "desc": "1029"}, {"messageId": "1024", "fix": "1114", "desc": "1026"}, {"messageId": "1027", "fix": "1115", "desc": "1029"}, {"messageId": "1024", "fix": "1116", "desc": "1026"}, {"messageId": "1027", "fix": "1117", "desc": "1029"}, {"messageId": "1024", "fix": "1118", "desc": "1026"}, {"messageId": "1027", "fix": "1119", "desc": "1029"}, {"messageId": "1024", "fix": "1120", "desc": "1026"}, {"messageId": "1027", "fix": "1121", "desc": "1029"}, {"messageId": "1024", "fix": "1122", "desc": "1026"}, {"messageId": "1027", "fix": "1123", "desc": "1029"}, {"messageId": "1024", "fix": "1124", "desc": "1026"}, {"messageId": "1027", "fix": "1125", "desc": "1029"}, {"messageId": "1024", "fix": "1126", "desc": "1026"}, {"messageId": "1027", "fix": "1127", "desc": "1029"}, {"messageId": "1024", "fix": "1128", "desc": "1026"}, {"messageId": "1027", "fix": "1129", "desc": "1029"}, {"messageId": "1024", "fix": "1130", "desc": "1026"}, {"messageId": "1027", "fix": "1131", "desc": "1029"}, {"desc": "1132", "fix": "1133"}, {"messageId": "1134", "data": "1135", "fix": "1136", "desc": "1137"}, {"messageId": "1134", "data": "1138", "fix": "1139", "desc": "1140"}, {"desc": "1141", "fix": "1142"}, {"messageId": "1024", "fix": "1143", "desc": "1026"}, {"messageId": "1027", "fix": "1144", "desc": "1029"}, {"messageId": "1024", "fix": "1145", "desc": "1026"}, {"messageId": "1027", "fix": "1146", "desc": "1029"}, {"messageId": "1024", "fix": "1147", "desc": "1026"}, {"messageId": "1027", "fix": "1148", "desc": "1029"}, {"messageId": "1134", "data": "1149", "fix": "1150", "desc": "1137"}, {"messageId": "1134", "data": "1151", "fix": "1152", "desc": "1140"}, {"desc": "1153", "fix": "1154"}, {"desc": "1155", "fix": "1156"}, {"desc": "1157", "fix": "1158"}, {"desc": "1159", "fix": "1160"}, {"messageId": "1024", "fix": "1161", "desc": "1026"}, {"messageId": "1027", "fix": "1162", "desc": "1029"}, {"messageId": "1024", "fix": "1163", "desc": "1026"}, {"messageId": "1027", "fix": "1164", "desc": "1029"}, {"messageId": "1024", "fix": "1165", "desc": "1026"}, {"messageId": "1027", "fix": "1166", "desc": "1029"}, {"messageId": "1024", "fix": "1167", "desc": "1026"}, {"messageId": "1027", "fix": "1168", "desc": "1029"}, {"messageId": "1024", "fix": "1169", "desc": "1026"}, {"messageId": "1027", "fix": "1170", "desc": "1029"}, {"messageId": "1024", "fix": "1171", "desc": "1026"}, {"messageId": "1027", "fix": "1172", "desc": "1029"}, {"messageId": "1024", "fix": "1173", "desc": "1026"}, {"messageId": "1027", "fix": "1174", "desc": "1029"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "1175", "text": "1176"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "1177", "text": "1178"}, "suggestUnknown", {"range": "1179", "text": "1180"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1181", "text": "1182"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [status, orderId, router, fetchOrderInfo]", {"range": "1183", "text": "1184"}, {"range": "1185", "text": "1180"}, {"range": "1186", "text": "1182"}, {"range": "1187", "text": "1180"}, {"range": "1188", "text": "1182"}, "Update the dependencies array to be: [fetchToolInfo, router, status, toolId]", {"range": "1189", "text": "1190"}, {"range": "1191", "text": "1180"}, {"range": "1192", "text": "1182"}, {"range": "1193", "text": "1190"}, "replaceWithAlt", {"alt": "1194"}, {"range": "1195", "text": "1196"}, "Replace with `&quot;`.", {"alt": "1197"}, {"range": "1198", "text": "1199"}, "Replace with `&ldquo;`.", {"alt": "1200"}, {"range": "1201", "text": "1202"}, "Replace with `&#34;`.", {"alt": "1203"}, {"range": "1204", "text": "1205"}, "Replace with `&rdquo;`.", {"alt": "1194"}, {"range": "1206", "text": "1207"}, {"alt": "1197"}, {"range": "1208", "text": "1209"}, {"alt": "1200"}, {"range": "1210", "text": "1211"}, {"alt": "1203"}, {"range": "1212", "text": "1213"}, {"alt": "1194"}, {"range": "1214", "text": "1215"}, {"alt": "1197"}, {"range": "1216", "text": "1217"}, {"alt": "1200"}, {"range": "1218", "text": "1219"}, {"alt": "1203"}, {"range": "1220", "text": "1221"}, {"alt": "1194"}, {"range": "1222", "text": "1223"}, {"alt": "1197"}, {"range": "1224", "text": "1225"}, {"alt": "1200"}, {"range": "1226", "text": "1227"}, {"alt": "1203"}, {"range": "1228", "text": "1229"}, {"alt": "1194"}, {"range": "1230", "text": "1231"}, {"alt": "1197"}, {"range": "1232", "text": "1233"}, {"alt": "1200"}, {"range": "1234", "text": "1235"}, {"alt": "1203"}, {"range": "1236", "text": "1237"}, {"alt": "1194"}, {"range": "1238", "text": "1239"}, {"alt": "1197"}, {"range": "1240", "text": "1241"}, {"alt": "1200"}, {"range": "1242", "text": "1243"}, {"alt": "1203"}, {"range": "1244", "text": "1245"}, {"range": "1246", "text": "1180"}, {"range": "1247", "text": "1182"}, {"range": "1248", "text": "1180"}, {"range": "1249", "text": "1182"}, {"range": "1250", "text": "1180"}, {"range": "1251", "text": "1182"}, {"range": "1252", "text": "1180"}, {"range": "1253", "text": "1182"}, {"range": "1254", "text": "1180"}, {"range": "1255", "text": "1182"}, {"range": "1256", "text": "1180"}, {"range": "1257", "text": "1182"}, {"range": "1258", "text": "1180"}, {"range": "1259", "text": "1182"}, {"range": "1260", "text": "1180"}, {"range": "1261", "text": "1182"}, {"range": "1262", "text": "1180"}, {"range": "1263", "text": "1182"}, {"range": "1264", "text": "1180"}, {"range": "1265", "text": "1182"}, {"range": "1266", "text": "1180"}, {"range": "1267", "text": "1182"}, {"range": "1268", "text": "1180"}, {"range": "1269", "text": "1182"}, {"range": "1270", "text": "1180"}, {"range": "1271", "text": "1182"}, {"range": "1272", "text": "1180"}, {"range": "1273", "text": "1182"}, {"range": "1274", "text": "1180"}, {"range": "1275", "text": "1182"}, {"range": "1276", "text": "1180"}, {"range": "1277", "text": "1182"}, {"range": "1278", "text": "1180"}, {"range": "1279", "text": "1182"}, {"range": "1280", "text": "1180"}, {"range": "1281", "text": "1182"}, {"range": "1282", "text": "1180"}, {"range": "1283", "text": "1182"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "1284", "text": "1285"}, "replaceEmptyInterface", {"replacement": "1286"}, {"range": "1287", "text": "1288"}, "Replace empty interface with `object`.", {"replacement": "1180"}, {"range": "1289", "text": "1290"}, "Replace empty interface with `unknown`.", "Update the dependencies array to be: [status, router, fetchTools]", {"range": "1291", "text": "1292"}, {"range": "1293", "text": "1180"}, {"range": "1294", "text": "1182"}, {"range": "1295", "text": "1180"}, {"range": "1296", "text": "1182"}, {"range": "1297", "text": "1180"}, {"range": "1298", "text": "1182"}, {"replacement": "1286"}, {"range": "1299", "text": "1300"}, {"replacement": "1180"}, {"range": "1301", "text": "1302"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "1303", "text": "1304"}, "Update the dependencies array to be: [toolId, initialLikes, initialLiked, initializeToolState]", {"range": "1305", "text": "1306"}, "Update the dependencies array to be: [fetchRelatedTools, tool.category]", {"range": "1307", "text": "1308"}, "Update the dependencies array to be: [refreshToolState, session, toolStates]", {"range": "1309", "text": "1310"}, {"range": "1311", "text": "1180"}, {"range": "1312", "text": "1182"}, {"range": "1313", "text": "1180"}, {"range": "1314", "text": "1182"}, {"range": "1315", "text": "1180"}, {"range": "1316", "text": "1182"}, {"range": "1317", "text": "1180"}, {"range": "1318", "text": "1182"}, {"range": "1319", "text": "1180"}, {"range": "1320", "text": "1182"}, {"range": "1321", "text": "1180"}, {"range": "1322", "text": "1182"}, {"range": "1323", "text": "1180"}, {"range": "1324", "text": "1182"}, [950, 961], "[fetchStats, timeRange]", [1443, 1457], "[fetchTools, statusFilter]", [842, 845], "unknown", [842, 845], "never", [1246, 1263], "[status, orderId, router, fetchOrderInfo]", [1559, 1562], [1559, 1562], [650, 653], [650, 653], [943, 959], "[fetchToolInfo, router, status, toolId]", [544, 547], [544, 547], [892, 908], "&quot;", [4472, 4520], "AI工具导航（以下简称&quot;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&ldquo;", [4472, 4520], "AI工具导航（以下简称&ldquo;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&#34;", [4472, 4520], "AI工具导航（以下简称&#34;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", "&rdquo;", [4472, 4520], "AI工具导航（以下简称&rdquo;我们\"或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&quot;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&ldquo;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&#34;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们&rdquo;或\"本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&quot;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&ldquo;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&#34;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或&rdquo;本平台\"）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&quot;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&ldquo;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&#34;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [4472, 4520], "AI工具导航（以下简称\"我们\"或\"本平台&rdquo;）是一个专业的人工智能工具发现和推荐平台，为用户提供：", [6921, 6947], "我们的服务按&quot;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&ldquo;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&#34;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按&rdquo;现状\"提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&quot;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&ldquo;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&#34;提供，不提供任何明示或暗示的保证", [6921, 6947], "我们的服务按\"现状&rdquo;提供，不提供任何明示或暗示的保证", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [746, 749], [746, 749], [1625, 1628], [1625, 1628], [2591, 2594], [2591, 2594], [4770, 4773], [4770, 4773], [5136, 5139], [5136, 5139], [5980, 5983], [5980, 5983], [6670, 6673], [6670, 6673], [6761, 6764], [6761, 6764], [6784, 6787], [6784, 6787], [876, 879], [876, 879], [1983, 1986], [1983, 1986], [4561, 4564], [4561, 4564], [4783, 4786], [4783, 4786], [1364, 1367], [1364, 1367], [1635, 1638], [1635, 1638], [2622, 2625], [2622, 2625], [2710, 2713], [2710, 2713], [1214, 1257], "[filterTools, likedTools, searchQuery, selectedCategory]", "object", [565, 601], "type SubmittedToolsListProps = object", [565, 601], "type SubmittedToolsListProps = unknown", [1966, 1982], "[status, router, fetchTools]", [1561, 1564], [1561, 1564], [2024, 2027], [2024, 2027], [4765, 4768], [4765, 4768], [730, 798], "type SubmitFormProps = object", [730, 798], "type SubmitFormProps = unknown", [1768, 1776], "[fetchComments, toolId]", [1272, 1308], "[toolId, initialLikes, initialLiked, initializeToolState]", [1249, 1264], "[fetchRelatedTools, tool.category]", [4405, 4414], "[refreshToolState, session, toolStates]", [8390, 8393], [8390, 8393], [125, 128], [125, 128], [2315, 2318], [2315, 2318], [4647, 4650], [4647, 4650], [4803, 4806], [4803, 4806], [4862, 4865], [4862, 4865], [1724, 1727], [1724, 1727]]