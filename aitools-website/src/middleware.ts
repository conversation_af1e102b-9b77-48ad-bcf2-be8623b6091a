import { NextRequest, NextResponse } from 'next/server';

export default function middleware(request: NextRequest) {
  console.log('🔥 MIDDLEWARE CALLED:', request.nextUrl.pathname);

  const pathname = request.nextUrl.pathname;

  // Check if the pathname already has a locale prefix
  const hasLocalePrefix = pathname.startsWith('/en/') || pathname.startsWith('/zh/') || pathname === '/en' || pathname === '/zh';

  if (hasLocalePrefix) {
    console.log('✅ Has locale prefix, continuing');
    return NextResponse.next();
  }

  // For root path, detect locale based on accept-language header
  if (pathname === '/') {
    const acceptLanguage = request.headers.get('accept-language') || '';
    const isChinesePreferred = acceptLanguage.includes('zh');
    const locale = isChinesePreferred ? 'zh' : 'en';

    console.log('🏠 Root path, redirecting to:', `/${locale}`);
    const url = new URL(`/${locale}`, request.url);
    url.search = request.nextUrl.search;
    return NextResponse.redirect(url);
  }

  // For all other paths, redirect to /en prefix
  console.log('🔀 Other path, redirecting to:', `/en${pathname}`);
  const url = new URL(`/en${pathname}`, request.url);
  url.search = request.nextUrl.search;
  return NextResponse.redirect(url);
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico).*)'
  ]
};
