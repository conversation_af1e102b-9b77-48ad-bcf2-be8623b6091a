'use client';

import { Link as NextLink } from '@/i18n/routing';
import { useTranslations, useLocale } from 'next-intl';
import { type Locale } from '@/i18n/config';

export default function FooterClient() {
  const t = useTranslations('layout');
  const locale = useLocale() as Locale;

  return (
    <footer className="bg-white border-t border-gray-200 mt-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AI</span>
              </div>
              <span className="text-xl font-bold text-gray-900">
                {locale === 'zh' ? 'AI工具导航' : 'AI Tools'}
              </span>
            </div>
            <p className="text-gray-600 mb-4">
              {t('footer.description')}
            </p>
          </div>
          
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              {t('footer.quick_links')}
            </h3>
            <ul className="space-y-2">
              <li>
                <NextLink href="/tools" className="text-gray-600 hover:text-blue-600">
                  {t('footer.tools_directory')}
                </NextLink>
              </li>
              <li>
                <NextLink href="/categories" className="text-gray-600 hover:text-blue-600">
                  {t('footer.browse_categories')}
                </NextLink>
              </li>
              <li>
                <NextLink href="/submit" className="text-gray-600 hover:text-blue-600">
                  {t('footer.submit_tool')}
                </NextLink>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              {t('footer.support')}
            </h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-600 hover:text-blue-600">
                  {t('footer.help_center')}
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-blue-600">
                  {t('footer.contact_us')}
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-blue-600">
                  {t('footer.privacy_policy')}
                </a>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-200 mt-8 pt-8">
          <p className="text-center text-gray-600">
            {t('footer.copyright')}
          </p>
        </div>
      </div>
    </footer>
  );
}
