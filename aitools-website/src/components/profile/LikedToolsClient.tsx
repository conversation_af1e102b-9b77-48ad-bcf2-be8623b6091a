'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { Link } from '@/i18n/routing';
import ErrorMessage from '@/components/ErrorMessage';
import ToolCard from '@/components/ToolCard';
import { Tool } from '@/lib/api';
import { useCategoryName } from '@/constants/categories-i18n';
import { Locale } from '@/i18n/config';
import {
  Heart,
  ArrowLeft,
  Search,
  Filter
} from 'lucide-react';

// 分类名称显示组件
function CategoryName({ slug }: { slug: string }) {
  const categoryName = useCategoryName(slug);
  return <>{categoryName}</>;
}

interface LikedToolsClientProps {
  initialTools: Tool[];
}

export default function LikedToolsClient({ initialTools }: LikedToolsClientProps) {
  const params = useParams();
  const locale = params?.locale as Locale || 'zh';
  
  const [likedTools, setLikedTools] = useState<Tool[]>(initialTools);
  const [filteredTools, setFilteredTools] = useState<Tool[]>(initialTools);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // 当搜索或分类过滤条件改变时，重新过滤工具
  useEffect(() => {
    filterTools();
  }, [likedTools, searchQuery, selectedCategory]);

  const filterTools = () => {
    let filtered = likedTools;

    // 按搜索关键词过滤
    if (searchQuery) {
      filtered = filtered.filter(tool =>
        tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        tool.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // 按分类过滤
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(tool => tool.category === selectedCategory);
    }

    setFilteredTools(filtered);
  };

  const handleUnlike = async (toolId: string) => {
    try {
      // 立即从本地状态中移除工具，提供即时反馈
      setLikedTools(prev => prev.filter(tool => tool._id !== toolId));
      setError(''); // 清除之前的错误
    } catch (error) {
      console.error('Error unliking tool:', error);
      setError('取消收藏失败，请重试');
    }
  };

  // 获取所有分类
  const categories = Array.from(new Set(likedTools.map(tool => tool.category)));

  // 根据locale确定文本
  const getText = () => {
    if (locale === 'en') {
      return {
        title: 'My Favorites',
        subtitle: `Your favorite AI tools (${likedTools.length})`,
        searchPlaceholder: 'Search favorite tools...',
        allCategories: 'All Categories',
        noToolsFound: 'No matching tools found',
        noToolsYet: 'No favorite tools yet',
        adjustFilters: 'Try adjusting your search criteria or filters',
        startExploring: 'Start exploring and favorite your preferred AI tools!',
        browseTools: 'Browse Tools'
      };
    } else {
      return {
        title: '我的收藏',
        subtitle: `您收藏的AI工具 (${likedTools.length})`,
        searchPlaceholder: '搜索收藏的工具...',
        allCategories: '所有分类',
        noToolsFound: '没有找到匹配的工具',
        noToolsYet: '还没有收藏任何工具',
        adjustFilters: '尝试调整搜索条件或筛选器',
        startExploring: '开始探索并收藏您喜欢的AI工具吧！',
        browseTools: '浏览工具'
      };
    }
  };

  const text = getText();

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <div className="flex items-center mb-2">
            <Link
              href={`/${locale}/profile`}
              className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ArrowLeft className="h-5 w-5" />
            </Link>
            <h1 className="text-3xl font-bold text-gray-900">{text.title}</h1>
          </div>
          <p className="text-lg text-gray-600">{text.subtitle}</p>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder={text.searchPlaceholder}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Category Filter */}
          <div className="sm:w-48">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Filter className="h-5 w-5 text-gray-400" />
              </div>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">{text.allCategories}</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    <CategoryName slug={category} />
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <ErrorMessage
          message={error}
          onClose={() => setError('')}
          className="mb-6"
        />
      )}

      {/* Tools Grid */}
      {filteredTools.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTools.map((tool) => (
            <ToolCard
              key={tool._id}
              tool={tool}
              onUnlike={handleUnlike}
              isInLikedPage={true}
            />
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <div className="text-gray-400 mb-4">
            <Heart className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchQuery || selectedCategory !== 'all' ? text.noToolsFound : text.noToolsYet}
          </h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || selectedCategory !== 'all' 
              ? text.adjustFilters
              : text.startExploring
            }
          </p>
          {(!searchQuery && selectedCategory === 'all') && (
            <Link
              href={`/${locale}/tools`}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              {text.browseTools}
            </Link>
          )}
        </div>
      )}
    </div>
  );
}
