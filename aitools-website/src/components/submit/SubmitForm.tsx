'use client';

import React, { useState, Fragment } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import SuccessMessage from '@/components/SuccessMessage';
import LoginModal from '@/components/auth/LoginModal';
import { TOOL_PRICING_FORM_OPTIONS } from '@/constants/pricing';
import { useCategoryOptions } from '@/constants/categories-i18n';
import {
  Upload,
  Link as LinkIcon,
  Info
} from 'lucide-react';
import { MAX_TAGS_COUNT } from '@/constants/tags';
import TagSelector from '@/components/TagSelector';

interface SubmitFormProps {
  // Props can be added here if needed
}

export default function SubmitForm({}: SubmitFormProps) {
  const t = useTranslations('submit');
  const { data: session } = useSession();
  const router = useRouter();

  // 使用国际化的分类选项
  const categoryOptions = useCategoryOptions();

  const [formData, setFormData] = useState({
    name: '',
    tagline: '',
    description: '',
    websiteUrl: '',
    logoFile: null as File | null,
    category: '',
    tags: [] as string[],
    pricingModel: ''
  });

  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleLogoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        alert('Please upload a valid image file (JPEG, PNG, GIF, or WebP)');
        return;
      }

      // Validate file size (5MB)
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        alert('File size must be less than 5MB');
        return;
      }

      setFormData(prev => ({
        ...prev,
        logoFile: file
      }));

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleTagsChange = (selectedTags: string[]) => {
    setFormData(prev => ({
      ...prev,
      tags: selectedTags
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!session) {
      setIsLoginModalOpen(true);
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      const submitData = new FormData();
      submitData.append('name', formData.name);
      submitData.append('tagline', formData.tagline);
      submitData.append('description', formData.description);
      submitData.append('websiteUrl', formData.websiteUrl);
      submitData.append('category', formData.category);
      submitData.append('tags', JSON.stringify(formData.tags));
      submitData.append('pricingModel', formData.pricingModel);
      
      if (formData.logoFile) {
        submitData.append('logo', formData.logoFile);
      }

      const response = await fetch('/api/tools/submit', {
        method: 'POST',
        body: submitData,
      });

      if (response.ok) {
        setSubmitStatus('success');
        // Reset form
        setFormData({
          name: '',
          tagline: '',
          description: '',
          websiteUrl: '',
          logoFile: null,
          category: '',
          tags: [],
          pricingModel: ''
        });
        setLogoPreview(null);
        
        // Redirect to profile after a delay
        setTimeout(() => {
          router.push('/profile/submitted');
        }, 2000);
      } else {
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('Submit error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Fragment>
      {/* Status Messages */}
      {submitStatus === 'success' && (
        <SuccessMessage message={t('form.success_message')} />
      )}
      
      {submitStatus === 'error' && (
        <ErrorMessage message={t('form.error_message')} />
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Basic Information Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">{t('form.basic_info')}</h2>
          
          <div className="space-y-6">
            {/* Tool Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.tool_name')} *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder={t('form.tool_name_placeholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* Tagline */}
            <div>
              <label htmlFor="tagline" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.tagline')}
              </label>
              <input
                type="text"
                id="tagline"
                name="tagline"
                value={formData.tagline}
                onChange={handleInputChange}
                placeholder={t('form.tagline_placeholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Website URL */}
            <div>
              <label htmlFor="websiteUrl" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.website_url')} *
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LinkIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="url"
                  id="websiteUrl"
                  name="websiteUrl"
                  value={formData.websiteUrl}
                  onChange={handleInputChange}
                  placeholder={t('form.website_url_placeholder')}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.description')} *
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder={t('form.description_placeholder')}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* Logo Upload */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.logo_upload')}
              </label>
              <div className="flex items-start space-x-4">
                <div className="flex-1">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    <input
                      type="file"
                      id="logo"
                      accept="image/jpeg,image/png,image/gif,image/webp"
                      onChange={handleLogoUpload}
                      className="hidden"
                    />
                    <label htmlFor="logo" className="cursor-pointer">
                      <Upload className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="mt-2">
                        <span className="text-sm text-blue-600 hover:text-blue-500">
                          Click to upload
                        </span>
                        <p className="text-xs text-gray-500 mt-1">
                          {t('form.logo_upload_hint')}
                        </p>
                      </div>
                    </label>
                  </div>
                </div>
                
                {logoPreview && (
                  <div className="flex-shrink-0">
                    <div className="w-24 h-24 border border-gray-300 rounded-lg overflow-hidden">
                      <img
                        src={logoPreview}
                        alt={t('form.logo_preview')}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Category and Pricing Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">{t('form.category_and_pricing')}</h2>
          
          <div className="space-y-6">
            {/* Category */}
            <div>
              <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.category')} *
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">{t('form.category_placeholder')}</option>
                {categoryOptions.map((category) => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Tags */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.tags')}
              </label>
              <TagSelector
                selectedTags={formData.tags}
                onTagsChange={handleTagsChange}
                maxTags={MAX_TAGS_COUNT}
                placeholder={t('form.tags_placeholder')}
              />
            </div>

            {/* Pricing Model */}
            <div>
              <label htmlFor="pricingModel" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.pricing_model')} *
              </label>
              <select
                id="pricingModel"
                name="pricingModel"
                value={formData.pricingModel}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">{t('form.pricing_placeholder')}</option>
                {TOOL_PRICING_FORM_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {t(`form.${option.value}`)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* User Info Display */}
        {session && (
          <div className="mb-8 bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="text-sm font-medium text-green-800 mb-2">{t('form.submitter_info')}</h3>
            <p className="text-sm text-green-700">
              {t('form.submitter')}: {session.user?.name || session.user?.email}
            </p>
            <p className="text-sm text-green-700">
              {t('form.email')}: {session.user?.email}
            </p>
          </div>
        )}

        {/* Guidelines */}
        <div className="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <div className="flex items-start">
            <Info className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-medium text-blue-900 mb-2">{t('form.guidelines_title')}</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• {t('form.guideline_1')}</li>
                <li>• {t('form.guideline_2')}</li>
                <li>• {t('form.guideline_3')}</li>
                <li>• {t('form.guideline_4')}</li>
                <li>• {t('form.guideline_5')}</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-8 py-3 rounded-lg font-medium transition-colors ${
              isSubmitting
                ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" className="mr-2" />
                {t('form.submitting')}
              </div>
            ) : (
              t('form.submit_button')
            )}
          </button>
        </div>
      </form>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </Fragment>
  );
}
