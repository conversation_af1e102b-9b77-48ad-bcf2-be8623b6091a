'use client';

import { useState } from 'react';
import { usePathname } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import {
  useStripe,
  useElements,
  PaymentElement,
  AddressElement
} from '@stripe/react-stripe-js';
import { CreditCard, Loader2 } from 'lucide-react';
import { formatStripeAmount } from '@/constants/pricing';
import { Locale } from '@/i18n/config';

interface StripeCheckoutFormProps {
  onSuccess: () => void;
  amount: number;
}

export default function StripeCheckoutForm({ onSuccess, amount }: StripeCheckoutFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  const pathname = usePathname();
  const t = useTranslations('payment');

  // Extract current locale from pathname
  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsProcessing(true);
    setErrorMessage('');

    try {
      // 确认支付
      const { error } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/submit/success`,
        },
        redirect: 'if_required'
      });

      if (error) {
        // 支付失败
        if (error.type === 'card_error' || error.type === 'validation_error') {
          setErrorMessage(error.message || t('payment_failed'));
        } else {
          setErrorMessage(t('payment_error'));
        }
      } else {
        // 支付成功
        onSuccess();
      }
    } catch (err) {
      setErrorMessage(t('payment_processing_failed'));
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* 支付方式选择 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          {t('payment_method')}
        </label>
        <PaymentElement 
          options={{
            layout: 'tabs',
            defaultValues: {
              billingDetails: {
                address: {
                  country: 'CN'
                }
              }
            }
          }}
        />
      </div>

      {/* 账单地址 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          {t('billing_address')}
        </label>
        <AddressElement 
          options={{
            mode: 'billing',
            defaultValues: {
              address: {
                country: 'CN'
              }
            }
          }}
        />
      </div>

      {/* 错误信息 */}
      {errorMessage && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600 text-sm">{errorMessage}</p>
        </div>
      )}

      {/* 支付按钮 */}
      <button
        type="submit"
        disabled={!stripe || !elements || isProcessing}
        className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors"
      >
        {isProcessing ? (
          <>
            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
            {t('processing_payment')}
          </>
        ) : (
          <>
            <CreditCard className="h-5 w-5 mr-2" />
            {t('pay_now', { amount: formatStripeAmount(amount) })}
          </>
        )}
      </button>

      {/* 安全提示 */}
      <div className="text-center">
        <p className="text-gray-500 text-xs">
          {t('security_notice')}
        </p>
      </div>
    </form>
  );
}
