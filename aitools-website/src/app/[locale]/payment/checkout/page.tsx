import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import CheckoutClient from '@/app/[locale]/payment/checkout/CheckoutClient';
import { AlertCircle } from 'lucide-react';

interface PageProps {
  searchParams: Promise<{ orderId?: string }>;
}

async function getOrderData(orderId: string) {
  try {
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3001';
    const response = await fetch(`${baseUrl}/api/orders/${orderId}`, {
      cache: 'no-store'
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    return data.success ? data.data : null;
  } catch (error) {
    console.error('Failed to fetch order:', error);
    return null;
  }
}

export default async function CheckoutPage({ searchParams }: PageProps) {
  const session = await getServerSession(authOptions);
  const { orderId } = await searchParams;

  // 检查用户是否已登录
  if (!session) {
    redirect('/');
  }

  // 检查是否有订单ID
  if (!orderId) {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">订单不存在</h1>
          <p className="text-gray-600 mb-4">未找到有效的订单ID</p>
        </div>
      </div>
    );
  }

  // 获取订单数据
  const order = await getOrderData(orderId);

  if (!order) {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">订单不存在</h1>
          <p className="text-gray-600 mb-4">订单可能已被删除或不存在</p>
        </div>
      </div>
    );
  }

  // 检查订单状态
  if (order.status === 'completed') {
    redirect(`/submit/success?toolId=${order.toolId}`);
  }

  if (order.status !== 'pending') {
    return (
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">订单状态异常</h1>
          <p className="text-gray-600 mb-4">该订单无法进行支付</p>
        </div>
      </div>
    );
  }

  return <CheckoutClient order={order} orderId={orderId} />;
}

