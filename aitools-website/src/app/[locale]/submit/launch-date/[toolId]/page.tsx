import { getServerSession } from 'next-auth/next';
import { redirect } from '@/i18n/routing';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';
import { CheckCircle, AlertCircle } from 'lucide-react';
import { getTranslations } from 'next-intl/server';
import LaunchDateClient from './LaunchDateClient';

interface PageProps {
  params: Promise<{
    locale: string;
    toolId: string;
  }>;
}

async function getToolData(toolId: string, userEmail: string) {
  try {
    await dbConnect();

    // 获取用户信息
    const user = await User.findOne({ email: userEmail });
    if (!user) {
      return { tool: null, error: 'unauthorized_access' };
    }

    const tool = await Tool.findById(toolId).lean();

    if (!tool) {
      return { tool: null, error: 'tool_not_found' };
    }

    // 检查工具是否属于当前用户 - 比较用户ID而不是邮箱
    if (tool.submittedBy !== user._id.toString()) {
      return { tool: null, error: 'unauthorized_access' };
    }

    // 检查工具状态
    if (tool.status !== 'draft') {
      return { tool: null, error: 'launch_date_already_set' };
    }

    return {
      tool: {
        _id: tool._id.toString(),
        name: tool.name,
        description: tool.description,
        status: tool.status
      },
      error: null
    };
  } catch (error) {
    console.error('Failed to fetch tool:', error);
    return { tool: null, error: 'fetch_failed' };
  }
}

export default async function LaunchDatePage({ params }: PageProps) {
  const { locale, toolId } = await params;
  const t = await getTranslations('submit');

  // 检查用户认证
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    redirect('/');
  }

  // 获取工具数据
  const { tool, error } = await getToolData(toolId, session.user.email);

  if (error) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            出错了
          </h1>
          <p className="text-gray-600 mb-4">
            {t(`errors.${error}`)}
          </p>
          <a
            href="/profile/submitted"
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            {t('actions.back_to_submitted')}
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          工具信息提交成功！
        </h1>
        <p className="text-lg text-gray-600">
          现在请选择您的发布日期和选项
        </p>
      </div>

      {/* Tool Info */}
      {tool && (
        <div className="bg-gray-50 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {tool.name}
          </h2>
          <p className="text-gray-600">{tool.description}</p>
        </div>
      )}

      {/* Launch Date Selector */}
      {tool && (
        <LaunchDateClient
          toolId={toolId}
          locale={locale}
        />
      )}
    </div>
  );
}
